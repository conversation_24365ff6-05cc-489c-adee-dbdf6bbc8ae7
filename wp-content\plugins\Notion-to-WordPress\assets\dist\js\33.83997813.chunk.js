/*! For license information please see 33.83997813.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[33],{5033:(e,t,n)=>{n.r(t),n.d(t,{LogsModule:()=>j,default:()=>L});n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3418),n(4423),n(3792),n(8598),n(2062),n(4782),n(6910),n(9572),n(2010),n(2892),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(3362),n(825),n(7495),n(8781),n(1699),n(7764),n(5440),n(5746),n(3500),n(2953),n(3296),n(7208),n(8408);var r=n(9223),o=n(6919),i=n(7232),a=n(8055);function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function c(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function u(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var c=r&&r.prototype instanceof s?r:s,l=Object.create(c.prototype);return f(l,"_invoke",function(n,r,o){var i,s,c,l=0,u=o||[],f=!1,h={p:0,n:0,v:e,a:v,f:v.bind(e,4),d:function(t,n){return i=t,s=0,c=e,h.n=n,a}};function v(n,r){for(s=n,c=r,t=0;!f&&l&&!o&&t<u.length;t++){var o,i=u[t],v=h.p,d=i[2];n>3?(o=d===r)&&(c=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=v&&((o=n<2&&v<i[1])?(s=0,h.v=r,h.n=i[1]):v<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,u,d){if(l>1)throw TypeError("Generator is already running");for(f&&1===u&&v(u,d),s=u,c=d;(t=s<2?e:c)||!f;){i||(s?s<3?(s>1&&(h.n=-1),v(s,c)):h.n=c:h.v=c);try{if(l=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,c)))throw TypeError("iterator result is not an object");if(!t.done)return t;c=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(c=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?c:n.call(r,h))!==a)break}catch(t){i=e,s=1,c=t}finally{l=1}}return{value:t,done:f}}}(n,o,i),!0),l}var a={};function s(){}function c(){}function l(){}t=Object.getPrototypeOf;var h=[][r]?t(t([][r]())):(f(t={},r,function(){return this}),t),v=l.prototype=s.prototype=Object.create(h);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,l):(e.__proto__=l,f(e,o,"GeneratorFunction")),e.prototype=Object.create(v),e}return c.prototype=l,f(v,"constructor",l),f(l,"constructor",c),c.displayName="GeneratorFunction",f(l,o,"GeneratorFunction"),f(v),f(v,o,"Generator"),f(v,r,function(){return this}),f(v,"toString",function(){return"[object Generator]"}),(u=function(){return{w:i,m:d}})()}function f(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}f=function(e,t,n,r){function i(t,n){f(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},f(e,t,n,r)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach(function(t){O(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function d(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function p(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){d(i,r,o,a,s,"next",e)}function s(e){d(i,r,o,a,s,"throw",e)}a(void 0)})}}function g(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,k(r.key),r)}}function y(e,t,n){return t=m(t),function(e,t){if(t&&("object"==s(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,b()?Reflect.construct(t,n||[],m(e).constructor):t.apply(e,n))}function b(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(b=function(){return!!e})()}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function w(e,t){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},w(e,t)}function O(e,t,n){return(t=k(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}var j=function(e){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return O(e=y(this,t,[].concat(r)),"logs",[]),O(e,"filteredLogs",[]),O(e,"currentFilter",{}),O(e,"autoRefresh",!1),O(e,"refreshTimer",null),O(e,"refreshInterval",1e4),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&w(e,t)}(t,e),n=t,r=[{key:"onInit",value:function(){}},{key:"onMount",value:function(){this.loadLogs(),this.setupAutoRefresh()}},{key:"onUnmount",value:function(){this.stopAutoRefresh()}},{key:"onDestroy",value:function(){this.stopAutoRefresh()}},{key:"onRender",value:function(){this.renderLogs()}},{key:"bindEvents",value:function(){var e=this.$("#refresh-logs");e&&this.addEventListener(e,"click",this.handleRefresh.bind(this));var t=this.$("#clear-logs");t&&this.addEventListener(t,"click",this.handleClear.bind(this));var n=this.$("#export-logs");n&&this.addEventListener(n,"click",this.handleExport.bind(this));var r=this.$("#auto-refresh");r&&this.addEventListener(r,"change",this.handleAutoRefreshToggle.bind(this)),this.bindFilterEvents()}},{key:"onStateChange",value:function(e,t,n){}},{key:"bindFilterEvents",value:function(){var e=this;this.$$(".log-filter").forEach(function(t){e.addEventListener(t,"change",e.handleFilterChange.bind(e))});var t,n=this.$("#log-search");n&&this.addEventListener(n,"input",function(){clearTimeout(t),t=setTimeout(function(){e.handleFilterChange()},300)})}},{key:"loadLogs",value:(d=p(u().m(function e(){var t,n;return u().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,i.bE)("notion_to_wordpress_get_logs",v({limit:1e3},this.currentFilter));case 1:if(!(t=e.v).data.success){e.n=2;break}this.logs=t.data.data,this.applyFilters(),this.render(),e.n=3;break;case 2:throw new Error(t.data.message||"加载日志失败");case 3:e.n=5;break;case 4:e.p=4,n=e.v,(0,o.Qg)("加载日志失败: ".concat(n.message));case 5:return e.a(2)}},e,this,[[0,4]])})),function(){return d.apply(this,arguments)})},{key:"renderLogs",value:function(){var e=this,t=this.$("#logs-container");if(t)if(0!==this.filteredLogs.length){var n=this.filteredLogs.map(function(t){return e.renderLogEntry(t)}).join("");t.innerHTML='<div class="logs-list">'.concat(n,"</div>"),this.updateStats()}else t.innerHTML='<div class="no-logs">没有找到日志记录</div>'}},{key:"renderLogEntry",value:function(e){var t=(0,a.Az)(Date.now()-e.timestamp),n=e.context?'<div class="log-context">'.concat(JSON.stringify(e.context,null,2),"</div>"):"";return'\n      <div class="log-entry log-'.concat(e.level,'" data-log-id="').concat(e.id,'">\n        <div class="log-header">\n          <span class="log-level">').concat(e.level.toUpperCase(),'</span>\n          <span class="log-source">').concat(e.source,'</span>\n          <span class="log-time" title="').concat(new Date(e.timestamp).toLocaleString(),'">\n            ').concat(t,'前\n          </span>\n        </div>\n        <div class="log-message">').concat(this.escapeHtml(e.message),"</div>\n        ").concat(n,"\n      </div>\n    ")}},{key:"escapeHtml",value:function(e){var t=document.createElement("div");return t.textContent=e,t.innerHTML}},{key:"applyFilters",value:function(){var e=this;this.filteredLogs=this.logs.filter(function(t){if(e.currentFilter.level&&t.level!==e.currentFilter.level)return!1;if(e.currentFilter.source&&t.source!==e.currentFilter.source)return!1;if(e.currentFilter.dateFrom){var n=new Date(e.currentFilter.dateFrom).getTime();if(t.timestamp<n)return!1}if(e.currentFilter.dateTo){var r=new Date(e.currentFilter.dateTo).getTime()+864e5;if(t.timestamp>r)return!1}if(e.currentFilter.search){var o=e.currentFilter.search.toLowerCase();if(!"".concat(t.message," ").concat(t.source).toLowerCase().includes(o))return!1}return!0}),this.filteredLogs.sort(function(e,t){return t.timestamp-e.timestamp})}},{key:"updateStats",value:function(){var e=this.$("#logs-stats");if(e){var t={total:this.logs.length,filtered:this.filteredLogs.length,error:this.filteredLogs.filter(function(e){return"error"===e.level}).length,warning:this.filteredLogs.filter(function(e){return"warning"===e.level}).length,info:this.filteredLogs.filter(function(e){return"info"===e.level}).length,debug:this.filteredLogs.filter(function(e){return"debug"===e.level}).length};e.innerHTML='\n      <div class="stats-item">\n        <span class="stats-label">总计:</span>\n        <span class="stats-value">'.concat(t.total,'</span>\n      </div>\n      <div class="stats-item">\n        <span class="stats-label">显示:</span>\n        <span class="stats-value">').concat(t.filtered,'</span>\n      </div>\n      <div class="stats-item error">\n        <span class="stats-label">错误:</span>\n        <span class="stats-value">').concat(t.error,'</span>\n      </div>\n      <div class="stats-item warning">\n        <span class="stats-label">警告:</span>\n        <span class="stats-value">').concat(t.warning,'</span>\n      </div>\n      <div class="stats-item info">\n        <span class="stats-label">信息:</span>\n        <span class="stats-value">').concat(t.info,'</span>\n      </div>\n      <div class="stats-item debug">\n        <span class="stats-label">调试:</span>\n        <span class="stats-value">').concat(t.debug,"</span>\n      </div>\n    ")}}},{key:"handleRefresh",value:(h=p(u().m(function e(t){var n,r;return u().w(function(e){for(;;)switch(e.p=e.n){case 0:return t.preventDefault(),n=t.target,r=n.textContent,n.disabled=!0,n.textContent="刷新中...",e.p=1,e.n=2,this.loadLogs();case 2:(0,o.Te)("日志已刷新");case 3:return e.p=3,n.disabled=!1,n.textContent=r,e.f(3);case 4:return e.a(2)}},e,this,[[1,,3,4]])})),function(e){return h.apply(this,arguments)})},{key:"handleClear",value:(f=p(u().m(function e(t){var n,r;return u().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t.preventDefault(),confirm("确定要清空所有日志吗？此操作不可撤销。")){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,(0,i.bE)("notion_to_wordpress_clear_logs",{});case 2:if(!(n=e.v).data.success){e.n=3;break}this.logs=[],this.filteredLogs=[],this.render(),(0,o.Te)("日志已清空"),e.n=4;break;case 3:throw new Error(n.data.message||"清空日志失败");case 4:e.n=6;break;case 5:e.p=5,r=e.v,(0,o.Qg)("清空日志失败: ".concat(r.message));case 6:return e.a(2)}},e,this,[[1,5]])})),function(e){return f.apply(this,arguments)})},{key:"handleExport",value:(l=p(u().m(function e(t){var n,r,i,a;return u().w(function(e){for(;;)switch(e.n){case 0:t.preventDefault();try{n=this.generateCSV(),r=new Blob([n],{type:"text/csv;charset=utf-8;"}),void 0!==(i=document.createElement("a")).download&&(a=URL.createObjectURL(r),i.setAttribute("href",a),i.setAttribute("download","notion-wp-logs-".concat((new Date).toISOString().split("T")[0],".csv")),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),(0,o.Te)("日志已导出"))}catch(e){(0,o.Qg)("导出日志失败: ".concat(e.message))}case 1:return e.a(2)}},e,this)})),function(e){return l.apply(this,arguments)})},{key:"generateCSV",value:function(){var e=this.filteredLogs.map(function(e){return[new Date(e.timestamp).toISOString(),e.level,e.source,'"'.concat(e.message.replace(/"/g,'""'),'"'),e.context?'"'.concat(JSON.stringify(e.context).replace(/"/g,'""'),'"'):""]});return[["时间","级别","来源","消息","上下文"]].concat(c(e)).map(function(e){return e.join(",")}).join("\n")}},{key:"handleAutoRefreshToggle",value:function(e){var t=e.target;this.autoRefresh=t.checked,this.autoRefresh?this.startAutoRefresh():this.stopAutoRefresh()}},{key:"handleFilterChange",value:function(){var e,t,n,r,o,i=this;this.currentFilter={level:(null===(e=this.$("#filter-level"))||void 0===e?void 0:e.value)||void 0,source:(null===(t=this.$("#filter-source"))||void 0===t?void 0:t.value)||void 0,dateFrom:(null===(n=this.$("#filter-date-from"))||void 0===n?void 0:n.value)||void 0,dateTo:(null===(r=this.$("#filter-date-to"))||void 0===r?void 0:r.value)||void 0,search:(null===(o=this.$("#log-search"))||void 0===o?void 0:o.value)||void 0},Object.keys(this.currentFilter).forEach(function(e){i.currentFilter[e]||delete i.currentFilter[e]}),this.applyFilters(),this.render()}},{key:"setupAutoRefresh",value:function(){var e=this.$("#auto-refresh");e&&e.checked&&(this.autoRefresh=!0,this.startAutoRefresh())}},{key:"startAutoRefresh",value:function(){var e=this;this.refreshTimer&&clearInterval(this.refreshTimer),this.refreshTimer=setInterval(function(){e.loadLogs().catch(console.error)},this.refreshInterval)}},{key:"stopAutoRefresh",value:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)}},{key:"getLogs",value:function(){return this.logs}},{key:"getFilteredLogs",value:function(){return this.filteredLogs}}],r&&g(n.prototype,r),s&&g(n,s),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,s,l,f,h,d}(r.$);function L(e){return new j({element:e,selector:e?void 0:"#logs-container"})}}}]);
//# sourceMappingURL=33.83997813.chunk.js.map