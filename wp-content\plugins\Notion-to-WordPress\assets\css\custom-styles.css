/**
 * 插件前端自定义样式
 *
 * 为 Notion to WordPress 插件提供前端页面的自定义样式，包括数据库视图、区块元素和整体布局。
 *
 * @since      1.0.0
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/<PERSON>-<PERSON><PERSON>/Notion-to-WordPress
 */

#notion-to-wordpress-plugin-admin {
    padding: 5px 20px;
}


#notion-to-wordpress-plugin-admin .widefat {
    width: 100%; /* Makes the text fields use full width */
    max-width: 900px; /* Set a maximum width if desired */
    min-height: 40px; /* Adjust height if necessary */
    padding: 10px; /* Add some padding */
}


#notion-to-wordpress-plugin-admin .widetable {
    max-width: 100%;
}

#notion-to-wordpress-plugin-admin .red-text {
    color: red;
}

#notion-to-wordpress-plugin-admin #loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: none;
}

#notion-to-wordpress-plugin-admin .loading-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 18px;
    font-weight: bold;
}


.notion-to-wordpress-header {
    background-color: #2e65a8; /* Dark blue background */
    color: white;
    width: 100%;
    position: sticky;
    top: 32px;
    left: 0;
    margin: 0;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.notion-to-wordpress-header-inner {
    max-width: 100%; /* Ensure it spans the full content width */
    padding: 5px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notion-to-wordpress-logo {
    height: 50px; /* Adjust to fit desired size */
    max-height: 100%; /* Prevent overflowing */
    width: auto; /* Maintain aspect ratio */
    margin-right: 20px; /* Spacing between logo and title */
}


.notion-to-wordpress-title {
    font-size: 24px;
    margin: 0;
    font-weight: bold;
    color: white;


}

.notion-to-wordpress-nav  {
    display: flex;
    justify-content: flex-start; /* Aligns the nav items to the left */
    flex-grow: 1; /* Allows the nav to expand and take available space */
    margin-left: 20px;

}


.notion-to-wordpress-nav a {
    margin-left: 15px;
    text-decoration: none;
    color: #ffffff; /* White text for links */
    font-weight: 500;
}
.notion-to-wordpress-nav a:hover {
    text-decoration: underline;
}
.notion-to-wordpress-nav a.active {
    font-weight: bold;
    color: #ffcc00; /* Highlighted tab color */
}
.notion-to-wordpress-body {
    margin: 0;
    padding: 20px;
    background: #ffffff;
}


/* Remove extra margin between admin menu and plugin header */
#wpcontent {
    padding-left: 0 !important;
}
.notion-to-wordpress-header {
    margin-left: 0 !important;
}

/* ================ Notion 内容渲染样式 ================ */

/* 通用容器样式 */
.notion-page-content {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    line-height: 1.5;
    color: #37352f;
    margin: 0 auto;
    max-width: 100%;
}

/* 标题样式 */
.notion-page-content h1 {
    font-size: 2em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.notion-page-content h2 {
    font-size: 1.5em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.notion-page-content h3 {
    font-size: 1.25em;
    margin: 1em 0 0.5em;
    font-weight: 600;
}

/* 段落和列表样式*/
.notion-page-content p,
.notion-page-content li {
    margin-bottom: 0.5em;
}

.notion-page-content ul,
.notion-page-content ol {
    margin: 0.5em 0 1em;
    padding-left: 1.5em;
}

/* 待办事项样式 */
.notion-page-content input[type="checkbox"] {
    margin-right: 0.5em;
    position: relative;
    top: -1px;
}

/* 图片显示样式优化 */
.wp-block-image {
    margin: 1.5em 0;
    max-width: 100%;
}

.wp-block-image img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.notion-image-caption {
    text-align: center;
    font-size: 0.9em;
    color: #555;
    margin-top: 0.5em;
}

/* ================ 分栏布局样式 ================ */

/* 分栏容器 */
.notion-column-list {
    display: flex !important;
    gap: 16px;
    margin: 1em 0;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
}

/* 分栏列 */
.notion-column {
    flex: 1 1 0; /* 默认等宽分布，内联样式会覆盖此设置 */
    min-width: 0;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    box-sizing: border-box;
}

/* 分栏内容样式优化 */
.notion-column > * {
    margin-top: 0;
}

.notion-column > *:last-child {
    margin-bottom: 0;
}

/* 表格样式 */
.notion-page-content table {
    border-collapse: collapse;
    margin: 1.5em 0;
    width: 100%;
    overflow-x: auto;
    display: block;
}

.notion-page-content table td,
.notion-page-content table th {
    border: 1px solid #e1e1e1;
    padding: 8px 12px;
}

.notion-page-content table tr:nth-child(even) {
    background-color: #f9f9f9;
}

/* 公式显示样式 */
.notion-equation {
    text-align: center;
    margin: 1.5em 0;
    overflow-x: auto;
    max-width: 100%;
    font-size: 1.1em;
    padding: 0.5em 0;
}

.notion-inline-equation {
    display: inline-block;
    vertical-align: middle;
    font-size: 1.05em;
}

/* 确保行内公式不会被截断*/
.mjx-chtml {
    overflow-x: auto;
    overflow-y: hidden;
    max-width: 100%;
}

/* 块级公式样式 */
.mjx-chtml.MJXc-display {
    margin: 1em 0;
    padding: 0.5em 0;
    overflow-x: auto;
    overflow-y: hidden;
    max-width: 100%;
}

/* Mermaid 图表样式 - GitHub风格设计 */
.mermaid {
    position: relative;
    text-align: center;
    margin: 1.5em auto;
    max-width: 100%; /* 适应页面容器宽度 */
    background: #ffffff;
    border: 1px solid #d0d7de;
    border-radius: 6px;
    padding: 16px;
    overflow: hidden; /* 隐藏溢出，工具栏在内部右下角 */
    display: block;
    width: auto;
    box-shadow: 0 1px 3px rgba(27, 31, 36, 0.12);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
    min-height: 100px; /* 确保有足够空间显示工具栏 */
}

/* 移除旧的 pre.mermaid 样式，现在统一使用 div.mermaid */

/* Mermaid 渲染后的 SVG 样式 */
.mermaid > svg {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

/* 调整 Mermaid 节点与文本默认字号 */
.mermaid svg text {
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
}

/* SVG 变换样式 - 移除冲突的 !important 规则 */
.mermaid svg {
    transition: transform 0.2s ease;
    transform-origin: center center;
    cursor: grab;
}

.mermaid svg:active {
    cursor: grabbing;
}

/* ===== Mermaid GitHub风格控制按钮样式 ===== */
.mermaid-controls {
    position: absolute;
    bottom: 6px; /* GitHub风格：右下角位置 */
    right: 6px;
    z-index: 100;
    background: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(27, 31, 36, 0.15);
    border-radius: 4px;
    padding: 2px;
    box-shadow: 0 1px 2px rgba(27, 31, 36, 0.08);
    backdrop-filter: blur(2px);
    opacity: 0.7;
    transition: all 0.15s ease;
    font-size: 0; /* 移除按钮间隙 */
}

.mermaid-controls:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.95);
}

.mermaid-zoom-controls {
    display: flex;
    gap: 0; /* 紧密排列 */
}

.mermaid-btn {
    width: 22px;
    height: 22px;
    border: none;
    background: transparent;
    color: #656d76;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    font-weight: normal;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.12s ease;
    user-select: none;
    position: relative;
    margin: 0;
    padding: 0;
}

.mermaid-btn:hover {
    background: #f3f4f6;
    color: #24292f;
}

.mermaid-btn:active {
    background: #e5e7ea;
    transform: scale(0.96);
}

.mermaid-btn:focus {
    outline: 2px solid #0366d6;
    outline-offset: -2px;
}

/* 按钮工具提示 - 精致版（上方显示） */
.mermaid-btn::after {
    content: attr(title);
    position: absolute;
    top: -26px; /* 在按钮上方显示 */
    left: 50%;
    transform: translateX(-50%);
    background: rgba(36, 41, 46, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.15s ease;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.mermaid-btn:hover::after {
    opacity: 1;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 768px) {
    .mermaid {
        margin: 1em 0;
        padding: 12px;
        border-radius: 4px;
    }

    .mermaid-controls {
        bottom: 4px;
        right: 4px;
        padding: 2px;
    }

    .mermaid-btn {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .mermaid-btn::after {
        font-size: 10px;
        top: -24px; /* 移动端也在上方显示 */
        bottom: auto;
    }
}

/* GitHub风格的容器适配 */
.mermaid {
    /* 确保不超出父容器 */
    box-sizing: border-box;
    max-width: 650px;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .mermaid {
        background: #0d1117;
        border-color: #30363d;
    }

    .mermaid-controls {
        background: rgba(33, 38, 45, 0.9);
        border-color: #30363d;
        backdrop-filter: blur(4px);
    }

    .mermaid-btn {
        color: #8b949e;
    }

    .mermaid-btn:hover {
        background: #30363d;
        color: #f0f6fc;
    }

    .mermaid-btn:active {
        background: #282e33;
    }

    .mermaid-btn::after {
        background: #f0f6fc;
        color: #24292e;
    }
}

/* 全屏模式样式 */
.mermaid-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    margin: 0 !important;
    border: none !important;
    border-radius: 0 !important;
    background: #ffffff !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 全屏模式下保持工具栏在右下角，稍微大一些 */
.mermaid-fullscreen .mermaid-controls {
    bottom: 20px !important;
    right: 20px !important;
    top: auto !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(27, 31, 36, 0.15) !important;
    border-radius: 6px !important;
    padding: 4px !important;
    box-shadow: 0 2px 8px rgba(27, 31, 36, 0.15) !important;
}

/* 全屏模式下的按钮稍微大一些 */
.mermaid-fullscreen .mermaid-btn {
    width: 28px !important;
    height: 28px !important;
    font-size: 14px !important;
    border-radius: 3px !important;
}

/* 全屏模式下的工具提示调整 */
.mermaid-fullscreen .mermaid-btn::after {
    top: -28px !important;
    font-size: 11px !important;
    padding: 3px 7px !important;
}

/* 深色模式下的全屏样式 */
@media (prefers-color-scheme: dark) {
    .mermaid-fullscreen {
        background: #0d1117 !important;
    }

    .mermaid-fullscreen .mermaid-controls {
        background: rgba(33, 38, 45, 0.9) !important;
        border-color: #30363d !important;
        padding: 4px !important;
        border-radius: 6px !important;
    }
}

/* Mermaid 响应式样式 */

/* 平板端样式 (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .mermaid {
        max-width: 90%; /* 平板端使用90%宽度 */
    }
}

/* 移动端样式 (≤768px) */
@media (max-width: 768px) {
    .mermaid {
        margin: 1.5em auto;
        padding: 12px;
        font-size: 14px;
        max-width: 100%; /* 移动端使用全宽 */
    }

    .mermaid svg text {
        font-size: 12px;
    }

    .mermaid-controls {
        top: 4px;
        right: 4px;
        padding: 2px;
    }

    .mermaid-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .mermaid {
        margin: 1em auto;
        padding: 8px;
        font-size: 12px;
    }

    .mermaid svg text {
        font-size: 10px;
    }

    .mermaid-btn {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .mermaid-btn::after {
        display: none; /* 在小屏幕上隐藏工具提示 */
    }
}

/* 化学方程式样式*/
.notion-chem-equation {
    display: inline-block;
    vertical-align: middle;
    font-size: 1.05em;
    margin: 0 2px;
}

/* 代码块样式优化*/
.wp-block-code {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1em;
    overflow-x: auto;
    margin: 1.5em 0;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 0.9em;
}

.wp-block-code code {
    font-family: monospace;
    white-space: pre;
    tab-size: 2;
}

/* 语法高亮支持 */
.wp-block-code .language-javascript,
.wp-block-code .language-python,
.wp-block-code .language-css,
.wp-block-code .language-html,
.wp-block-code .language-json {
    display: block;
}

/* 内联代码 */
code {
    background: #f0f0f0;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

/* 引用*/
blockquote {
    border-left: 4px solid #ddd;
    padding-left: 1em;
    margin-left: 0;
    color: #555;
}

/* 分割线*/
.notion-page-content hr {
    border: none;
    border-top: 1px solid #e1e1e1;
    margin: 2em 0;
}

/* Toggle 折叠*/
.notion-toggle {
    margin: 1em 0;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
}

.notion-toggle summary {
    padding: 10px;
    cursor: pointer;
    background-color: #f9f9f9;
    font-weight: 500;
}

.notion-toggle summary:hover {
    background-color: #f0f0f0;
}

.notion-toggle[open] > summary {
    border-bottom: 1px solid #f0f0f0;
}

.notion-toggle > *:not(summary) {
    padding: 10px;
}

/* Notion Callout 样式 */
.notion-callout {
    padding: 16px;
    border-radius: 3px;
    border-left: 4px solid #ddd;
    background-color: #f8f9fa;
    margin: 1em 0;
    display: flex;
    align-items: flex-start;
}

.notion-callout-icon {
    margin-right: 8px;
    font-size: 1.2em;
    flex-shrink: 0;
}

.notion-callout-content {
    flex: 1;
    min-width: 0;
}

/* 标注块内的子内容样式 */
.notion-callout-content > *:first-child {
    margin-top: 0;
}

.notion-callout-content > *:last-child {
    margin-bottom: 0;
}

/* 嵌入内容（如视频）样式*/
.notion-page-content iframe {
    max-width: 100%;
    margin: 1em 0;
    border: none;
}

/* 视频容器保持宽高*/
.wp-block-zibllblock-iframe,
.wp-block-zibllblock-dplayer {
    position: relative;
    width: 100%;
    margin: 1.5em 0;
}

.wp-block-zibllblock-iframe > div,
.wp-block-zibllblock-dplayer > div {
    position: relative;
    width: 100%;
}

.wp-block-zibllblock-iframe iframe,
.wp-block-zibllblock-dplayer iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 文件下载样式 */
.file-download-box {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin: 1.5em 0;
    background-color: #f9f9f9;
}

.file-download-name {
    font-weight: 500;
    margin-right: 10px;
}

.file-download-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-color: #4a89dc;
    border-radius: 4px;
    position: relative;
}

.file-download-icon:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background-color: white;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
    mask-size: cover;
    -webkit-mask-size: cover;
}

.file-download-btn {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 4px;
    background-color: #4a89dc;
    color: white !important;
    text-decoration: none;
    font-size: 0.9em;
}

.file-download-btn:hover {
    background-color: #3a7bd5;
}

/* 链接样式 */
.notion-page-content a {
    color: #0366d6;
    text-decoration: none;
}

.notion-page-content a:hover {
    text-decoration: underline;
}

/* 书签样式 */
.notion-bookmark {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 12px;
    margin: 1em 0;
    background-color: #f9f9f9;
}

/* 字体颜色支持 */
.notion-page-content span[style*="color:"] {
    display: inline;
}

.notion-page-content span[style*="background-color:"] {
    padding: 0 3px;
}

/* 音频播放器样式*/
.notion-page-content audio {
    width: 100%;
    margin: 1em 0;
}

/* PDF 嵌入样式 */
.notion-page-content iframe[src$=".pdf"] {
    width: 100%;
    height: 600px;
    border: 1px solid #e1e1e1;
}

/* 表格内容区块 */
.notion-toc-placeholder {
    background-color: #f9f9f9;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin: 1.5em 0;
}

/* 移动设备响应式样式*/
@media (max-width: 768px) {
    .notion-page-content iframe,
    .notion-page-content iframe[src$=".pdf"] {
        height: 400px;
    }
    
    .notion-page-content table {
        font-size: 0.9em;
    }
    
    .notion-callout {
        padding: 12px;
        margin: 0.8em 0;
    }
    
    .notion-callout-icon {
        margin-right: 8px;
        font-size: 1.1em;
    }
    
    .notion-callout-content {
        width: 100%;
    }

    /* 分栏响应式：移动端变为单列 */
    .notion-column-list {
        flex-direction: column;
        gap: 0;
    }

    .notion-column {
        margin-bottom: 1em;
    }

    .notion-column:last-child {
        margin-bottom: 0;
    }

    /* 移动端统一边距调整 */
    .notion-page-content h1,
    .notion-page-content h2,
    .notion-page-content h3 {
        margin: 0.8em 0 0.4em;
    }

    .notion-page-content table,
    .notion-image,
    .file-download-box,
    .notion-toc-placeholder {
        margin: 1.2em 0;
    }

    .notion-equation,
    .notion-equation-block {
        margin: 1.2em 0;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .notion-page-content {
        color: #e1e1e1;
    }
    
    .notion-page-content code,
    .wp-block-code {
        background-color: #2d2d2d;
        color: #e1e1e1;
        border-color: #444;
    }
    
    .notion-page-content table tr:nth-child(even) {
        background-color: #2a2a2a;
    }
    
    .notion-page-content table td,
    .notion-page-content table th {
        border-color: #444;
    }
    
    .notion-toggle,
    .notion-toggle summary,
    .notion-callout,
    .file-download-box,
    .notion-bookmark,
    .notion-toc-placeholder {
        background-color: #2a2a2a;
        border-color: #444;
    }
    
    blockquote {
        border-left-color: #555;
        color: #aaa;
    }
}

/* Notion颜色样式支持 */
/* 文本颜色 */
.notion-color-gray { color: #787774 !important; }
.notion-color-brown { color: #9f6b53 !important; }
.notion-color-orange { color: #d9730d !important; }
.notion-color-yellow { color: #cb912f !important; }
.notion-color-green { color: #448361 !important; }
.notion-color-blue { color: #337ea9 !important; }
.notion-color-purple { color: #9065b0 !important; }
.notion-color-pink { color: #c14c8a !important; }
.notion-color-red { color: #d44c47 !important; }

/* 背景颜色 */
.notion-color-gray_background { background-color: #f1f1ef !important; }
.notion-color-brown_background { background-color: #f9e9e3 !important; }
.notion-color-orange_background { background-color: #faebdd !important; }
.notion-color-yellow_background { background-color: #fbf3db !important; }
.notion-color-green_background { background-color: #edf3ec !important; }
.notion-color-blue_background { background-color: #e6f1f8 !important; }
.notion-color-purple_background { background-color: #f4eefe !important; }
.notion-color-pink_background { background-color: #fbe4ee !important; }
.notion-color-red_background { background-color: #fdebec !important; }

/* 图片容器优化 */
.notion-image {
    margin: 1.5em 0;
    text-align: center;
}

.notion-image img {
    max-width: 100%;
    height: auto;
    display: inline-block;
}

.notion-image figcaption {
    text-align: center;
    font-size: 0.9em;
    color: #555;
    margin-top: 0.5em;
}

/* 公式渲染优化 - 已合并到上方的.notion-equation样式中 */

.notion-equation-fallback {
    max-width: 100%;
    height: auto;
    display: inline-block;
}

/* 图片错误处理 */
.notion-image-error {
    border: 1px dashed #ddd;
    border-radius: 4px;
    padding: 1em;
    text-align: center;
    margin: 1.5em 0;
}

.notion-image-placeholder {
    color: #888;
    font-style: italic;
    padding: 2em 0;
}

/* 数学公式和图表样式 */
.math-display {
    display: block;
    margin: 1em 0;
    text-align: center;
    overflow-x: auto;
    max-width: 100%;
}

.math-inline {
    display: inline-block;
    vertical-align: middle;
    margin: 0 0.2em;
}

/* 针对某些主题可能的样式冲突进行修复 */
.katex-display {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0.5em 0;
}

/* 解决某些主题中的公式溢出问题 */
.katex-display > .katex {
    max-width: 100%;
}

.katex-display > .katex > .katex-html {
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-right: 1em;
}



/* 公式错误显示样式 */
.katex-error {
    color: #d32f2f;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
    border-radius: 4px;
    padding: 0.2em 0.4em;
    font-family: monospace;
    font-size: 0.9em;
    display: inline-block;
    margin: 0.1em;
}

/* KaTeX标签重叠修复和样式优化 - 适用于旧版本类名 */
.notion-equation-block .katex-display {
    position: relative;
    padding-right: 4em; /* 为标签预留更多空间 */
}

.notion-equation-block .katex-display .tag {
    position: absolute;
    right: 0.5em;
    top: 50%;
    transform: translateY(-50%);
    white-space: nowrap;
    z-index: 1;

    /* 标签样式优化 */
    background-color: rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 6px !important;
    padding: 0.3em 0.6em !important;
    font-size: 0.9em !important;
    color: #666 !important;
    font-family: inherit !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

.notion-equation-block .katex-display .tag:hover {
    background-color: rgba(0, 0, 0, 0.08) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    color: #333 !important;
}

/* 标签内部元素样式重置 */
.notion-equation-block .katex-display .tag .strut {
    display: none !important; /* 隐藏KaTeX的strut元素 */
}

.notion-equation-block .katex-display .tag .mord {
    color: inherit !important;
    font-family: inherit !important;
    font-size: inherit !important;
}

.notion-equation-block .katex-display .tag .mord.text {
    font-family: inherit !important;
}

/* 确保公式主体不会与标签重叠 */
.notion-equation-block .katex-display .katex-html {
    max-width: calc(100% - 5em);
    display: inline-block;
}

/* 响应式设计 - 小屏幕上的标签处理 */
@media (max-width: 768px) {
    .notion-equation-block .katex-display {
        padding-right: 2em;
    }

    .notion-equation-block .katex-display .tag {
        font-size: 0.8em !important;
        padding: 0.2em 0.4em !important;
        right: 0.2em;
    }

    .notion-equation-block .katex-display .katex-html {
        max-width: calc(100% - 3em);
    }
}

/* 向后兼容性样式 - 确保旧类名也能正常工作 */
.notion-equation-inline {
    display: inline-block;
    vertical-align: middle;
    margin: 0 0.2em;
}

.notion-equation-block {
    display: block;
    margin: 1.5em 0;
    text-align: center;
    overflow-x: auto;
    max-width: 100%;
}

/* PDF预览样式 */
.notion-pdf {
    margin: 20px 0;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
}

.pdf-preview-container {
    background: #f5f5f5;
    min-height: 600px;
    position: relative;
}

.pdf-preview-container object,
.pdf-preview-container embed {
    width: 100%;
    height: 600px;
    border: none;
}

.pdf-actions {
    padding: 15px;
    background: #f9f9f9;
    border-top: 1px solid #e1e1e1;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pdf-download-btn, .pdf-open-btn {
    background: #0073aa;
    color: white !important;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.pdf-open-btn {
    background: #666;
}

.pdf-download-btn:hover {
    background: #005a87 !important;
    color: white !important;
}

.pdf-open-btn:hover {
    background: #444 !important;
    color: white !important;
}

.notion-pdf-caption {
    padding: 10px 15px;
    margin: 0;
    font-style: italic;
    color: #666;
    background: #f9f9f9;
    border-top: 1px solid #e1e1e1;
    font-size: 0.9em;
}

/* ===== KaTeX 标签简化样式（去除背景框及抖动） ===== */
.katex-display .tag,
.notion-equation-block .katex-display .tag {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
    color: inherit !important;
    font-weight: normal !important;
    transform: translateY(-50%) !important; /* 保持垂直居中，不缩放 */
    transition: none !important;
}

.katex-display .tag:hover,
.notion-equation-block .katex-display .tag:hover {
    background: none !important;
    border: none !important;
    transform: translateY(-50%) !important; /* 取消缩放避免引发滚动条抖动 */
}

/* ================ 数据库区块样式 ================ */
/* 注意：数据库渲染器的详细样式已移至 notion-database.css 文件 */

/* CSS变量定义 - 保留用于其他组件 */
:root {
    --notion-database-bg: #ffffff;
    --notion-database-border: #e9ecef;
    --notion-gray-05: #f8f9fa;
    --notion-gray-40: #d1d5db;
    --notion-white: #ffffff;
    --notion-border-radius: 8px;
    --notion-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    --notion-table-border: #e9ecef;
    --notion-table-header-bg: #f8f9fa;
    --ntw-header-offset: 0px;
}

/* 数据库区块主容器 - 基础样式，详细样式在 notion-database.css */
.notion-child-database {
    border: 1px solid var(--notion-database-border);
    border-radius: var(--notion-border-radius);
    padding: 16px;
    margin: 16px 0;
    background: var(--notion-gray-05);
}

/* 数据库头部和标题样式已移至 notion-database.css */

.notion-property-types {
    color: #a0aec0;
    font-size: 13px;
}

/* 数据库链接 */
.notion-database-link {
    margin-top: 8px;
    font-size: 13px;
}

.notion-database-link a {
    color: #0366d6;
    text-decoration: none;
    font-weight: 500;
}

.notion-database-link a:hover {
    text-decoration: underline;
}

/* 降级提示样式 */
.notion-database-fallback {
    color: #888;
    font-style: italic;
    margin: 8px 0 0 0;
    font-size: 0.9em;
}

/* 响应式支持 */
@media (max-width: 768px) {
    .notion-child-database {
        padding: 12px;
        margin: 12px 0;
        border-radius: 6px;
    }

    .notion-database-title {
        font-size: 1.05em;
    }

    .notion-database-properties {
        font-size: 13px;
    }
}


/* 通用数据库记录样式 - 作为各视图的基础样式 */
.notion-database-record {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px;
    transition: all 0.15s ease;
    position: relative;
    cursor: pointer;
    margin-bottom: 4px;
}

.notion-database-record:hover {
    border-color: #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
}

.notion-database-record:last-child {
    margin-bottom: 0;
}

.notion-record-title {
    font-weight: 500;
    color: #37352f;
    margin-bottom: 4px;
    font-size: 13px;
    line-height: 1.3;
    word-break: break-word;
    display: flex;
    align-items: center;
    gap: 4px;
}

.notion-record-properties {
    margin-bottom: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.notion-record-property {
    font-size: 11px;
    color: #787774;
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: 2px;
}

.notion-property-name {
    font-weight: 400;
    color: #9b9a97;
}

.notion-property-value {
    color: #37352f;
    font-weight: 400;
}

.notion-record-link {
    margin-top: 4px;
}

.notion-record-link a {
    font-size: 11px;
    color: #0f62fe;
    text-decoration: none;
    font-weight: 400;
    opacity: 0.8;
}

.notion-record-link a:hover {
    opacity: 1;
    text-decoration: underline;
}



.notion-database-empty,
.notion-database-preview-error {
    text-align: center;
    padding: 16px;
    color: #a0aec0;
    font-style: italic;
    font-size: 14px;
    background: #f7fafc;
    border-radius: 4px;
    margin-top: 8px;
}

/* 通用响应式支持 */
@media (max-width: 768px) {
    .notion-database-record {
        padding: 6px;
    }

    .notion-record-title {
        font-size: 12px;
    }

    .notion-record-property {
        font-size: 10px;
    }
    }

@media (max-width: 480px) {
    .notion-database-record {
        padding: 4px;
    }

    .notion-record-title {
        font-size: 11px;
    }

    .notion-record-property {
        font-size: 9px;
    }
}

/* ================ 封面图片和图标样式 ================ */

/* 封面图片样式 */
.notion-record-cover {
    width: 100%;
    margin: -12px -12px 12px -12px;
    border-radius: 6px 6px 0 0;
    overflow: hidden;
    position: relative;
}

.notion-record-cover img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    display: block;
    transition: transform 0.2s ease;
}

.notion-record-cover:hover img {
    transform: scale(1.02);
}

/* 图标样式 */
.notion-record-icon {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
    line-height: 1;
}

.notion-record-icon-emoji {
    font-size: 16px;
    }

.notion-record-icon-image {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    object-fit: cover;
}

/* 重复的标题样式已移除，使用上方的通用样式 */

/* ================ 文件属性样式 ================ */

/* 文件容器 */
.notion-record-files {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 4px;
    }

/* 文件缩略图样式 */
.notion-file-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-width: 80px;
    max-width: 120px;
    }

.notion-file-thumbnail:hover {
    background: #f1f3f4;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    }

.notion-file-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
    margin-bottom: 4px;
}

.notion-file-thumbnail .notion-file-name {
    font-size: 10px;
    color: #4a5568;
    text-align: center;
    word-break: break-word;
    line-height: 1.2;
    }

/* 文件链接样式 */
.notion-file-link {
    display: inline-flex;
    align-items: center;
    padding: 6px 8px;
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.notion-file-link:hover {
    background: #f1f3f4;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    }

.notion-file-link a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #4a5568;
    font-size: 12px;
}

.notion-file-link a:hover {
    color: #2d3748;
}

.notion-file-icon {
    margin-right: 4px;
    font-size: 12px;
    }

.notion-file-name {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 更多文件提示 */
.notion-files-more {
    display: inline-flex;
    align-items: center;
    padding: 6px 8px;
    background: #e2e8f0;
        color: #718096;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

/* ================ 链接属性样式 ================ */

/* URL、邮箱、电话链接样式 */
.notion-property-value a {
    color: #0366d6;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.notion-property-value a:hover {
    color: #0256cc;
    text-decoration: underline;
    }

/* ================ 移动端响应式优化 ================ */

@media (max-width: 768px) {
    /* 封面图片移动端优化 */
    .notion-record-cover {
        margin: -10px -10px 10px -10px;
    }

    .notion-record-cover img {
        height: 100px;
    }

    /* 图标移动端优化 */
    .notion-record-icon-emoji {
        font-size: 14px;
    }

    .notion-record-icon-image {
        width: 14px;
        height: 14px;
    }

    /* 文件属性移动端优化 */
    .notion-record-files {
        gap: 6px;
    }

    .notion-file-thumbnail {
        min-width: 70px;
        max-width: 100px;
        padding: 6px;
    }

    .notion-file-thumbnail img {
        width: 50px;
        height: 50px;
    }

    .notion-file-thumbnail .notion-file-name {
        font-size: 9px;
    }

    .notion-file-link {
        padding: 4px 6px;
    }

    .notion-file-link a {
        font-size: 11px;
    }

    .notion-file-name {
        max-width: 80px;
    }
}

/* ================ 加载状态和动画效果 ================ */

/* 图片加载动画 */
.notion-record-cover img,
.notion-file-thumbnail img,
.notion-record-icon-image {
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
    }

/* 悬停动画增强 */
    .notion-database-record {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .notion-database-record:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 文件元素悬停效果 */
.notion-file-thumbnail,
.notion-file-link {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载占位符 */
.notion-record-cover.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 链接悬停效果增强 */
.notion-property-value a {
    position: relative;
    transition: all 0.2s ease;
}

.notion-property-value a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -1px;
    left: 0;
    background-color: currentColor;
    transition: width 0.2s ease;
}

.notion-property-value a:hover::after {
    width: 100%;
}

/* ================ 懒加载和性能优化样式 ================ */

/* 懒加载图片样式 */
.notion-lazy-image {
    transition: opacity 0.3s ease;
    opacity: 0.7;
}

.notion-lazy-image.notion-lazy-loading {
    opacity: 0.5;
    filter: blur(2px);
}

.notion-lazy-image.notion-lazy-loaded {
    opacity: 1;
    filter: none;
}

.notion-lazy-image.notion-lazy-error {
    opacity: 0.6;
    filter: grayscale(100%);
}

/* 渐进式加载样式 */
.notion-progressive-loading {
    margin-top: 16px;
}

.notion-loading-trigger {
    text-align: center;
    padding: 16px;
}

.notion-load-more-btn {
    background: #0366d6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.notion-load-more-btn:hover:not(:disabled) {
    background: #0256cc;
    transform: translateY(-1px);
}

.notion-load-more-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.notion-loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.notion-progressive-content {
    margin-top: 16px;
}


/* ================ 记录展开功能样式 ================ */

/* 记录展开状态 */
.notion-database-record {
    cursor: pointer;
    transition: all 0.3s ease;
}

.notion-database-record:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

.notion-database-record:focus {
    outline: 2px solid #0366d6;
    outline-offset: 2px;
}

.notion-record-expanded {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.notion-record-expanded-content {
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;
    border-top: 1px solid #e2e8f0;
    margin-top: 12px;
    }

.notion-expanded-details {
    padding: 16px;
}

.notion-expanded-section {
    margin-bottom: 16px;
}

.notion-expanded-section h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
}

.notion-expanded-section p {
    margin: 4px 0;
    font-size: 13px;
    color: #4a5568;
}

.notion-expanded-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.notion-action-btn {
    background: #0366d6;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.notion-action-btn:hover {
    background: #0256cc;
    transform: translateY(-1px);
}



/* ================ 提示消息样式 ================ */

.notion-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2d3748;
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notion-toast-show {
    transform: translateX(0);
    }

/* ================ 响应式优化 ================ */

@media (max-width: 768px) {


    .notion-view-controls {
        justify-content: center;
    }

    .notion-search-filter {
        min-width: auto;
    }

    .notion-expanded-actions {
        justify-content: center;
    }



    .notion-toast {
        right: 16px;
        left: 16px;
        transform: translateY(-100%);
    }

    .notion-toast-show {
        transform: translateY(0);
    }
}

/* ============================================
   锚点跳转相关样式
   ============================================ */

/* 平滑滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 区块容器基础样式 */
.notion-block {
    position: relative;
    transition: background-color 0.3s ease;
    scroll-margin-top: var(--ntw-header-offset, 0px);
}

/* 锚点跳转高亮动画效果 */
.notion-block-highlight {
    background-color: rgba(255, 235, 59, 0.3) !important;
    border-radius: 4px;
    animation: notion-anchor-highlight 1s ease-out;
}

@keyframes notion-anchor-highlight {
    0% {
        background-color: rgba(255, 235, 59, 0.6);
        box-shadow: 0 0 0 4px rgba(255, 235, 59, 0.3);
    }
    50% {
        background-color: rgba(255, 235, 59, 0.4);
        box-shadow: 0 0 0 2px rgba(255, 235, 59, 0.2);
    }
    100% {
        background-color: rgba(255, 235, 59, 0.1);
        box-shadow: none;
    }
}

/* 响应式支持 */
@media (max-width: 768px) {
    .notion-block-highlight {
        background-color: rgba(255, 235, 59, 0.4) !important;
    }

    @keyframes notion-anchor-highlight {
        0% {
            background-color: rgba(255, 235, 59, 0.7);
            box-shadow: 0 0 0 2px rgba(255, 235, 59, 0.4);
    }
        100% {
            background-color: rgba(255, 235, 59, 0.2);
            box-shadow: none;
        }
    }
}

/* 确保高亮效果不影响现有布局 */
.notion-block-highlight * {
    position: relative;
    z-index: 1;
}

/* 针对深色主题的适配 */
@media (prefers-color-scheme: dark) {
    .notion-block-highlight {
        background-color: rgba(255, 193, 7, 0.2) !important;
    }

    @keyframes notion-anchor-highlight {
        0% {
            background-color: rgba(255, 193, 7, 0.4);
            box-shadow: 0 0 0 4px rgba(255, 193, 7, 0.2);
        }
        50% {
            background-color: rgba(255, 193, 7, 0.3);
            box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.1);
        }
        100% {
            background-color: rgba(255, 193, 7, 0.1);
            box-shadow: none;
        }
    }
}

/* === 表格视图改进：防止单元格内容截断 === */
/* 这些样式已经在基础.notion-table-cell规则中定义，避免重复 */

.notion-table-cell a {
    word-break: break-word; /* 改为break-word，更温和的换行 */
    text-decoration: none;
    color: #3b82f6;
    transition: color 0.2s ease;
}

.notion-table-cell a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* 空值占位符样式 */
.notion-empty-value {
    color: #9ca3af;
    font-style: italic;
    opacity: 0.7;
}

/* 错误值样式 */
.notion-error-value {
    color: #ef4444;
    font-style: italic;
    font-size: 0.9em;
    cursor: help;
}

/* 空视图样式 */
.notion-empty-table,
.notion-empty-gallery,
.notion-empty-board,
.notion-empty-database {
    padding: 40px 20px;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    color: #6c757d;
    margin: 16px 0;
    }

.notion-empty-gallery {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notion-empty-board {
    min-height: 240px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 看板降级提示样式 */
.notion-board-fallback-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 16px;
    color: #856404;
    font-size: 14px;
}

/* 表格响应式设计 */
@media (max-width: 768px) {
    /* 移动端表格优化 */
    .notion-database-table {
        max-height: 400px;
        font-size: 14px;
    }

    .notion-table-cell {
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
    }

    .notion-table-header-cell {
        font-size: 12px;
        padding: 6px 8px;
    }

    /* 移动端强制使用Flexbox布局 */
    .notion-database-table .notion-table-row {
        display: flex !important;
        grid-template-columns: none !important;
    }

    .notion-table-simple .notion-table-cell {
        min-width: 80px;
        flex: 1;
    }

    .notion-table-simple .notion-table-title-cell {
        flex: 1.5;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    /* 超小屏幕优化 */
    .notion-database-table {
        max-height: 300px;
        font-size: 12px;
    }

    .notion-table-cell {
        padding: 6px 8px;
        font-size: 12px;
        min-height: 32px;
    }

    .notion-table-simple .notion-table-cell {
        min-width: 60px;
    }
}

/* 简化表格样式已移至 notion-database.css，避免使用 !important */

/* ================ 外部特色图像支持 ================ */

/* 外部特色图像样式 */
.post-thumbnail img[src^="http"],
.wp-post-image[src^="http"] {
    max-width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 4px;
    transition: transform 0.2s ease;
}

/* 特色图像容器 */
.post-thumbnail,
.wp-post-image {
    display: block;
    overflow: hidden;
    border-radius: 4px;
}

/* 特色图像悬停效果 */
.post-thumbnail:hover img,
.wp-post-image:hover {
    transform: scale(1.02);
}

/* 确保外部图像的懒加载 */
.post-thumbnail img[loading="lazy"],
.wp-post-image[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.post-thumbnail img[loading="lazy"].loaded,
.wp-post-image[loading="lazy"].loaded {
    opacity: 1;
}

/* 外部特色图像错误处理 */
.post-thumbnail img[src^="http"]:not([alt=""]),
.wp-post-image[src^="http"]:not([alt=""]) {
    background: #f5f5f5;
    border: 1px solid #e0e0e0;
}

/* 外部特色图像错误占位符 */
.notion-featured-image-error {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    background: #f8f9fa;
    border: 2px dashed #e0e0e0;
    border-radius: 4px;
    color: #666;
}

.notion-image-placeholder {
    text-align: center;
    padding: 1em;
}

.notion-image-icon {
    display: block;
    font-size: 2em;
    margin-bottom: 0.5em;
    opacity: 0.5;
}

.notion-image-text {
    display: block;
    font-size: 0.9em;
    color: #888;
}

/* 响应式特色图像 */
@media (max-width: 768px) {
    .post-thumbnail img,
    .wp-post-image {
        border-radius: 2px;
    }

    .notion-featured-image-error {
        min-height: 150px;
    }

    .notion-image-icon {
        font-size: 1.5em;
    }
}
