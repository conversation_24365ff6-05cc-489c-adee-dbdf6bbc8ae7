<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="body_1" width="256" height="256">

<g transform="matrix(0.25 0 0 0.25 0 0)">
	<path transform="matrix(1 0 0 1 208 424)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 3.941091 1.0456865 3.941091 1.0456865 6.1484375 0.87890625C 7.4004393 0.8055505 7.4004393 0.8055505 8.677734 0.7307129C 9.567832 0.67520267 10.45793 0.6196924 11.375 0.5625C 13.205665 0.46749124 15.036394 0.37373573 16.867188 0.28125C 19.77404 0.129092 22.667984 -0.05262012 25.570312 -0.28125C 39.98278 -1.049915 50.386204 9.054667 61.052246 17.47705C 63.617134 19.482538 66.235985 21.405918 68.875 23.3125C 69.71934 23.924805 70.563675 24.53711 71.43359 25.167969C 73.26878 26.478022 75.11667 27.770367 76.97266 29.050781C 83.20609 33.411724 87.54087 36.622623 90 44C 90.09032 45.395588 90.126274 46.795094 90.12698 48.193604C 90.12981 49.04958 90.13262 49.90556 90.13553 50.787476C 90.13249 52.193985 90.13249 52.193985 90.129395 53.628906C 90.13203 55.11602 90.13203 55.11602 90.134705 56.63318C 90.13914 59.94297 90.13617 63.25271 90.13281 66.5625C 90.13349 68.90226 90.13446 71.24203 90.13571 73.58179C 90.13719 78.52849 90.13503 83.475174 90.13037 88.421875C 90.12591 93.38803 90.12571 98.35416 90.13037 103.32031C 90.14498 119.005295 90.109375 134.68929 90.018875 150.37402C 89.99978 154.04231 89.98739 157.71059 89.97595 161.3789C 89.95472 167.32043 89.90623 173.2612 89.83785 179.20236C 89.816 181.4166 89.80238 183.63092 89.797615 185.84525C 89.7892 188.89888 89.74977 191.95065 89.70264 195.0039C 89.70696 195.88873 89.71129 196.77354 89.71574 197.68518C 89.56867 204.07222 87.83172 208.84961 84 214C 82.071846 215.67117 80.25205 216.74886 78 218C 77.34 218.66 76.68 219.32 76 220C 73.484085 220.44144 71.03397 220.79016 68.5 221.0625C 63.602383 221.46886 63.602383 221.46886 59 223C 57.001453 223.0402 55.001698 223.02179 53.003174 222.98047C 41.301468 222.86055 29.628149 223.66975 17.949219 224.3125C 15.438728 224.44745 12.928214 224.58195 10.417679 224.71606C 3.1778028 225.10368 -4.061773 225.49672 -11.301308 225.89064C -21.162836 226.42686 -31.024591 226.9588 -40.88656 227.48691C -44.548065 227.68399 -48.209373 227.88446 -51.87068 228.0852C -54.126427 228.20668 -56.38218 228.32803 -58.63794 228.44922C -59.644405 228.50533 -60.65087 228.56143 -61.687836 228.61923C -84.54506 229.83228 -84.54506 229.83228 -92.42969 222.84766C -98.58704 216.3067 -103.8592 209.02023 -109.17969 201.79688C -111.46524 198.69952 -113.82297 195.67607 -116.25 192.6875C -119.0192 189.25206 -121.72958 185.78197 -124.375 182.25C -125.02211 181.3889 -125.66922 180.52782 -126.33594 179.64062C -129.39578 174.7851 -130.24998 170.32315 -130.2607 164.63773C -130.26778 163.37892 -130.27487 162.1201 -130.28217 160.82315C -130.27988 159.44086 -130.27736 158.05856 -130.27466 156.67627C -130.279 155.21054 -130.2842 153.74483 -130.29015 152.27911C -130.30351 148.30795 -130.30412 144.3369 -130.30159 140.36572C -130.3005 137.04578 -130.3054 133.72583 -130.31018 130.40588C -130.32127 122.57054 -130.32173 114.73525 -130.31567 106.8999C -130.30963 98.82806 -130.32195 90.75639 -130.34325 82.68458C -130.36089 75.74425 -130.36685 68.80398 -130.3636 61.86363C -130.3618 57.722984 -130.3644 53.582462 -130.37833 49.441837C -130.39093 45.546238 -130.38893 41.650913 -130.37592 37.755318C -130.37355 36.329945 -130.37625 34.904556 -130.38454 33.479206C -130.473 17.058258 -130.473 17.058258 -125.23828 10.941406C -124.602776 10.486367 -123.96726 10.031328 -123.3125 9.5625C -122.68988 9.089414 -122.06727 8.616328 -121.42578 8.128906C -116.020035 5.6131873 -109.27335 5.8477683 -103.40088 5.626953C -102.687355 5.5991526 -101.97382 5.571352 -101.23868 5.5427094C -98.90141 5.452386 -96.564 5.366851 -94.22656 5.28125C -92.57716 5.218159 -90.92778 5.1547165 -89.27841 5.0909424C -84.9421 4.9240108 -80.60566 4.760924 -76.269165 4.598938C -69.36667 4.340664 -62.464363 4.077355 -55.562115 3.8124847C -53.181976 3.7218134 -50.80177 3.6332831 -48.42154 3.545044C -39.928585 3.2250292 -31.444178 2.8310654 -22.95994 2.3276062C -17.758188 2.022486 -12.584283 1.8713665 -7.375 1.9375C -6.280586 1.9442676 -6.280586 1.9442676 -5.1640625 1.9511719C -3.4426687 1.9624969 -1.7213194 1.9803901 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#F7F5F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 658 155)" d="M0 0C 1.7123024 1.6197456 3.3716326 3.2958946 5 5C 5.658711 5.603281 6.317422 6.2065625 6.9960938 6.828125C 19.040447 18.100136 27.097507 32.315907 28.8125 48.8125C 29.592396 53.611187 29.592396 53.611187 32.529297 57.293457C 35.83921 59.279232 39.346478 60.71442 42.90625 62.1875C 53.05403 66.41419 60.6571 75.87407 65.24609 85.65625C 67.83936 92.267654 69.06884 98.97956 70 106C 69.34 106 68.68 106 68 106C 68.009026 106.559456 68.01804 107.118904 68.02734 107.69531C 67.6044 119.133095 61.479767 129.6439 53.64453 137.7461C 33.731476 154.71779 8.237467 152.156 -16.3125 152.13281C -18.855307 152.13348 -21.398111 152.13446 -23.940918 152.13571C -29.252773 152.13718 -34.564613 152.13516 -39.876465 152.13037C -45.947514 152.12495 -52.01853 152.12668 -58.08958 152.1322C -64.006615 152.13736 -69.92363 152.13672 -75.84067 152.13382C -78.32165 152.13316 -80.80262 152.13393 -83.2836 152.13629C -95.58561 152.14543 -107.87362 152.05754 -120.16797 151.59326C -121.617935 151.54303 -121.617935 151.54303 -123.0972 151.49179C -141.31146 150.70827 -157.40033 144.48854 -170 131C -179.21722 120.19547 -183.68983 107.163155 -183 93C -182.54442 90.30402 -182.0484 87.76044 -181.375 85.125C -181.19751 84.42955 -181.02002 83.7341 -180.83716 83.01758C -179.19028 76.99743 -176.7504 72.0156 -173 67C -172.47536 66.29488 -171.9507 65.58977 -171.41016 64.86328C -164.5957 56.445423 -156.28566 51.716225 -146.17578 48.140625C -144.60378 47.576015 -144.60378 47.576015 -143 47C -141.76637 46.622303 -140.53273 46.24461 -139.26172 45.85547C -135.68 44.25365 -135.68 44.25365 -134.46875 40.410156C -134.12321 38.966164 -133.80139 37.516346 -133.5 36.0625C -128.88895 17.807459 -116.6013 3.5137212 -100.52734 -6.1289062C -68.74939 -24.36926 -29.60693 -20.720638 0 0z" stroke="none"  fill="#F4F3EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 820.1875 436.375)" d="M0 0C 0.598125 0.20625 1.19625 0.4125 1.8125 0.625C 1.1525 0.955 0.4925 1.285 -0.1875 1.625C 1.66875 1.9034375 1.66875 1.9034375 3.5625 2.1875C 4.2585936 2.291914 4.9546876 2.3963282 5.671875 2.5039062C 7.776082 2.737124 7.776082 2.737124 9.598145 2.1342773C 12.261974 1.5216258 14.650145 1.5471352 17.382812 1.5976562C 18.44371 1.6099024 19.50461 1.6221484 20.597656 1.6347656C 22.832094 1.6700462 25.066475 1.7091354 27.300781 1.7519531C 28.359102 1.7629101 29.417421 1.7738672 30.507812 1.7851562C 31.962116 1.8104138 31.962116 1.8104138 33.4458 1.8361816C 36.002014 1.8631291 36.002014 1.8631291 37.8125 -0.375C 38.3668 -0.08625 38.921093 0.2025 39.492188 0.5C 40.57887 1.056875 40.57887 1.056875 41.6875 1.625C 42.766445 2.181875 42.766445 2.181875 43.867188 2.75C 45.76099 3.7617373 45.76099 3.7617373 47.8125 3.625C 48.1425 2.965 48.4725 2.305 48.8125 1.625C 49.605274 2.5453906 49.605274 2.5453906 50.414062 3.484375C 53.25428 6.0192924 55.558884 6.7623343 59.1875 7.875C 72.76246 12.522031 82.70355 19.634281 92.8125 29.625C 93.52922 30.269531 94.24594 30.914062 94.984375 31.578125C 99.43207 35.67 102.82153 40.263294 106.1875 45.25C 106.61692 45.883896 107.04634 46.51779 107.48877 47.1709C 109.93019 50.90277 111.440384 54.354767 112.8125 58.625C 113.4725 58.955 114.1325 59.285 114.8125 59.625C 115.739914 61.955425 116.55386 64.239815 117.3125 66.625C 117.5339 67.308365 117.755295 67.99173 117.9834 68.6958C 120.29669 75.95363 121.9142 82.97087 122.46875 90.58203C 122.85698 92.88933 123.766495 94.54564 124.8125 96.625C 124.8125 97.615 124.8125 98.605 124.8125 99.625C 124.1525 99.625 123.4925 99.625 122.8125 99.625C 122.79832 100.55957 122.78414 101.49414 122.76953 102.45703C 121.81365 128.7824 111.48992 153.04318 92.13672 171.125C 91.2666 171.95 90.396484 172.775 89.5 173.625C 88.61699 174.45 87.733986 175.275 86.82422 176.125C 84.57245 178.46024 84.57245 178.46024 84.51953 181.4375C 84.61621 182.15938 84.71289 182.88126 84.8125 183.625C 84.4825 182.305 84.1525 180.985 83.8125 179.625C 82.9759 180.20508 82.1393 180.78516 81.27734 181.38281C 73.754654 186.47327 66.42257 190.29921 57.89453 193.4336C 54.71022 194.5362 54.71022 194.5362 51.8125 196.625C 52.4725 196.625 53.1325 196.625 53.8125 196.625C 53.4825 197.615 53.1525 198.605 52.8125 199.625C 51.8225 198.965 50.8325 198.305 49.8125 197.625C 47.114098 197.81027 44.543015 198.10405 41.875 198.5C 31.832033 199.8251 21.92517 199.815 11.8125 199.625C 11.4825 200.285 11.1525 200.945 10.8125 201.625C 8.8325 201.625 6.8525 201.625 4.8125 201.625C 4.8125 200.965 4.8125 200.305 4.8125 199.625C 2.654662 198.50157 2.654662 198.50157 -0.375 197.4375C -1.9794275 196.83403 -3.583546 196.22974 -5.1875 195.625C -6.299961 195.2125 -7.4124217 194.8 -8.558594 194.375C -32.44971 185.33623 -49.843983 170.45984 -63.1875 148.625C -63.8475 148.295 -64.5075 147.965 -65.1875 147.625C -65.6825 148.615 -65.6825 148.615 -66.1875 149.625C -66.1875 148.635 -66.1875 147.645 -66.1875 146.625C -65.5275 146.625 -64.8675 146.625 -64.1875 146.625C -64.1875 145.965 -64.1875 145.305 -64.1875 144.625C -65.1775 144.955 -66.1675 145.285 -67.1875 145.625C -68.3125 141.875 -68.3125 141.875 -67.1875 139.625C -68.182274 137.28943 -69.18265 134.95625 -70.1875 132.625C -70.1875 131.305 -70.1875 129.985 -70.1875 128.625C -71.1775 128.955 -72.1675 129.285 -73.1875 129.625C -73.5175 128.965 -73.8475 128.305 -74.1875 127.625C -73.1975 127.625 -72.2075 127.625 -71.1875 127.625C -71.63712 125.66465 -72.09748 123.70676 -72.5625 121.75C -72.94535 120.11418 -72.94535 120.11418 -73.33594 118.44531C -73.97656 115.52091 -73.97656 115.52091 -76.1875 113.625C -75.5275 113.625 -74.8675 113.625 -74.1875 113.625C -74.1875 110.325 -74.1875 107.025 -74.1875 103.625C -74.8475 103.295 -75.5075 102.965 -76.1875 102.625C -76.1875 101.635 -76.1875 100.645 -76.1875 99.625C -76.8475 99.295 -77.5075 98.965 -78.1875 98.625C -77.8575 97.305 -77.5275 95.985 -77.1875 94.625C -76.5275 94.955 -75.8675 95.285 -75.1875 95.625C -75.6825 96.615 -75.6825 96.615 -76.1875 97.625C -75.5275 97.625 -74.8675 97.625 -74.1875 97.625C -74.25711 96.604065 -74.25711 96.604065 -74.328125 95.5625C -75.163025 73.13949 -64.12423 51.54298 -50.1875 34.625C -49.270977 33.74586 -48.354454 32.86672 -47.410156 31.960938C -44.84284 29.800175 -44.84284 29.800175 -45.1875 26.625C -44.642227 26.537344 -44.096954 26.449688 -43.535156 26.359375C -40.43621 25.389988 -38.252148 23.776556 -35.625 21.875C -34.67496 21.199532 -33.724922 20.524063 -32.746094 19.828125C -30.166628 17.607029 -28.745962 15.626481 -27.1875 12.625C -26.1975 12.955 -25.2075 13.285 -24.1875 13.625C -21.461239 12.60739 -18.88337 11.531347 -16.25 10.3125C -15.533926 9.990879 -14.817852 9.669258 -14.080078 9.337891C -10.748057 7.834004 -7.4390106 6.297448 -4.1875 4.625C -4.6825 3.14 -4.6825 3.14 -5.1875 1.625C -2.1875 -0.375 -2.1875 -0.375 0 0zM0.8125 2.625C 0.1525 3.285 -0.5075 3.945 -1.1875 4.625C -0.1975 4.625 0.7925 4.625 1.8125 4.625C 1.4825 3.965 1.1525 3.305 0.8125 2.625z" stroke="none"  fill="#0F6793" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 771 649)" d="M0 0C 3.4322987 4.7670817 4.274977 9.005543 5.0078125 14.7890625C 5.090665 15.442597 5.173517 16.096132 5.2588806 16.76947C 6.187454 24.248997 6.9509525 31.743723 7.625 39.25C 7.6903696 39.976357 7.755739 40.702713 7.8230896 41.45108C 8.726451 51.702927 9.288181 61.89088 9.3125 72.1875C 9.317818 73.28417 9.323134 74.38084 9.328613 75.51074C 9.272424 81.758606 9.080771 87.495544 5.8125 93C 3.4790907 94.2874 1.7175561 94.26731 -0.87109375 93.75391C -7.0847874 91.55346 -12.435938 87.17679 -17.679688 83.27734C -18.303513 82.81771 -18.92734 82.35808 -19.570068 81.88452C -20.799013 80.97337 -22.018957 80.04994 -23.22876 79.113525C -26.384459 76.71038 -26.384459 76.71038 -30.171875 75.92969C -32.72212 77.422775 -34.041607 79.25682 -35.8125 81.625C -44.01843 92.03185 -53.726234 101.627426 -64 110C -64.514656 110.423134 -65.02932 110.84627 -65.55957 111.28223C -129.82607 164 -129.82607 164 -157 164C -157 164.99 -157 165.98 -157 167C -157.99 166.67 -158.98 166.34 -160 166C -162.68842 166.62163 -162.68842 166.62163 -165.6875 167.69531C -166.84741 168.07928 -168.00732 168.46327 -169.2024 168.85889C -169.82047 169.06577 -170.43854 169.27263 -171.07535 169.48578C -185.85147 174.39326 -200.60295 177.62408 -216 180C -217.44875 180.2434 -217.44875 180.2434 -218.92676 180.4917C -296.7111 193.49153 -378.3583 167.41362 -441.5625 122.375C -449.78537 116.38209 -457.47018 109.834885 -465 103C -465.59216 102.46423 -466.18433 101.92847 -466.79443 101.376465C -475.6766 93.31794 -484.21906 85.13828 -492 76C -492.72833 75.16082 -493.45663 74.32164 -494.20703 73.45703C -505.47858 60.255318 -515.4978 46.135303 -524 31C -524.3756 30.337744 -524.7512 29.675488 -525.1382 28.993164C -526.2724 26.981808 -527.3897 24.962145 -528.5 22.9375C -528.8388 22.33011 -529.17755 21.72272 -529.5266 21.096924C -531.5006 17.415474 -532.687 14.177297 -533 10C -531.28784 6.832514 -530.46265 6.154215 -527 5C -522.9291 4.6439147 -518.8511 4.4909 -514.76807 4.3325195C -512.7705 4.2508383 -510.77386 4.147016 -508.77734 4.0429688C -496.4982 3.5577753 -496.4982 3.5577753 -491 7C -489.3203 8.5859375 -489.3203 8.5859375 -488.125 10.375C -487.64032 11.07625 -487.15564 11.7775 -486.65625 12.5C -486.10968 13.325 -485.5631 14.15 -485 15C -483.54538 17.127668 -482.08786 19.253004 -480.625 21.375C -480.23828 21.937515 -479.85156 22.500029 -479.45312 23.07959C -475.86957 28.259314 -472.029 33.162296 -468 38C -467.5796 38.505314 -467.1592 39.010624 -466.72607 39.53125C -454.49252 54.202694 -441.9774 68.056015 -427 80C -425.20813 81.49975 -423.41647 82.99975 -421.625 84.5C -411.6283 92.874565 -411.6283 92.874565 -400.3047 99.06641C -397.8263 100.070366 -396.85995 101.131645 -395 103C -357.02872 128.9065 -306.16296 139.26353 -260.9375 139.1875C -260.21158 139.18657 -259.48563 139.18562 -258.7377 139.18466C -247.04869 139.15211 -235.57118 138.7422 -224 137C -222.96666 136.84933 -221.9333 136.69868 -220.86865 136.54346C -190.33714 131.96307 -162.24846 122.351776 -135 108C -134.04045 107.50427 -134.04045 107.50427 -133.06152 106.998535C -117.25124 98.78371 -102.94903 88.21329 -90 76C -88.60821 74.80777 -87.2125 73.62008 -85.8125 72.4375C -82.31369 69.414185 -79.36568 66.08305 -76.44531 62.503906C -75.96836 62.007618 -75.49141 61.51133 -75 61C -74.34 61 -73.68 61 -73 61C -72.59781 60.16469 -72.59781 60.16469 -72.1875 59.3125C -71 57 -71 57 -69.3125 54.5C -67.82709 52.19044 -67.82709 52.19044 -68.10547 50.109375C -69.39557 47.06721 -71.39815 45.75921 -74 43.75C -77.783424 40.761787 -81.5099 37.75332 -85.125 34.5625C -85.84946 33.924416 -86.573906 33.286327 -87.32031 32.628906C -89.59291 30.425013 -89.96183 29.292635 -90.375 26.125C -90 23 -90 23 -88.04492 20.981201C -84.144035 18.44306 -80.37661 17.360897 -75.90625 16.175781C -74.53914 15.799735 -74.53914 15.799735 -73.14441 15.416092C -62.16203 12.433016 -51.105007 9.731652 -40.046387 7.0480957C -36.447876 6.1748514 -32.850792 5.295919 -29.253906 4.4160156C -26.9506 3.8538465 -24.647217 3.2919858 -22.34375 2.7304688C -20.742311 2.3399055 -20.742311 2.3399055 -19.10852 1.941452C -18.11558 1.700624 -17.122639 1.4597958 -16.09961 1.2116699C -15.232513 1.0010442 -14.3654175 0.7904184 -13.472046 0.57341003C -4.297974 -1.5545864 -4.297974 -1.5545864 0 0z" stroke="none"  fill="#F3F0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 490.6875 399.875)" d="M0 0C 1.2251734 0.00338379 2.4503467 0.00676758 3.7126465 0.01025391C 15.96197 0.06372106 28.211298 0.23997407 40.458893 0.44439697C 42.73145 0.48138675 45.004055 0.5153759 47.276703 0.5463257C 50.498516 0.5916374 53.719864 0.6496286 56.941406 0.7109375C 58.400528 0.7282794 58.400528 0.7282794 59.88913 0.7459717C 69.9136 0.96598804 69.9136 0.96598804 74.1875 4.8125C 76.22573 9.002195 74.57135 13.289808 73.24365 17.57373C 72.79973 18.78432 72.35581 19.994911 71.89844 21.242188C 71.65186 21.92037 71.40529 22.598553 71.151245 23.297287C 70.3363 25.533884 69.511826 27.766846 68.6875 30C 68.11148 31.577152 67.53614 33.15455 66.961426 34.732178C 64.07624 42.64092 61.16065 50.538483 58.24814 58.437195C 56.88969 62.123383 55.534126 65.81063 54.178528 69.49786C 53.206947 72.13472 52.229485 74.76933 51.251465 77.40381C 50.644 79.05168 50.03673 80.69963 49.429688 82.34766C 49.1583 83.073494 48.886913 83.79933 48.6073 84.547165C 46.491543 90.316505 44.861645 96.18056 43.3125 102.125C 44.671 102.15259 44.671 102.15259 46.056946 102.18074C 54.591434 102.35665 63.125477 102.54645 71.6593 102.75187C 76.04667 102.85699 80.43402 102.95672 84.82178 103.04419C 89.056114 103.1288 93.28991 103.2275 97.523705 103.336006C 99.139114 103.37492 100.754654 103.40875 102.37028 103.437416C 104.63315 103.47844 106.89488 103.537506 109.15723 103.60083C 110.44518 103.6297 111.73314 103.65857 113.06012 103.68831C 116.48144 104.14768 117.95018 104.641304 120.3125 107.125C 120.91449 110.457886 121.07353 112.82464 119.316895 115.82617C 117.91408 117.597824 116.43027 119.23567 114.875 120.875C 113.775055 122.09416 112.67611 123.314224 111.578125 124.53516C 111.02834 125.13602 110.47855 125.736885 109.91211 126.35596C 107.56977 128.9464 105.345024 131.61624 103.125 134.3125C 102.05186 135.6022 102.05186 135.6022 100.95703 136.91797C 99.142654 139.11244 99.142654 139.11244 98.3125 142.125C 96.65821 143.47366 94.989784 144.80505 93.3125 146.125C 91.89136 147.73116 90.51796 149.38046 89.1875 151.0625C 85.71613 155.3988 82.174736 159.65553 78.5625 163.875C 75.75472 167.16693 72.97708 170.44765 70.5 174C 67.42238 178.39659 63.945553 182.34813 60.316406 186.29688C 55.48637 191.56601 50.993874 196.99275 46.63257 202.6482C 42.654232 207.77594 38.446774 212.68063 34.108154 217.50684C 31.563389 220.39592 30.229208 222.45004 29.3125 226.125C 27.99537 226.82314 26.659073 227.4855 25.3125 228.125C 23.357603 230.16637 23.357603 230.16637 21.4375 232.6875C 20.65375 233.67105 19.87 234.6546 19.0625 235.66797C 18.41088 236.49048 18.41088 236.49048 17.746094 237.32959C 15.014458 240.75064 12.222462 244.12233 9.4375 247.5C 8.83736 248.2292 8.237222 248.95842 7.6188965 249.70972C 3.502626 254.7042 -0.6588953 259.65765 -4.850586 264.58887C -7.7039204 267.94983 -10.521473 271.33713 -13.3125 274.75C -14.031796 275.62656 -14.751094 276.5031 -15.4921875 277.40625C -17.041609 279.3251 -18.565382 281.2648 -20.070312 283.21875C -20.851484 284.21906 -21.632656 285.21936 -22.4375 286.25C -23.133595 287.16006 -23.829687 288.07016 -24.546875 289.0078C -26.993053 291.42722 -28.274137 292.00348 -31.6875 292.125C -34.5625 290.375 -34.5625 290.375 -36.6875 287.125C -37.09192 279.0306 -34.509384 271.19107 -32.125 263.5625C -31.768574 262.39212 -31.412148 261.2217 -31.044922 260.01587C -29.935852 256.3824 -28.812313 252.75362 -27.6875 249.125C -27.010788 246.91695 -26.334978 244.70863 -25.660156 242.5C -24.018332 237.13559 -22.3638 231.7752 -20.705078 226.41602C -18.942886 220.7168 -17.19081 215.01447 -15.4375 209.3125C -14.516226 206.31813 -13.594859 203.32378 -12.672791 200.32965C -12.045981 198.29095 -11.4208355 196.25175 -10.797913 194.21187C -8.251136 185.88803 -5.610357 177.59662 -2.9375 169.3125C -2.5220582 168.02281 -2.1066933 166.7331 -1.6914062 165.44336C -0.69085497 162.33699 0.31045774 159.23088 1.3125 156.125C 0.48200133 156.13388 -0.3484973 156.14276 -1.2041626 156.15192C -9.031848 156.23207 -16.859396 156.29297 -24.687397 156.33224C -28.71186 156.35312 -32.735985 156.38144 -36.760254 156.42676C -40.644135 156.47023 -44.52769 156.49414 -48.411797 156.5045C -49.893402 156.5119 -51.37499 156.52632 -52.856453 156.54793C -54.93236 156.57697 -57.006927 156.58096 -59.083008 156.5791C -60.264374 156.58798 -61.44574 156.59686 -62.662903 156.60602C -65.6875 156.125 -65.6875 156.125 -68.10698 154.38843C -69.997086 151.68164 -70.059006 150.67624 -69.58032 147.45605C -69.45207 146.57542 -69.32382 145.6948 -69.19168 144.78748C -68.4268 140.74832 -67.48442 136.79337 -66.40234 132.82812C -66.1818 131.99086 -65.96125 131.1536 -65.734024 130.29095C -65.038216 127.65033 -64.33284 125.01242 -63.625 122.375C -62.688152 118.882385 -61.761143 115.38737 -60.839844 111.890625C -60.624245 111.099884 -60.40864 110.30914 -60.18651 109.494446C -59.07192 105.26151 -58.444984 101.545746 -58.6875 97.125C -57.6975 96.63 -57.6975 96.63 -56.6875 96.125C -55.89003 93.9744 -55.89003 93.9744 -55.285156 91.34766C -55.034435 90.3583 -54.78371 89.36894 -54.52539 88.34961C -54.269512 87.306114 -54.013634 86.26262 -53.75 85.1875C -52.102764 78.59434 -50.40589 72.046646 -48.417236 65.54761C -47.189606 61.472065 -46.091087 57.363823 -45 53.25C -44.77506 52.42242 -44.550117 51.594845 -44.31836 50.742188C -44.106308 49.94297 -43.894257 49.14375 -43.67578 48.320312C -43.4854 47.60891 -43.295025 46.89751 -43.098877 46.16455C -42.555687 43.818264 -42.555687 43.818264 -42.6875 40.125C -42.0275 40.125 -41.3675 40.125 -40.6875 40.125C -40.58228 39.546696 -40.477062 38.968388 -40.368652 38.37256C -39.041126 31.38754 -37.271843 24.582365 -35.3125 17.75C -35.02117 16.683945 -34.729843 15.61789 -34.429688 14.519531C -34.13578 13.494726 -33.841873 12.469922 -33.539062 11.4140625C -33.279476 10.493189 -33.019894 9.572314 -32.75244 8.623535C -31.243984 5.0844345 -29.127851 2.8451755 -25.6875 1.125C -17.156664 0.09850446 -8.581511 -0.0355952 0 0z" stroke="none"  fill="#FECD3A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 208 424)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 3.941091 1.0456865 3.941091 1.0456865 6.1484375 0.87890625C 7.4004393 0.8055505 7.4004393 0.8055505 8.677734 0.7307129C 9.567832 0.67520267 10.45793 0.6196924 11.375 0.5625C 13.205665 0.46749124 15.036394 0.37373573 16.867188 0.28125C 19.77404 0.129092 22.667984 -0.05262012 25.570312 -0.28125C 39.98278 -1.049915 50.386204 9.054667 61.052246 17.47705C 63.617134 19.482538 66.235985 21.405918 68.875 23.3125C 69.71934 23.924805 70.563675 24.53711 71.43359 25.167969C 73.26878 26.478022 75.11667 27.770367 76.97266 29.050781C 83.20609 33.411724 87.54087 36.622623 90 44C 90.09032 45.395588 90.126274 46.795094 90.12698 48.193604C 90.12981 49.04958 90.13262 49.90556 90.13553 50.787476C 90.13249 52.193985 90.13249 52.193985 90.129395 53.628906C 90.13203 55.11602 90.13203 55.11602 90.134705 56.63318C 90.13914 59.94297 90.13617 63.25271 90.13281 66.5625C 90.13349 68.90226 90.13446 71.24203 90.13571 73.58179C 90.13719 78.52849 90.13503 83.475174 90.13037 88.421875C 90.12591 93.38803 90.12571 98.35416 90.13037 103.32031C 90.14498 119.005295 90.109375 134.68929 90.018875 150.37402C 89.99978 154.04231 89.98739 157.71059 89.97595 161.3789C 89.95472 167.32043 89.90623 173.2612 89.83785 179.20236C 89.816 181.4166 89.80238 183.63092 89.797615 185.84525C 89.7892 188.89888 89.74977 191.95065 89.70264 195.0039C 89.70696 195.88873 89.71129 196.77354 89.71574 197.68518C 89.56867 204.07222 87.83172 208.84961 84 214C 82.071846 215.67117 80.25205 216.74886 78 218C 77.34 218.66 76.68 219.32 76 220C 73.484085 220.44144 71.03397 220.79016 68.5 221.0625C 63.602383 221.46886 63.602383 221.46886 59 223C 57.001453 223.0402 55.001698 223.02179 53.003174 222.98047C 41.301468 222.86055 29.628149 223.66975 17.949219 224.3125C 15.438728 224.44745 12.928214 224.58195 10.417679 224.71606C 3.1778028 225.10368 -4.061773 225.49672 -11.301308 225.89064C -21.162836 226.42686 -31.024591 226.9588 -40.88656 227.48691C -44.548065 227.68399 -48.209373 227.88446 -51.87068 228.0852C -54.126427 228.20668 -56.38218 228.32803 -58.63794 228.44922C -59.644405 228.50533 -60.65087 228.56143 -61.687836 228.61923C -84.54506 229.83228 -84.54506 229.83228 -92.42969 222.84766C -98.58704 216.3067 -103.8592 209.02023 -109.17969 201.79688C -111.46524 198.69952 -113.82297 195.67607 -116.25 192.6875C -119.0192 189.25206 -121.72958 185.78197 -124.375 182.25C -125.02211 181.3889 -125.66922 180.52782 -126.33594 179.64062C -129.39578 174.7851 -130.24998 170.32315 -130.2607 164.63773C -130.26778 163.37892 -130.27487 162.1201 -130.28217 160.82315C -130.27988 159.44086 -130.27736 158.05856 -130.27466 156.67627C -130.279 155.21054 -130.2842 153.74483 -130.29015 152.27911C -130.30351 148.30795 -130.30412 144.3369 -130.30159 140.36572C -130.3005 137.04578 -130.3054 133.72583 -130.31018 130.40588C -130.32127 122.57054 -130.32173 114.73525 -130.31567 106.8999C -130.30963 98.82806 -130.32195 90.75639 -130.34325 82.68458C -130.36089 75.74425 -130.36685 68.80398 -130.3636 61.86363C -130.3618 57.722984 -130.3644 53.582462 -130.37833 49.441837C -130.39093 45.546238 -130.38893 41.650913 -130.37592 37.755318C -130.37355 36.329945 -130.37625 34.904556 -130.38454 33.479206C -130.473 17.058258 -130.473 17.058258 -125.23828 10.941406C -124.602776 10.486367 -123.96726 10.031328 -123.3125 9.5625C -122.68988 9.089414 -122.06727 8.616328 -121.42578 8.128906C -116.020035 5.6131873 -109.27335 5.8477683 -103.40088 5.626953C -102.687355 5.5991526 -101.97382 5.571352 -101.23868 5.5427094C -98.90141 5.452386 -96.564 5.366851 -94.22656 5.28125C -92.57716 5.218159 -90.92778 5.1547165 -89.27841 5.0909424C -84.9421 4.9240108 -80.60566 4.760924 -76.269165 4.598938C -69.36667 4.340664 -62.464363 4.077355 -55.562115 3.8124847C -53.181976 3.7218134 -50.80177 3.6332831 -48.42154 3.545044C -39.928585 3.2250292 -31.444178 2.8310654 -22.95994 2.3276062C -17.758188 2.022486 -12.584283 1.8713665 -7.375 1.9375C -6.280586 1.9442676 -6.280586 1.9442676 -5.1640625 1.9511719C -3.4426687 1.9624969 -1.7213194 1.9803901 0 2C 0 1.34 0 0.68 0 0zM53.96103 53.071903C 48.96102 53.256474 43.965294 53.509274 38.96875 53.76953C 37.345097 53.851242 35.72143 53.93263 34.097748 54.013718C 28.898224 54.27473 23.699106 54.54333 18.5 54.8125C 16.7121 54.904675 14.924197 54.996796 13.1362915 55.088867C 5.9280424 55.460247 -1.280204 55.83165 -8.488251 56.20694C -18.07116 56.704533 -27.654556 57.177834 -37.24066 57.609543C -42.360924 57.84016 -47.480396 58.086815 -52.599854 58.334564C -54.93566 58.444183 -57.27178 58.547474 -59.608154 58.644196C -62.804916 58.777355 -65.99985 58.932823 -69.19531 59.09375C -70.11342 59.126034 -71.031525 59.158314 -71.97745 59.191574C -72.822945 59.23857 -73.668434 59.285564 -74.53955 59.333984C -75.262 59.366333 -75.98445 59.398678 -76.72879 59.432007C -80.532486 60.38325 -82.18752 61.13882 -85 64C -85.93037 68.54493 -85.799385 73.09855 -85.74463 77.71704C -85.74949 79.14133 -85.75693 80.565605 -85.766754 81.98987C -85.78557 85.855774 -85.76673 89.72067 -85.74009 93.5865C -85.71746 97.63033 -85.72568 101.67412 -85.72958 105.718C -85.731346 112.50805 -85.70919 119.2977 -85.67236 126.08765C -85.63001 133.94151 -85.62301 141.7948 -85.63549 149.64877C -85.64687 157.20084 -85.63559 164.7527 -85.613144 172.30473C -85.60384 175.52042 -85.602104 178.73598 -85.606316 181.95166C -85.61008 185.73657 -85.593895 189.52087 -85.562195 193.30563C -85.553696 194.696 -85.55201 196.08641 -85.55757 197.47678C -85.564 199.37167 -85.54343 201.26662 -85.52139 203.16139C -85.51729 204.22272 -85.51318 205.28403 -85.50895 206.37752C -85.22997 209.10555 -85.22997 209.10555 -83.18745 210.84436C -79.927505 212.5666 -76.776245 212.3552 -73.18579 212.25146C -72.39796 212.2396 -71.61012 212.2277 -70.798416 212.21547C -60.534275 212.02208 -50.279015 211.49617 -40.027344 210.97656C -37.84853 210.86897 -35.6697 210.76167 -33.49086 210.65463C -28.948269 210.43048 -24.405846 210.20322 -19.863525 209.97363C -14.118274 209.68344 -8.37269 209.40056 -2.6269598 209.12003C 1.8683641 208.89977 6.363495 208.67575 10.85858 208.45067C 12.973044 208.34517 15.087561 208.24072 17.202135 208.13744C 28.663528 207.5753 40.113075 206.90282 51.556396 206.04443C 52.597855 205.97084 53.639317 205.89725 54.712334 205.82143C 63.955116 205.39525 63.955116 205.39525 71.91048 201.29315C 73.38291 198.19408 73.400665 195.7404 73.43237 192.30957C 73.44765 191.30525 73.44765 191.30525 73.46322 190.28064C 73.49352 188.04639 73.502655 185.81256 73.51172 183.57812C 73.52882 181.9859 73.54716 180.3937 73.566666 178.80151C 73.61525 174.50858 73.64501 170.2157 73.670364 165.92256C 73.69319 162.3627 73.72551 158.80296 73.75786 155.24318C 73.84017 146.15765 73.90156 137.07211 73.95216 127.986374C 73.98611 122.005135 74.03056 116.02407 74.08252 110.04297C 74.14762 102.50782 74.19593 94.97274 74.22916 87.43738C 74.24172 84.6493 74.26046 81.86137 74.28427 79.073364C 74.31634 75.22077 74.32372 71.368355 74.328125 67.515625C 74.340416 66.3819 74.35271 65.24816 74.36537 64.08008C 74.36243 63.051083 74.35948 62.02209 74.356445 60.961914C 74.36009 60.069904 74.36374 59.17789 74.36749 58.25885C 74.14389 55.97747 74.14389 55.97747 72.89166 54.204605C 70.04246 52.39024 67.72818 52.686836 64.37695 52.764404C 63.715 52.77671 63.05305 52.78902 62.37104 52.8017C 59.56656 52.86329 56.764378 52.973045 53.96103 53.071903z" stroke="none"  fill="#151514" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 904 467)" d="M0 0C 1.5 4.5 1.5 4.5 3 9C 2.0409374 10.175625 2.0409374 10.175625 1.0625 11.375C -1.2279122 14.29007 -1.4105227 15.407927 -1 19C -0.34 19 0.32 19 1 19C 1.33 18.34 1.66 17.68 2 17C 2.33 18.98 2.66 20.96 3 23C 5.221898 20.778101 5.925989 18.915173 7 16C 7.66 16 8.32 16 9 16C 9 16.66 9 17.32 9 18C 9.99 18.33 10.98 18.66 12 19C 14 22 14 22 14.4375 24.125C 14.623125 24.74375 14.80875 25.3625 15 26C 15.99 26.33 16.98 26.66 18 27C 18 27.99 18 28.98 18 30C 21.277735 29.248499 21.277735 29.248499 23 26C 23.435984 33.532845 23.17109 40.572815 21.507812 47.9375C 21.335672 48.701744 21.163532 49.465984 20.986176 50.253387C 18.325699 61.44818 14.393532 72.266945 10.625 83.125C 9.823518 85.456055 9.022745 87.78736 8.222656 90.1189C 6.2127247 95.96655 4.1902494 101.80978 2.1622314 107.651184C 1.0471225 110.86422 -0.06528943 114.07818 -1.1767578 117.29248C -3.1113207 122.884766 -5.0531626 128.47447 -7 134.0625C -7.294772 134.90984 -7.589545 135.75717 -7.8932495 136.63019C -8.717372 138.99545 -9.545398 141.35931 -10.375 143.72266C -10.613769 144.40868 -10.852537 145.0947 -11.098541 145.8015C -12.222224 148.98756 -13.49004 151.98317 -15 155C -15 155.66 -15 156.32 -15 157C -16.32 157 -17.64 157 -19 157C -19 157.99 -19 158.98 -19 160C -25.435 162.475 -25.435 162.475 -32 165C -31.34 165.33 -30.68 165.66 -30 166C -30.33 166.99 -30.66 167.98 -31 169C -32.485 168.01 -32.485 168.01 -34 167C -36.698402 167.18527 -39.269485 167.47905 -41.9375 167.875C -51.980465 169.2001 -61.88733 169.19 -72 169C -72.33 169.66 -72.66 170.32 -73 171C -74.98 171 -76.96 171 -79 171C -79 170.34 -79 169.68 -79 169C -81.15784 167.87657 -81.15784 167.87657 -84.1875 166.8125C -85.79193 166.20903 -87.39605 165.60474 -89 165C -90.11246 164.5875 -91.22492 164.175 -92.37109 163.75C -111.05648 156.68073 -130.79768 145.03117 -141 127C -140.67 126.01 -140.34 125.02 -140 124C -136.36432 125.47025 -134.73454 127.364525 -132.4375 130.5C -125.77747 139.00868 -118.40588 145.40297 -109.12891 150.8086C -108.44026 151.21558 -107.751625 151.62256 -107.042114 152.04187C -105.36418 153.03262 -103.68248 154.01698 -102 155C -102.518326 153.60323 -102.518326 153.60323 -103.04712 152.17822C -110.716 131.4979 -118.25878 110.77728 -125.63916 89.99219C -128.43097 82.13794 -131.2386 74.29244 -134.16797 66.48828C -134.4085 65.8465 -134.64902 65.204704 -134.89682 64.54347C -135.99063 61.629658 -137.09308 58.71953 -138.20801 55.81372C -138.58151 54.825897 -138.95502 53.83807 -139.33984 52.820312C -139.66331 51.97743 -139.98679 51.13454 -140.32007 50.266113C -141 48 -141 48 -141 44C -140.01 43.34 -139.02 42.68 -138 42C -138 41.34 -138 40.68 -138 40C -137.34 40 -136.68 40 -136 40C -136.99 36.535 -136.99 36.535 -138 33C -138.66 33 -139.32 33 -140 33C -139.97937 32.05125 -139.95876 31.1025 -139.9375 30.125C -139.77448 27.093338 -139.77448 27.093338 -141 25C -140.01 25 -139.02 25 -138 25C -138.33 22.69 -138.66 20.38 -139 18C -132.62477 14.812384 -123.47783 15.847163 -116.5 15.875C -115.49969 15.871133 -114.499374 15.867266 -113.46875 15.863281C -112.52 15.864571 -111.57125 15.865859 -110.59375 15.8671875C -109.30501 15.868879 -109.30501 15.868879 -107.990234 15.870605C -106 16 -106 16 -105 17C -104.53815 21.387634 -104.53815 21.387634 -106.22266 23.621094C -108.440994 25.34213 -109.90883 25.486147 -112.6875 25.6875C -113.899864 25.786114 -113.899864 25.786114 -115.13672 25.886719C -116.059044 25.942793 -116.059044 25.942793 -117 26C -116.72551 26.82347 -116.45102 27.646938 -116.16821 28.495361C -105.77881 59.663574 -95.389404 90.83179 -85 122C -82.0562 115.67364 -79.59683 109.27957 -77.36328 102.671875C -77.038376 101.71984 -76.71346 100.76781 -76.37871 99.78693C -75.698616 97.79093 -75.02056 95.794235 -74.34448 93.796875C -73.31008 90.74287 -72.26782 87.69163 -71.22461 84.640625C -70.562035 82.69286 -69.89991 80.74496 -69.23828 78.796875C -68.92789 77.888466 -68.617485 76.980064 -68.29768 76.04413C -68.01036 75.19264 -67.72303 74.341156 -67.427 73.46387C -67.17481 72.72046 -66.92262 71.97705 -66.662796 71.21112C -64.957016 65.520546 -65.12774 62.75598 -67.421875 57.160156C -67.81285 56.19329 -67.81285 56.19329 -68.21173 55.206894C -69.04581 53.153614 -69.897835 51.108326 -70.75 49.0625C -71.308426 47.691216 -71.86573 46.319473 -72.421875 44.947266C -75.35543 37.72977 -75.35543 37.72977 -78.68457 30.689209C -80 28 -80 28 -80 26C -80.68707 25.974218 -81.37414 25.948437 -82.08203 25.921875C -82.983086 25.865156 -83.88414 25.808437 -84.8125 25.75C -86.15248 25.680391 -86.15248 25.680391 -87.51953 25.609375C -90 25 -90 25 -91.83594 23.085938C -93 21 -93 21 -92.8125 18.875C -92 17 -92 17 -90 15C -87.68494 14.746033 -87.68494 14.746033 -84.75586 14.741211C -83.66132 14.734906 -82.56679 14.728602 -81.43909 14.722107C -79.65939 14.72818 -79.65939 14.72818 -77.84375 14.734375C -76.63054 14.732462 -75.41733 14.730548 -74.16736 14.728577C -71.59839 14.727214 -69.02941 14.730918 -66.46045 14.739258C -62.516445 14.749955 -58.572895 14.739367 -54.628906 14.7265625C -52.13802 14.727884 -49.647133 14.730447 -47.15625 14.734375C -45.969788 14.730327 -44.78333 14.726278 -43.560913 14.722107C -41.91911 14.731564 -41.91911 14.731564 -40.24414 14.741211C -38.794247 14.743598 -38.794247 14.743598 -37.315063 14.746033C -35 15 -35 15 -33 17C -32.75 19.5 -32.75 19.5 -33 22C -35.680927 24.680927 -37.391674 24.386703 -41.125 24.625C -42.220703 24.699766 -43.316406 24.77453 -44.445312 24.851562C -45.28836 24.900547 -46.131405 24.949532 -47 25C -46.735985 25.786812 -46.47197 26.573624 -46.19995 27.384277C -42.446266 38.58276 -38.718586 49.789818 -35 61C -34.61155 62.17053 -34.2231 63.341057 -33.822876 64.54706C -31.46163 71.665085 -29.105703 78.78483 -26.759256 85.907745C -25.680046 89.1836 -24.598877 92.45878 -23.512787 95.73236C -22.279236 99.45091 -21.055273 103.17258 -19.832031 106.89453C -19.459623 108.01299 -19.087215 109.13145 -18.703522 110.2838C -16.986067 115.53517 -15.523017 120.491905 -15 126C -14.34 126 -13.68 126 -13 126C -12.826702 124.51246 -12.826702 124.51246 -12.649902 122.99487C -12.0602 118.88916 -10.887133 115.01661 -9.6015625 111.08203C -9.365936 110.34354 -9.130309 109.605034 -8.887543 108.84416C -8.13862 106.49855 -7.3824925 104.15536 -6.625 101.8125C 0.04025367 82.77814 0.04025367 82.77814 2.4375 62.9375C 2.4270263 62.161484 2.4165528 61.385468 2.4057617 60.585938C 2.0008821 47.73705 -4.7288876 37.66926 -11.031982 26.81836C -13.838328 21.546818 -13.554189 15.811285 -13 10C -10.226321 4.2471857 -6.5941477 0 0 0z" stroke="none"  fill="#F5F5F2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 441 240)" d="M0 0C 1.4386129 0.08746284 2.8759751 0.19565348 4.3125 0.3125C 5.113008 0.3705078 5.9135156 0.4285156 6.7382812 0.48828125C 9.51877 1.1173729 10.205653 1.8034925 12 4C 13.13501 6.647949 13.13501 6.647949 14.191406 9.8046875C 14.57877 10.945508 14.966133 12.0863285 15.365234 13.261719C 15.760332 14.454102 16.15543 15.646484 16.5625 16.875C 17.35456 19.22687 18.147497 21.578442 18.941406 23.929688C 19.288889 24.973667 19.636372 26.017647 19.994385 27.093262C 20.649607 28.987186 21.356787 30.864162 22.121582 32.716553C 23 35 23 35 23.113281 37.60547C 21.641636 40.770798 19.714327 41.355385 16.531006 42.520264C 15.757648 42.7817 14.984292 43.04314 14.1875 43.3125C 12.525644 43.918182 10.8642 44.524994 9.203125 45.132812C 8.340097 45.44122 7.4770703 45.74963 6.5878906 46.067383C -15.591806 54.060444 -37.486855 63.67236 -57 77C -57.679337 77.45842 -58.358673 77.91685 -59.058594 78.38916C -69.841995 85.69763 -80.021576 93.63329 -90 102C -90.83531 102.69094 -91.67062 103.381874 -92.53125 104.09375C -96.43065 107.39324 -99.83477 110.91366 -103.12891 114.81641C -106.037254 118.210495 -109.11953 121.44587 -112.17969 124.703125C -113.98451 126.98046 -114.42479 128.23521 -115 131C -114.07059 131.79665 -113.141174 132.59328 -112.18359 133.41406C -104.74609 139.78906 -104.74609 139.78906 -102.882324 141.3916C -101.60926 142.4794 -100.32941 143.5593 -99.04346 144.63184C -98.42197 145.15294 -97.800476 145.67404 -97.16016 146.21094C -96.3211 146.90776 -96.3211 146.90776 -95.46509 147.61865C -94 149 -94 149 -92 152C -92 154.4375 -92 154.4375 -93 157C -97.9612 161.08899 -103.052444 162.26761 -109.25 163.625C -111.04832 164.02745 -112.84528 164.43602 -114.640625 164.85156C -115.429855 165.0251 -116.219086 165.19864 -117.03223 165.37744C -119.206215 165.89896 -119.206215 165.89896 -121 168C -123.01327 167.96721 -125.02651 167.93188 -127.03955 167.8872C -133.56982 168.13602 -140.03337 169.69377 -146.4375 170.9375C -170.9905 175.65765 -170.9905 175.65765 -176.4375 173.875C -182.76785 169.24303 -184.11926 161.28004 -185.35568 153.86768C -186.09976 148.82231 -186.71368 143.76173 -187.30762 138.69678C -187.62123 136.03207 -187.95021 133.36961 -188.28125 130.70703C -193.69016 86.23576 -193.69016 86.23576 -188 78C -182.32976 78.40912 -178.79622 80.03667 -174.375 83.4375C -173.78107 83.87691 -173.18713 84.316315 -172.5752 84.76904C -169.4425 87.099945 -166.35822 89.492966 -163.27344 91.88672C -162.3852 92.575195 -162.3852 92.575195 -161.479 93.27759C -160.34357 94.16244 -159.21284 95.05339 -158.0874 95.95093C -155.37337 98.29802 -155.37337 98.29802 -152 99C -151.01 99.99 -151.01 99.99 -150 101C -149.35419 100.28199 -148.70836 99.56399 -148.04297 98.82422C -135.57167 85.07747 -122.778595 71.29102 -108 60C -106.92761 59.140606 -105.85743 58.27845 -104.78906 57.414062C -73.02906 31.830498 -37.60957 14.738531 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#F3F0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 235.52881 424.73535)" d="M0 0C 8.247383 -0.40916124 12.909548 1.3560735 19.471191 6.2646484C 20.2348 6.802671 20.998407 7.3406935 21.785156 7.8950195C 24.104235 9.545998 26.384092 11.240498 28.658691 12.952148C 30.311901 14.18841 31.965551 15.42408 33.61963 16.65918C 34.839245 17.571112 34.839245 17.571112 36.083496 18.501465C 38.309574 20.145304 40.557198 21.750055 42.822754 23.338867C 51.457664 29.41806 58.582577 34.693527 61.47119 45.26465C 61.75379 48.518795 61.72701 51.757847 61.70166 55.02246C 61.701565 56.03055 61.70147 57.038635 61.70137 58.07727C 61.697567 61.51484 61.678333 64.95214 61.65869 68.38965C 61.651726 70.853676 61.645233 73.31771 61.63916 75.78174C 61.497 118.27688 60.944805 160.77222 60.47119 203.26465C 59.84181 203.56355 59.21242 203.86246 58.563965 204.17041C 56.334778 205.33597 55.258358 206.42549 53.72119 208.38965C 52.97869 209.3384 52.23619 210.28716 51.47119 211.26465C 50.48119 210.93465 49.49119 210.60464 48.47119 210.26465C 48.47119 209.60464 48.47119 208.94464 48.47119 208.26465C 47.893692 208.45027 47.316193 208.6359 46.72119 208.82715C 44.264267 209.30489 42.827217 209.02194 40.47119 208.26465C 40.316505 209.40933 40.316505 209.40933 40.15869 210.57715C 39.118877 214.64188 39.118877 214.64188 36.47119 216.26465C 33.005634 216.65523 29.655033 216.85657 26.471191 215.26465C 26.471191 215.92465 26.471191 216.58466 26.471191 217.26465C 30.10119 217.26465 33.73119 217.26465 37.47119 217.26465C 37.47119 217.92465 37.47119 218.58466 37.47119 219.26465C 21.096104 220.83533 4.9201407 221.55365 -11.528809 221.26465C -11.528809 220.93465 -11.528809 220.60464 -11.528809 220.26465C -14.168809 219.93465 -16.80881 219.60464 -19.528809 219.26465C -19.528809 218.93465 -19.528809 218.60464 -19.528809 218.26465C -17.21881 217.93465 -14.908809 217.60464 -12.528809 217.26465C -12.8588085 216.60464 -13.188808 215.94464 -13.528809 215.26465C -12.538809 215.26465 -11.548809 215.26465 -10.528809 215.26465C -10.528809 214.60464 -10.528809 213.94464 -10.528809 213.26465C -11.188808 212.93465 -11.848808 212.60464 -12.528809 212.26465C -11.868809 211.27464 -11.208809 210.28465 -10.528809 209.26465C -13.498809 208.93465 -16.46881 208.60464 -19.528809 208.26465C -19.528809 207.93465 -19.528809 207.60464 -19.528809 207.26465C -14.327159 206.56987 -9.187632 206.07207 -3.942871 205.7998C -2.5107281 205.72275 -1.0785997 205.64543 0.35351562 205.56787C 1.0885834 205.52882 1.8236511 205.48976 2.5809937 205.44952C 17.978996 204.63266 17.978996 204.63266 33.34619 203.38965C 34.038902 203.33559 34.731613 203.28152 35.445312 203.22583C 38.391834 202.90929 40.24992 202.43141 42.632874 200.63544C 45.07753 197.48268 44.987755 195.5422 45.01709 191.57422C 45.02742 190.90468 45.037746 190.23512 45.04839 189.54529C 45.078 187.31088 45.079304 185.07736 45.080566 182.84277C 45.095745 181.2505 45.112625 179.65825 45.13109 178.06602C 45.175697 173.77301 45.195488 169.48021 45.21002 165.18701C 45.228645 160.73143 45.268032 156.27602 45.304443 151.82056C 45.367542 143.6321 45.40837 135.44368 45.438835 127.25503C 45.46148 121.26711 45.493652 115.27933 45.533203 109.291504C 45.587227 101.04238 45.62492 92.793304 45.651077 84.54405C 45.6587 82.45021 45.669197 80.3564 45.680298 78.26257C 45.69977 74.40257 45.701935 70.54267 45.70166 66.68262C 45.708786 65.543724 45.71591 64.40483 45.72325 63.23143C 45.718727 61.687824 45.718727 61.687824 45.71411 60.113037C 45.715538 59.218807 45.71696 58.324574 45.71843 57.403244C 45.67077 55.05008 45.67077 55.05008 43.47119 53.26465C 40.480305 52.98708 40.480305 52.98708 36.943604 53.089355C 35.945503 53.100945 35.945503 53.100945 34.92724 53.112766C 26.91466 53.235947 18.913946 53.648907 10.912598 54.069336C 9.2362585 54.15456 7.559902 54.23944 5.8835297 54.324005C 1.4847796 54.546783 -2.9137282 54.774025 -7.312195 55.00232C -11.644033 55.226456 -15.976074 55.4466 -20.308105 55.666992C -23.755375 55.84269 -27.202639 56.018505 -30.649902 56.194336C -31.931473 56.259697 -31.931473 56.259697 -33.238934 56.326378C -35.836708 56.45895 -38.43447 56.591717 -41.032227 56.72461C -41.890587 56.768513 -42.74895 56.812416 -43.63332 56.857647C -49.003498 57.133057 -54.37334 57.414246 -59.74303 57.698994C -63.342537 57.8892 -66.94221 58.0761 -70.541916 58.262455C -72.23 58.35063 -73.91801 58.440258 -75.60593 58.53152C -86.26195 59.10709 -96.85629 59.368073 -107.52881 59.26465C -110.58151 59.106533 -110.58151 59.106533 -113.52881 60.26465C -115.71979 56.930553 -116.96335 54.22286 -117.52881 50.26465C -116.53881 50.26465 -115.548805 50.26465 -114.52881 50.26465C -114.85881 48.614647 -115.18881 46.96465 -115.52881 45.26465C -114.53881 45.26465 -113.548805 45.26465 -112.52881 45.26465C -113.18881 44.60465 -113.84881 43.94465 -114.52881 43.26465C -113.389084 43.206448 -112.24937 43.14825 -111.0751 43.088287C -100.166245 42.53106 -89.257454 41.972424 -78.34873 41.412514C -72.74473 41.124924 -67.14071 40.837776 -61.53662 40.551758C -55.046967 40.22048 -48.5574 39.887466 -42.06787 39.55371C -41.005505 39.49923 -39.94314 39.44475 -38.84858 39.38862C -28.343431 38.848812 -17.839643 38.298798 -7.340332 37.654297C -6.5611787 37.606747 -5.7820253 37.559196 -4.9792614 37.510204C -1.4680068 37.29494 2.042591 37.07372 5.552826 36.842194C 14.539538 36.27762 23.471916 36.17585 32.47119 36.26465C 28.871214 32.61096 25.171215 29.311275 21.096191 26.202148C 20.557364 25.78127 20.018536 25.36039 19.463379 24.926758C 18.65127 24.303173 18.65127 24.303173 17.822754 23.666992C 17.346607 23.300817 16.870459 22.934643 16.379883 22.557373C 15.750014 22.130774 15.120147 21.704174 14.471191 21.264648C 13.462983 20.56219 12.454776 19.859732 11.416016 19.135986C 6.736186 16.519217 2.2465317 16.769604 -2.9975586 16.920898C -4.034891 16.936165 -5.072224 16.951433 -6.140991 16.967163C -20.11413 17.232668 -34.07457 18.019306 -48.02881 18.764648C -66.53834 19.753304 -84.985756 20.523796 -103.52881 20.264648C -103.52881 19.934649 -103.52881 19.604649 -103.52881 19.264648C -87.68881 18.274649 -87.68881 18.274649 -71.52881 17.264648C -71.52881 16.604649 -71.52881 15.944649 -71.52881 15.264648C -70.20881 15.264648 -68.88881 15.264648 -67.52881 15.264648C -67.52881 13.944649 -67.52881 12.624648 -67.52881 11.264648C -68.18881 10.604649 -68.84881 9.944649 -69.52881 9.264648C -69.52881 8.604649 -69.52881 7.9446483 -69.52881 7.2646484C -68.868805 7.2646484 -68.20881 7.2646484 -67.52881 7.2646484C -67.52881 6.6046486 -67.52881 5.9446483 -67.52881 5.2646484C -69.50881 5.2646484 -71.48881 5.2646484 -73.52881 5.2646484C -73.52881 4.9346485 -73.52881 4.6046486 -73.52881 4.2646484C -66.26881 4.2646484 -59.00881 4.2646484 -51.52881 4.2646484C -51.52881 4.5946484 -51.52881 4.9246483 -51.52881 5.2646484C -50.229435 5.4502735 -48.930058 5.6358986 -47.59131 5.8271484C -46.86041 5.9315624 -46.129513 6.0359764 -45.376465 6.1435547C -44.461876 6.203496 -44.461876 6.203496 -43.52881 6.2646484C -43.198807 5.9346485 -42.86881 5.6046486 -42.52881 5.2646484C -41.394432 5.1202736 -40.26006 4.9758983 -39.09131 4.8271484C -35.48822 4.616173 -35.48822 4.616173 -33.52881 2.2646484C -30.75415 1.8540039 -30.75415 1.8540039 -27.228027 1.6318359C -25.93961 1.5480468 -24.651192 1.4642578 -23.32373 1.3779297C -22.647537 1.3387744 -21.971342 1.2996192 -21.274658 1.2592773C -19.261755 1.1422898 -17.249744 1.0102819 -15.237793 0.8779297C -10.158834 0.56128013 -5.0745516 0.3805738 0 0z" stroke="none"  fill="#131312" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 250.9375 498.75)" d="M0 0C 0.8576294 -0.009104 1.7152588 -0.01820801 2.598877 -0.02758789C 3.400271 -0.02364014 4.201665 -0.01969238 5.0273438 -0.015625C 5.7349586 -0.01336914 6.442573 -0.01111328 7.171631 -0.00878906C 9.0625 0.25 9.0625 0.25 12.0625 2.25C 12.5 4.6875 12.5 4.6875 12.0625 7.25C 9.228699 10.192794 5.8787737 11.021474 2.0625 12.25C 2.0537007 13.9998045 2.0537007 13.9998045 2.0447235 15.784958C 1.9875196 26.776497 1.9147701 37.767838 1.8267851 48.759174C 1.7820506 54.409992 1.7428012 60.060738 1.7160645 65.71167C 1.6900624 71.164764 1.6496816 76.617584 1.5993137 82.0705C 1.5826241 84.151276 1.5711229 86.2321 1.5648861 88.312935C 1.5552679 91.226746 1.5272355 94.13983 1.494873 97.05347C 1.4970257 98.344955 1.4970257 98.344955 1.4992218 99.66254C 1.4254391 104.35734 1.0732006 107.42329 -1.9375 111.25C -6.6905537 115.637436 -10.30797 116.73043 -16.796875 116.57422C -23.104462 115.61887 -27.044054 111.46854 -30.75 106.5625C -34.45128 101.17818 -37.807713 95.59228 -41.167236 89.991455C -46.21106 81.65076 -51.65884 73.58433 -57.128418 65.51953C -63.36398 56.274223 -69.181015 46.79902 -74.9375 37.25C -74.960625 45.707672 -74.97842 54.165333 -74.98931 62.62303C -74.99454 66.54989 -75.00164 70.47672 -75.01294 74.403564C -75.02377 78.19014 -75.02978 81.9767 -75.03238 85.76329C -75.03423 87.211044 -75.03785 88.65879 -75.04323 90.10654C -75.050446 92.12757 -75.05091 94.14863 -75.051025 96.16968C -75.05436 97.89842 -75.05436 97.89842 -75.057755 99.662094C -75.23187 102.18013 -75.23187 102.18013 -73.9375 103.25C -71.13375 103.74504 -68.32211 104.15051 -65.50391 104.55469C -62.9375 105.25 -62.9375 105.25 -60.9375 108.25C -61.26164 111.5724 -61.54526 112.857765 -63.9375 115.25C -66.8833 115.660645 -66.8833 115.660645 -70.63281 115.88281C -71.29909 115.92438 -71.96536 115.965935 -72.651825 116.00876C -74.065475 116.09529 -75.47941 116.177376 -76.893555 116.25537C -79.033844 116.37342 -81.17309 116.503746 -83.3125 116.63672C -102.0552 117.773865 -102.0552 117.773865 -104.9375 116.0625C -106.32074 113.55538 -106.43066 112.068016 -105.9375 109.25C -102.791245 105.86172 -100.83273 106.06587 -95.9375 105.25C -95.9375 76.54 -95.9375 47.83 -95.9375 18.25C -98.9075 17.26 -101.8775 16.27 -104.9375 15.25C -105.9375 14.25 -105.9375 14.25 -106.25 11.375C -105.9375 8.25 -105.9375 8.25 -104.31909 6.600342C -101.28866 4.8821115 -98.99257 4.727535 -95.51953 4.5195312C -94.264626 4.438965 -93.00973 4.3583984 -91.7168 4.2753906C -90.40775 4.205137 -89.09871 4.134883 -87.75 4.0625C -86.4603 3.9806445 -85.170586 3.8987892 -83.8418 3.8144531C -81.333664 3.6619403 -78.82426 3.5289533 -76.313965 3.4174805C -74.26909 3.31605 -72.22489 3.171103 -70.18994 2.9458008C -67.83984 2.7460938 -67.83984 2.7460938 -63.9375 3.25C -61.522644 5.2931004 -60.186623 7.7438903 -58.6875 10.5C -57.755447 12.071227 -56.821785 13.641502 -55.88672 15.2109375C -55.168148 16.45182 -55.168148 16.45182 -54.43506 17.717773C -51.93384 21.94709 -49.205353 26.024546 -46.5 30.125C -41.6363 37.580505 -36.86207 45.08922 -32.13208 52.630127C -28.627127 58.203907 -25.005495 63.677887 -21.265625 69.09766C -19.9375 71.25 -19.9375 71.25 -19.9375 73.25C -18.9475 73.25 -17.9575 73.25 -16.9375 73.25C -17.2675 53.45 -17.5975 33.65 -17.9375 13.25C -22.8875 12.755 -22.8875 12.755 -27.9375 12.25C -30.9375 8.25 -30.9375 8.25 -30.6875 5.5625C -29.9375 3.25 -29.9375 3.25 -28.9375 2.25C -19.342918 0.709018 -9.7121525 0.0506721 0 0z" stroke="none"  fill="#141413" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 925.6538 453.74023)" d="M0 0C 5.7261515 5.49625 10.480902 11.283939 15.049316 17.75586C 15.477285 18.252148 15.905254 18.748438 16.346191 19.259766C 17.006191 19.259766 17.666191 19.259766 18.346191 19.259766C 18.449316 19.961016 18.55244 20.662266 18.658691 21.384766C 19.438957 24.647696 20.824015 27.281591 22.346191 30.259766C 22.346191 30.919765 22.346191 31.579765 22.346191 32.259766C 23.006191 32.589767 23.666191 32.919765 24.346191 33.259766C 25.006191 33.589767 25.666191 33.919765 26.346191 34.259766C 26.558294 35.81519 26.76241 37.371895 26.939941 38.93164C 27.442612 41.812332 28.31705 44.478848 29.221191 47.259766C 37.670826 74.52062 36.71012 103.31425 24.346191 129.25977C 24.025215 129.94136 23.704239 130.62296 23.373535 131.3252C 12.784822 153.43869 -2.8741946 170.10957 -23.966309 182.5957C -26.682077 184.1354 -26.682077 184.1354 -28.653809 186.25977C -30.640562 186.96494 -32.643726 187.62415 -34.65381 188.25977C -36.507095 188.97594 -38.360252 189.69249 -40.210205 190.41724C -66.05773 200.46748 -94.619026 201.55518 -120.65381 191.25977C -121.61158 190.90141 -122.56936 190.54305 -123.55615 190.17383C -136.40977 185.14049 -147.30789 178.35353 -157.65381 169.25977C -158.49556 168.56238 -159.33733 167.865 -160.20459 167.14648C -169.76277 159.01534 -176.85223 149.14088 -183.0835 138.30664C -184.48657 136.09764 -184.48657 136.09764 -186.84912 135.46289C -187.44467 135.39586 -188.04022 135.32883 -188.65381 135.25977C -189.27881 133.38477 -189.27881 133.38477 -189.65381 131.25977C -188.6638 130.26976 -188.6638 130.26976 -187.65381 129.25977C -188.43887 127.13232 -189.23207 125.007866 -190.02881 122.884766C -190.46967 121.70141 -190.91052 120.51804 -191.36475 119.29883C -192.44884 116.21483 -192.44884 116.21483 -194.65381 114.259766C -195.34131 110.697266 -195.34131 110.697266 -195.65381 107.259766C -195.3238 106.92976 -194.9938 106.59976 -194.65381 106.259766C -194.89285 104.70907 -195.19852 103.16862 -195.52881 101.634766C -198.66641 84.73496 -197.8312 67.71894 -192.65381 51.259766C -193.64381 50.929764 -194.6338 50.599766 -195.65381 50.259766C -194.3338 50.259766 -193.01381 50.259766 -191.65381 50.259766C -191.48882 49.104767 -191.3238 47.949764 -191.15381 46.759766C -185.8956 23.373589 -165.62524 0.0346656 -145.84131 -12.552734C -144.50133 -13.366777 -144.50133 -13.366777 -143.13428 -14.197266C -140.70593 -15.633495 -140.70593 -15.633495 -138.88037 -17.19336C -96.625305 -46.54951 -35.181866 -33.03169 0 0zM-149.65381 3.2597656C -150.41951 3.8952734 -151.18521 4.5307813 -151.97412 5.185547C -170.07678 20.496101 -182.2985 43.880547 -185.65381 67.259766C -185.8418 71.49963 -185.83963 75.73667 -185.83911 79.98047C -185.8413 81.990456 -185.85945 83.99988 -185.87842 86.009766C -185.88939 93.35156 -185.49574 100.143875 -183.65381 107.259766C -183.4859 107.948044 -183.31801 108.63632 -183.14502 109.34546C -179.23299 124.59111 -172.01752 138.46384 -161.65381 150.25977C -160.69281 151.45279 -160.69281 151.45279 -159.7124 152.66992C -142.52977 173.24344 -115.65846 185.54588 -89.27002 187.97314C -63.85055 189.80623 -36.336544 181.74852 -16.415527 165.68945C -4.4588213 155.17026 6.7961607 143.01457 13.346191 128.25977C 13.346191 127.59976 13.346191 126.939766 13.346191 126.259766C 12.686192 126.259766 12.026192 126.259766 11.346191 126.259766C 11.676191 124.939766 12.006191 123.61977 12.346191 122.259766C 12.961074 122.21207 13.575957 122.164375 14.209473 122.115234C 16.61019 121.43099 16.61019 121.43099 17.57666 118.74805C 17.89248 117.70004 18.208302 116.65203 18.533691 115.572266C 18.857246 114.53715 19.180801 113.50203 19.51416 112.43555C 20.861588 107.41645 20.861588 107.41645 21.346191 102.259766C 21.668457 101.15246 21.990723 100.04516 22.322754 98.9043C 23.656046 93.59591 23.729511 88.39864 23.721191 82.947266C 23.720304 81.89926 23.71942 80.85125 23.718506 79.771484C 23.527412 66.982735 21.012592 55.133015 16.346191 43.259766C 15.686192 43.919765 15.026192 44.579765 14.346191 45.259766C 14.0161915 44.269764 13.686192 43.279766 13.346191 42.259766C 13.0161915 41.929764 12.686192 41.599766 12.346191 41.259766C 11.159751 36.63265 11.159751 36.63265 12.346191 34.259766C 11.176511 32.052765 9.965311 30.027355 8.596191 27.947266C 8.1944065 27.331497 7.792622 26.715727 7.378662 26.081299C 4.327513 21.510693 1.0444889 17.173689 -2.4663086 12.947266C -2.92167 12.390633 -3.3770313 11.833999 -3.8461914 11.260498C -6.219217 8.486294 -7.901959 6.5833516 -11.653809 6.2597656C -11.653809 5.269766 -11.653809 4.2797656 -11.653809 3.2597656C -24.32702 -8.404762 -44.37743 -19.157524 -61.71631 -19.865234C -62.355682 -19.823984 -62.99506 -19.782734 -63.65381 -19.740234C -63.65381 -20.070234 -63.65381 -20.400234 -63.65381 -20.740234C -64.60256 -20.71961 -65.55131 -20.698984 -66.52881 -20.677734C -69.65381 -20.740234 -69.65381 -20.740234 -71.65381 -21.740234C -99.620125 -24.231161 -128.62183 -15.48939 -149.65381 3.2597656zM13.346191 39.259766C 14.346191 41.259766 14.346191 41.259766 14.346191 41.259766zM-194.65381 108.259766C -193.65381 110.259766 -193.65381 110.259766 -193.65381 110.259766zM-193.65381 111.259766C -192.65381 113.259766 -192.65381 113.259766 -192.65381 113.259766zM12.346191 123.259766C 12.346191 123.91977 12.346191 124.579765 12.346191 125.259766C 13.006191 125.259766 13.666191 125.259766 14.346191 125.259766C 14.346191 124.59976 14.346191 123.939766 14.346191 123.259766C 13.686192 123.259766 13.026192 123.259766 12.346191 123.259766zM-186.65381 131.25977C -185.65381 133.25977 -185.65381 133.25977 -185.65381 133.25977z" stroke="none"  fill="#136791" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 820.1875 436.375)" d="M0 0C 0.598125 0.20625 1.19625 0.4125 1.8125 0.625C 1.1525 0.955 0.4925 1.285 -0.1875 1.625C 1.66875 1.9034375 1.66875 1.9034375 3.5625 2.1875C 4.2585936 2.291914 4.9546876 2.3963282 5.671875 2.5039062C 7.776082 2.737124 7.776082 2.737124 9.598145 2.1342773C 12.261974 1.5216258 14.650145 1.5471352 17.382812 1.5976562C 18.44371 1.6099024 19.50461 1.6221484 20.597656 1.6347656C 22.832094 1.6700462 25.066475 1.7091354 27.300781 1.7519531C 28.359102 1.7629101 29.417421 1.7738672 30.507812 1.7851562C 31.962116 1.8104138 31.962116 1.8104138 33.4458 1.8361816C 36.002014 1.8631291 36.002014 1.8631291 37.8125 -0.375C 38.3668 -0.08625 38.921093 0.2025 39.492188 0.5C 40.57887 1.056875 40.57887 1.056875 41.6875 1.625C 42.766445 2.181875 42.766445 2.181875 43.867188 2.75C 45.76099 3.7617373 45.76099 3.7617373 47.8125 3.625C 48.1425 2.965 48.4725 2.305 48.8125 1.625C 49.605274 2.5453906 49.605274 2.5453906 50.414062 3.484375C 53.25428 6.0192924 55.558884 6.7623343 59.1875 7.875C 72.76246 12.522031 82.70355 19.634281 92.8125 29.625C 93.52922 30.269531 94.24594 30.914062 94.984375 31.578125C 99.43207 35.67 102.82153 40.263294 106.1875 45.25C 106.61692 45.883896 107.04634 46.51779 107.48877 47.1709C 109.93019 50.90277 111.440384 54.354767 112.8125 58.625C 113.4725 58.955 114.1325 59.285 114.8125 59.625C 115.739914 61.955425 116.55386 64.239815 117.3125 66.625C 117.5339 67.308365 117.755295 67.99173 117.9834 68.6958C 120.29669 75.95363 121.9142 82.97087 122.46875 90.58203C 122.85698 92.88933 123.766495 94.54564 124.8125 96.625C 124.8125 97.615 124.8125 98.605 124.8125 99.625C 124.1525 99.625 123.4925 99.625 122.8125 99.625C 122.79832 100.55957 122.78414 101.49414 122.76953 102.45703C 121.81365 128.7824 111.48992 153.04318 92.13672 171.125C 91.2666 171.95 90.396484 172.775 89.5 173.625C 88.61699 174.45 87.733986 175.275 86.82422 176.125C 84.57245 178.46024 84.57245 178.46024 84.51953 181.4375C 84.61621 182.15938 84.71289 182.88126 84.8125 183.625C 84.4825 182.305 84.1525 180.985 83.8125 179.625C 82.859886 180.26309 81.907265 180.90117 80.92578 181.5586C 79.65913 182.39365 78.39219 183.22826 77.125 184.0625C 76.49916 184.48338 75.87332 184.90425 75.228516 185.33789C 71.77869 187.59563 69.00959 189.18362 64.8125 189.625C 64.8125 188.965 64.8125 188.305 64.8125 187.625C 66.1325 187.625 67.4525 187.625 68.8125 187.625C 68.585625 187.06813 68.35875 186.51125 68.125 185.9375C 67.68704 182.69664 69.12428 181.37743 70.8125 178.625C 71.48765 176.97305 72.11254 175.29984 72.69092 173.61157C 73.02957 172.63213 73.36822 171.6527 73.71713 170.64357C 74.07603 169.58685 74.43492 168.53014 74.80469 167.4414C 75.18735 166.32979 75.57001 165.21817 75.964264 164.07286C 77.211075 160.44635 78.44962 156.81708 79.6875 153.1875C 80.543236 150.68867 81.39936 148.18997 82.25586 145.6914C 83.546875 141.92413 84.83714 138.15663 86.12482 134.38821C 88.672325 126.93498 91.24759 119.49226 93.85156 112.05859C 94.732666 109.53081 95.61275 107.00268 96.49225 104.474335C 97.02979 102.934715 97.570496 101.396194 98.114685 99.85892C 102.7638 86.71314 106.604996 73.743225 105.8125 59.625C 104.8225 60.615 104.8225 60.615 103.8125 61.625C 102.6371 60.698475 101.4724 59.758366 100.3125 58.8125C 99.66281 58.29043 99.01312 57.76836 98.34375 57.23047C 96.8125 55.625 96.8125 55.625 96.8125 52.625C 96.1525 51.965 95.4925 51.305 94.8125 50.625C 94.11501 49.307526 93.4429 47.975857 92.8125 46.625C 92.1525 46.625 91.4925 46.625 90.8125 46.625C 90.4825 48.275 90.1525 49.925 89.8125 51.625C 90.8025 52.285 91.7925 52.945 92.8125 53.625C 90.8325 53.625 88.8525 53.625 86.8125 53.625C 85.8225 51.645 85.8225 51.645 84.8125 49.625C 84.1525 49.625 83.4925 49.625 82.8125 49.625C 82.0625 47.4375 82.0625 47.4375 81.8125 44.625C 83.75 41.8125 83.75 41.8125 85.8125 39.625C 85.4825 39.068127 85.1525 38.51125 84.8125 37.9375C 83.68154 35.322155 83.65587 33.444336 83.8125 30.625C 83.1525 30.295 82.4925 29.965 81.8125 29.625C 83.1325 29.625 84.4525 29.625 85.8125 29.625C 85.8125 28.965 85.8125 28.305 85.8125 27.625C 84.959785 27.230547 84.959785 27.230547 84.08984 26.828125C 81.65014 25.539223 79.58305 24.028244 77.375 22.375C 61.942966 11.475136 43.324173 6.20225 24.5625 6.3125C 23.060339 6.319872 23.060339 6.319872 21.527832 6.3273926C -3.8632855 6.7255964 -27.024458 17.817795 -45.1875 35.3125C -46.204845 36.400707 -47.20615 37.504223 -48.1875 38.625C -48.67992 39.16383 -49.172344 39.702656 -49.679688 40.257812C -53.64456 45.073902 -53.89857 49.5472 -54.1875 55.625C -54.670265 58.46124 -55.276573 60.892223 -56.1875 63.625C -55.5275 63.625 -54.8675 63.625 -54.1875 63.625C -52.1875 66.625 -52.1875 66.625 -52.1875 70.625C -52.8475 70.625 -53.5075 70.625 -54.1875 70.625C -53.8575 71.615 -53.5275 72.605 -53.1875 73.625C -55.1675 74.12 -55.1675 74.12 -57.1875 74.625C -51.47312 91.31408 -45.586826 107.932365 -39.52942 124.49991C -37.343987 130.47856 -35.166714 136.46019 -32.98828 142.4414C -32.567554 143.59608 -32.146824 144.75075 -31.713348 145.94041C -28.43722 154.93318 -25.170698 163.9292 -21.9375 172.9375C -21.537971 174.04335 -21.138443 175.14922 -20.726807 176.28857C -20.37868 177.26198 -20.030552 178.23538 -19.671875 179.23828C -19.380224 180.0508 -19.088573 180.86331 -18.788086 181.70044C -18.1875 183.625 -18.1875 183.625 -18.1875 185.625C -32.828026 180.1348 -45.36086 168.33536 -54.1875 155.625C -55.5075 155.955 -56.8275 156.285 -58.1875 156.625C -58.525234 156.0591 -58.86297 155.49321 -59.210938 154.91016C -59.65695 154.17668 -60.10297 153.4432 -60.5625 152.6875C -61.00336 151.9566 -61.444218 151.22571 -61.898438 150.47266C -63.0649 148.48727 -63.0649 148.48727 -65.1875 147.625C -65.6825 148.615 -65.6825 148.615 -66.1875 149.625C -66.1875 148.635 -66.1875 147.645 -66.1875 146.625C -65.5275 146.625 -64.8675 146.625 -64.1875 146.625C -64.1875 145.965 -64.1875 145.305 -64.1875 144.625C -65.1775 144.955 -66.1675 145.285 -67.1875 145.625C -68.3125 141.875 -68.3125 141.875 -67.1875 139.625C -68.182274 137.28943 -69.18265 134.95625 -70.1875 132.625C -70.1875 131.305 -70.1875 129.985 -70.1875 128.625C -71.1775 128.955 -72.1675 129.285 -73.1875 129.625C -73.5175 128.965 -73.8475 128.305 -74.1875 127.625C -73.1975 127.625 -72.2075 127.625 -71.1875 127.625C -71.63712 125.66465 -72.09748 123.70676 -72.5625 121.75C -72.94535 120.11418 -72.94535 120.11418 -73.33594 118.44531C -73.97656 115.52091 -73.97656 115.52091 -76.1875 113.625C -75.5275 113.625 -74.8675 113.625 -74.1875 113.625C -74.1875 110.325 -74.1875 107.025 -74.1875 103.625C -74.8475 103.295 -75.5075 102.965 -76.1875 102.625C -76.1875 101.635 -76.1875 100.645 -76.1875 99.625C -76.8475 99.295 -77.5075 98.965 -78.1875 98.625C -77.8575 97.305 -77.5275 95.985 -77.1875 94.625C -76.5275 94.955 -75.8675 95.285 -75.1875 95.625C -75.6825 96.615 -75.6825 96.615 -76.1875 97.625C -75.5275 97.625 -74.8675 97.625 -74.1875 97.625C -74.25711 96.604065 -74.25711 96.604065 -74.328125 95.5625C -75.163025 73.13949 -64.12423 51.54298 -50.1875 34.625C -49.270977 33.74586 -48.354454 32.86672 -47.410156 31.960938C -44.84284 29.800175 -44.84284 29.800175 -45.1875 26.625C -44.642227 26.537344 -44.096954 26.449688 -43.535156 26.359375C -40.43621 25.389988 -38.252148 23.776556 -35.625 21.875C -34.67496 21.199532 -33.724922 20.524063 -32.746094 19.828125C -30.166628 17.607029 -28.745962 15.626481 -27.1875 12.625C -26.1975 12.955 -25.2075 13.285 -24.1875 13.625C -21.461239 12.60739 -18.88337 11.531347 -16.25 10.3125C -15.533926 9.990879 -14.817852 9.669258 -14.080078 9.337891C -10.748057 7.834004 -7.4390106 6.297448 -4.1875 4.625C -4.6825 3.14 -4.6825 3.14 -5.1875 1.625C -2.1875 -0.375 -2.1875 -0.375 0 0zM0.8125 2.625C 0.1525 3.285 -0.5075 3.945 -1.1875 4.625C -0.1975 4.625 0.7925 4.625 1.8125 4.625C 1.4825 3.965 1.1525 3.305 0.8125 2.625z" stroke="none"  fill="#EAEFED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 666 484)" d="M0 0C 2.3515625 1.5117188 2.3515625 1.5117188 4.625 3.6875C 5.9224415 4.9288673 5.9224415 4.9288673 7.2460938 6.1953125C 11.161402 10.182818 14.964729 14.259191 18.71875 18.398438C 22.325827 22.37462 26.007212 26.279205 29.6875 30.1875C 31.560928 32.186234 33.432762 34.186443 35.30371 36.1875C 36.37031 37.32717 37.438446 38.465405 38.50879 39.601562C 39.783634 40.957047 41.048504 42.321896 42.3125 43.6875C 42.989258 44.413242 43.666016 45.138985 44.36328 45.88672C 46.199276 48.257298 47.261513 50.086338 48 53C 46.99053 58.20266 42.875298 61.64284 39.3125 65.3125C 38.038223 66.65024 36.764774 67.98878 35.492188 69.328125C 34.580982 70.28236 34.580982 70.28236 33.651367 71.25586C 30.131943 74.97301 26.753094 78.818054 23.546875 82.80859C 22.400318 84.2352 21.205225 85.6226 20 87C 19.34 87 18.68 87 18 87C 17.732117 87.58499 17.464233 88.16998 17.188232 88.772705C 15.162488 92.569885 12.202211 95.52716 9.3125 98.6875C 8.712441 99.3765 8.112383 100.065506 7.4941406 100.77539C 4.4241447 104.128975 2.690926 105.80728 -1.78125 107.05469C -5 107 -5 107 -7.8125 105.4375C -12.85191 99.82216 -12.250375 93.24245 -12.125 86.125C -12.115976 85.15047 -12.106953 84.175934 -12.097656 83.171875C -12.074216 80.78099 -12.041405 78.390625 -12 76C -12.99 76 -13.98 76 -15 76C -16.339815 75.693756 -17.67512 75.365486 -19 75C -18.67 74.67 -18.34 74.34 -18 74C -23.94 74 -29.88 74 -36 74C -36.495 74.99 -36.495 74.99 -37 76C -47.509907 71.98019 -47.509907 71.98019 -50 67C -49.22466 61.794147 -46.32981 58.84209 -43 55C -42.67 54.01 -42.34 53.02 -42 52C -40.593807 50.82817 -39.18314 49.66113 -37.746094 48.527344C -35.75914 46.789314 -34.133022 44.89701 -32.4375 42.875C -26.744066 36.28653 -26.744066 36.28653 -22.054688 35.796875C -18.541744 35.719097 -15.369821 36.06936 -12 37C -12.015711 35.6228 -12.015711 35.6228 -12.031738 34.217773C -12.065872 30.803522 -12.090924 27.389454 -12.109863 23.975098C -12.119905 22.49883 -12.133543 21.022585 -12.150879 19.546387C -12.175204 17.4215 -12.186501 15.296876 -12.1953125 13.171875C -12.211023 11.2552 -12.211023 11.2552 -12.227051 9.299805C -12.01392 6.2023053 -11.711297 4.5421934 -10 2C -6.7577333 -0.3159047 -3.9011078 -0.34364596 0 0z" stroke="none"  fill="#FE923B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 365 484)" d="M0 0C 3.8102496 2.6774728 4.7742734 5.615239 6 10C 6.227051 13.067871 6.227051 13.067871 6.1953125 16.273438C 6.188867 17.427149 6.1824217 18.58086 6.1757812 19.769531C 6.1590233 20.959335 6.142266 22.149141 6.125 23.375C 6.1159763 24.588007 6.106953 25.801016 6.0976562 27.050781C 6.074166 30.034058 6.041317 33.016926 6 36C 6.8575487 35.994762 7.7150974 35.989525 8.598633 35.98413C 11.789223 35.96706 14.97979 35.954536 18.17041 35.94507C 19.54965 35.94005 20.928885 35.93323 22.308105 35.92456C 24.293922 35.91239 26.279778 35.907284 28.265625 35.902344C 29.45962 35.897106 30.653614 35.89187 31.88379 35.886475C 34.466946 35.98058 36.550083 36.233738 39 37C 39 37.66 39 38.32 39 39C 40.485 39.495 40.485 39.495 42 40C 42.875 42.375 42.875 42.375 43 46C 41.967773 48.13501 41.967773 48.13501 40.484375 50.503906C 39.93314 51.387558 39.381905 52.27121 38.813965 53.18164C 35.709957 58.004345 32.56662 62.797283 29.375 67.5625C 28.676409 68.60737 27.977818 69.65223 27.258057 70.72876C 21.314356 79.58829 15.268756 88.36713 9 97C 7.93459 98.46953 7.93459 98.46953 6.8476562 99.96875C 2.003894 106.44075 2.003894 106.44075 -2 108C -6.2084723 108 -7.6739326 107.29708 -10.8125 104.4375C -15.482369 98.02582 -13.964825 90.17218 -13.231934 82.723145C -12.878467 78.61198 -12.963499 75.63956 -15 72C -16.987759 71.02267 -16.987759 71.02267 -19 71C -19.33 71.33 -19.66 71.66 -20 72C -20.782461 71.85176 -21.564922 71.703514 -22.371094 71.55078C -25.47054 71.08036 -28.109907 70.969444 -31.222656 71.04297C -32.17334 71.06424 -33.124023 71.08551 -34.103516 71.10742C -36.080425 71.16092 -38.057056 71.22608 -40.033203 71.302734C -47.229965 71.434654 -47.229965 71.434654 -51.121094 69.23828C -53.051968 66.938095 -53.59972 65.91069 -54 63C -51.381657 56.62578 -47.585033 51.548275 -43.3125 46.1875C -42.13826 44.70472 -40.96752 43.21916 -39.80078 41.73047C -39.28685 41.08489 -38.772915 40.439312 -38.24341 39.77417C -36.8117 37.910954 -36.8117 37.910954 -36 35C -34.33906 33.327625 -32.671444 31.661877 -31 30C -29.384668 27.874277 -27.83208 25.75312 -26.3125 23.5625C -25.888237 22.96212 -25.463974 22.361738 -25.026855 21.743164C -22.753447 18.524103 -20.50729 15.286876 -18.269531 12.042969C -17.826336 11.40738 -17.38314 10.771792 -16.926514 10.116943C -16.076593 8.897784 -15.233948 7.673512 -14.399658 6.4436035C -10.326079 0.61681724 -7.12268 -0.7631443 0 0z" stroke="none"  fill="#FE913A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 726 311)" d="M0 0C 2.6757812 2.1171875 2.6757812 2.1171875 5.3125 4.875C 5.786875 5.3682275 6.26125 5.861455 6.75 6.369629C 10.620232 10.45127 14.205002 14.747657 17.707031 19.148438C 19.698805 21.625431 21.748648 24.035797 23.833008 26.434814C 36.091885 40.60058 46.75602 56.150127 57.1875 71.6875C 57.60419 72.30593 58.020878 72.924355 58.450195 73.56152C 59.652103 75.36294 60.82934 77.178154 62 79C 62.40428 79.60667 62.808563 80.21333 63.225098 80.83838C 64.854675 83.42432 65.95031 85.5719 66.30469 88.625C 64.09192 92.65303 60.152378 94.04404 56.125 96C 55.26036 96.43425 54.39572 96.86851 53.504883 97.31592C 51.011204 98.56089 48.506363 99.78083 46 101C 44.85789 101.58008 43.715782 102.16016 42.539062 102.75781C 37.656494 105.15123 33.53916 107.13044 28 106C 23.946964 101.71363 21.209383 96.44005 18.25 91.375C 14.41624 84.90183 10.520006 78.61728 5.9726562 72.61328C 4.1049857 70.139084 2.3210483 67.61699 0.5473633 65.07495C -8.407227 52.294567 -18.401709 40.440033 -29 29C -35.943092 21.433447 -35.943092 21.433447 -36 18C -32.307487 13.252483 -27.381964 10.690573 -22.1875 7.8125C -20.635508 6.950846 -19.087093 6.082704 -17.542969 5.2070312C -16.862585 4.8303833 -16.182201 4.4537354 -15.481201 4.065674C -14.992405 3.7140014 -14.503609 3.362329 -14 3C -14 2.34 -14 1.68 -14 1C -13.05125 0.855625 -12.1025 0.71125 -11.125 0.5625C -3.1971443 -0.86451405 -3.1971443 -0.86451405 0 0z" stroke="none"  fill="#F3F1E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 787.5 482.875)" d="M0 0C 1.0003124 -0.00386719 2.000625 -0.00773437 3.03125 -0.01171875C 3.98 -0.01042969 4.92875 -0.00914062 5.90625 -0.0078125C 7.19499 -0.00612061 7.19499 -0.00612061 8.509766 -0.00439453C 10.5 0.125 10.5 0.125 11.5 1.125C 11.961856 5.5126348 11.961856 5.5126348 10.277344 7.7460938C 8.059009 9.467132 6.591172 9.611147 3.8125 9.8125C 2.6001368 9.911114 2.6001368 9.911114 1.3632812 10.011719C 0.7483984 10.049102 0.13351563 10.086484 -0.5 10.125C -0.08826538 11.360204 -0.08826538 11.360204 0.3317871 12.620361C 0.7263208 13.803963 1.1208545 14.987563 1.5273438 16.207031C 1.7476677 16.867912 1.9679916 17.528793 2.194992 18.209702C 3.4696069 22.033161 4.7437277 25.856787 6.01709 29.680664C 8.954186 38.498756 11.897184 47.314823 14.851273 56.127243C 16.395512 60.735023 17.937378 65.3436 19.479034 69.95224C 20.240545 72.226715 21.003132 74.500824 21.766815 76.77457C 22.849932 79.999825 23.929247 83.22634 25.007812 86.453125C 25.331303 87.41327 25.65479 88.37341 25.988083 89.362656C 27.72713 94.57893 29.244286 99.77116 30.5 105.125C 29.51 105.455 28.52 105.785 27.5 106.125C 27.17 106.785 26.84 107.445 26.5 108.125C 20.75 108.25 20.75 108.25 18.5 107.125C 18.5 107.785 18.5 108.445 18.5 109.125C 17.84 109.125 17.18 109.125 16.5 109.125C 17.276394 112.19954 17.276394 112.19954 19.5 114.125C 19.125 117.75 19.125 117.75 18.5 121.125C 19.16 121.125 19.82 121.125 20.5 121.125C 21.16 122.445 21.82 123.765 22.5 125.125C 23.16 125.125 23.82 125.125 24.5 125.125C 23.84 126.445 23.18 127.765 22.5 129.125C 21.84 129.125 21.18 129.125 20.5 129.125C 20.314375 130.26968 20.314375 130.26968 20.125 131.4375C 19.5 134.125 19.5 134.125 17.5 137.125C 16.18 137.125 14.86 137.125 13.5 137.125C 13.2599125 136.4585 13.019824 135.79199 12.772461 135.10529C 10.433548 128.61263 8.093927 122.12023 5.753687 115.62805C 4.5019054 112.15529 3.2504525 108.682396 1.9996948 105.20926C -3.1415317 90.93325 -8.28956 76.66035 -13.5234375 62.41797C -14.272147 60.376633 -15.020845 58.33529 -15.769531 56.293945C -17.14796 52.536762 -18.534681 48.78278 -19.925781 45.030273C -20.541967 43.352325 -21.157871 41.67427 -21.773438 39.996094C -22.193676 38.873787 -22.193676 38.873787 -22.622406 37.728806C -23.879492 34.281345 -24.5 31.846264 -24.5 28.125C -23.51 27.465 -22.52 26.805 -21.5 26.125C -21.5 25.465 -21.5 24.805 -21.5 24.125C -20.84 24.125 -20.18 24.125 -19.5 24.125C -20.16 21.815 -20.82 19.505 -21.5 17.125C -22.16 17.125 -22.82 17.125 -23.5 17.125C -23.479376 16.17625 -23.45875 15.2275 -23.4375 14.25C -23.27448 11.218337 -23.27448 11.218337 -24.5 9.125C -23.51 9.125 -22.52 9.125 -21.5 9.125C -21.83 6.815 -22.16 4.505 -22.5 2.125C -16.124767 -1.0626161 -6.9778266 -0.02783707 0 0z" stroke="none"  fill="#F5F6F2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 232.47266 440.7422)" d="M0 0C 0.97930986 -0.01862595 1.9586197 -0.03725189 2.9676056 -0.05644226C 10.928911 -0.0753567 15.58468 1.91033 21.527344 7.2578125C 22.77083 8.215959 24.022818 9.163134 25.28125 10.1015625C 28.782907 12.735817 32.236797 15.361171 35.527344 18.257812C 35.527344 18.917812 35.527344 19.577812 35.527344 20.257812C 6.2779207 22.065678 -22.975573 23.744759 -52.241943 25.255127C -54.528484 25.373207 -56.81502 25.491291 -59.101562 25.609375C -60.189465 25.665394 -61.27737 25.721413 -62.39824 25.779129C -68.57977 26.099678 -74.75904 26.445433 -80.9375 26.820312C -87.116165 27.187212 -93.28365 27.525602 -99.47266 27.632812C -100.746895 27.688887 -100.746895 27.688887 -102.046875 27.746094C -111.49197 27.87627 -117.95541 23.044584 -125.03516 17.257812C -125.566895 16.845312 -126.09863 16.432812 -126.646484 16.007812C -129.17238 13.92728 -130.39258 12.805152 -130.875 9.5078125C -130.47266 7.2578125 -130.47266 7.2578125 -129.47266 6.2578125C -126.31045 5.9865665 -123.16438 5.7785387 -119.99609 5.609375C -118.99206 5.5522685 -117.98803 5.495162 -116.95357 5.436325C -113.5852 5.246572 -110.21648 5.0643096 -106.84766 4.8828125C -104.48413 4.752894 -102.120605 4.622929 -99.75708 4.49292C -91.99621 4.06968 -84.23456 3.661671 -76.47266 3.2578125C -75.79338 3.2223809 -75.11411 3.1869493 -74.41425 3.150444C -65.35705 2.6782217 -56.29933 2.2176573 -47.240784 1.7719116C -44.556694 1.6398052 -41.87279 1.5049598 -39.188904 1.3687134C -26.130898 0.7093672 -13.075409 0.13145758 0 0z" stroke="none"  fill="#F6F3EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 866 432)" d="M0 0C 1.2400781 0.24492186 2.4801562 0.48984376 3.7578125 0.7421875C 18.727785 3.9361758 32.120296 10.326031 44 20C 45.056957 20.819443 46.114265 21.638432 47.171875 22.457031C 58.062626 31.075598 66.63138 41.28698 72.78125 53.79297C 74.478096 56.865788 75.7882 58.61915 79.1875 59.6875C 79.78562 59.790627 80.38375 59.89375 81 60C 78.74044 54.026596 76.32311 48.4594 73 43C 71.68 43.33 70.36 43.66 69 44C 69.20625 43.236874 69.4125 42.47375 69.625 41.6875C 70.333916 38.78114 70.333916 38.78114 68 36C 65.94075 35.276176 65.94075 35.276176 64 35C 64.66 34.67 65.32 34.34 66 34C 66.65213 31.975367 66.65213 31.975367 67 30C 69.98399 32.823658 72.23113 36.01343 74.59766 39.35547C 75.06043 39.898163 75.5232 40.44086 76 41C 76.66 41 77.32 41 78 41C 78.10313 41.70125 78.20625 42.4025 78.3125 43.125C 79.092766 46.38793 80.47782 49.021828 82 52C 82 52.66 82 53.32 82 54C 82.66 54.33 83.32 54.66 84 55C 84.66 55.33 85.32 55.66 86 56C 86.212105 57.555424 86.41622 59.11213 86.59375 60.671875C 87.09642 63.552567 87.970856 66.219086 88.875 69C 97.32463 96.26086 96.36393 125.05448 84 151C 83.67902 151.6816 83.35805 152.36319 83.02734 153.06543C 72.43863 175.17892 56.779613 191.84981 35.6875 204.33594C 32.97173 205.87564 32.97173 205.87564 31 208C 29.013247 208.70517 27.01008 209.36438 25 210C 23.146713 210.71617 21.293554 211.43272 19.443604 212.15747C -0.13579044 219.7705 -19.633013 221.85762 -40.375 218.625C -41.189526 218.499 -42.00405 218.37299 -42.84326 218.24316C -46.477245 217.62582 -48.8964 217.06906 -52 215C -50.624767 215.14363 -49.249825 215.29004 -47.875 215.4375C -46.726444 215.55931 -46.726444 215.55931 -45.554688 215.6836C -43.790577 215.90208 -42.03324 216.18062 -40.28589 216.50708C -34.74809 217.33781 -29.177874 217.22496 -23.5896 217.24023C -21.467525 217.24986 -19.346455 217.2809 -17.22461 217.3125C -8.47785 217.37837 -0.45316908 216.41545 8 214C 8 213.34 8 212.68 8 212C 7.01 211.34 6.02 210.68 5 210C 11.625 208.75 11.625 208.75 15 211C 15.66 211 16.32 211 17 211C 17 208.69 17 206.38 17 204C 15.584609 204.5066 15.584609 204.5066 14.140625 205.02344C 12.843925 205.47446 11.54704 205.92496 10.25 206.375C 9.635118 206.59673 9.020234 206.81844 8.386719 207.04688C -10.660392 213.5883 -34.013805 213.6039 -53 207C -54.085392 206.64937 -55.17078 206.29875 -56.289062 205.9375C -71.07519 200.75696 -83.59434 192.60344 -95 182C -95.96809 181.12473 -96.93617 180.24945 -97.93359 179.34766C -119.037 159.41504 -128.48854 131.06003 -129.3125 102.5625C -129.40634 74.51411 -115.78667 48.21701 -96.75 28.3125C -71.1118 3.5732574 -35.004303 -7.303056 0 0zM-90 25C -90.7657 25.635508 -91.5314 26.271015 -92.32031 26.925781C -110.42297 42.236336 -122.64469 65.62078 -126 89C -126.18798 93.23987 -126.185814 97.476906 -126.1853 101.7207C -126.18748 103.73069 -126.20564 105.74011 -126.22461 107.75C -126.23558 115.0918 -125.841934 121.88411 -124 129C -123.8321 129.68828 -123.6642 130.37656 -123.49121 131.0857C -119.57918 146.33134 -112.3637 160.20407 -102 172C -101.039 173.19302 -101.039 173.19302 -100.05859 174.41016C -82.87596 194.98367 -56.00465 207.28612 -29.61621 209.71338C -4.196744 211.54646 23.317265 203.48875 43.23828 187.42969C 55.19499 176.91049 66.44997 164.7548 73 150C 73 149.34 73 148.68 73 148C 72.34 148 71.68 148 71 148C 71.33 146.68 71.66 145.36 72 144C 72.61488 143.9523 73.22977 143.9046 73.86328 143.85547C 76.264 143.17122 76.264 143.17122 77.23047 140.48828C 77.54629 139.44028 77.86211 138.39227 78.1875 137.3125C 78.511055 136.27739 78.83461 135.24226 79.16797 134.17578C 80.515396 129.1567 80.515396 129.1567 81 124C 81.322266 122.89269 81.64453 121.78539 81.97656 120.64453C 83.30985 115.33614 83.38332 110.13888 83.375 104.6875C 83.374115 103.639496 83.37323 102.591484 83.372314 101.51172C 83.18122 88.72297 80.666405 76.87325 76 65C 75.34 65.66 74.68 66.32 74 67C 73.67 66.01 73.34 65.02 73 64C 72.67 63.67 72.34 63.34 72 63C 70.81356 58.372883 70.81356 58.372883 72 56C 70.83032 53.793 69.61912 51.76759 68.25 49.6875C 67.84821 49.07173 67.446434 48.455963 67.03247 47.821533C 63.981323 43.250927 60.698296 38.913925 57.1875 34.6875C 56.73214 34.130867 56.27678 33.574234 55.807617 33.000732C 53.434593 30.226528 51.75185 28.323586 48 28C 48 27.01 48 26.02 48 25C 35.32679 13.335472 15.27638 2.5827093 -2.0625 1.875C -2.701875 1.91625 -3.34125 1.9575 -4 2C -4 1.67 -4 1.34 -4 1C -4.94875 1.020625 -5.8975 1.04125 -6.875 1.0625C -10 1 -10 1 -12 0C -39.966316 -2.490927 -68.96801 6.2508435 -90 25zM73 61C 74 63 74 63 74 63zM72 145C 72 145.66 72 146.32 72 147C 72.66 147 73.32 147 74 147C 74 146.34 74 145.68 74 145C 73.34 145 72.68 145 72 145z" stroke="none"  fill="#176791" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 844 545)" d="M0 0C 2.223921 2.7453656 3.6072073 5.1838417 4.7424316 8.523682C 5.204674 9.865316 5.204674 9.865316 5.6762543 11.234055C 6.0009418 12.199668 6.3256297 13.165281 6.6601562 14.160156C 7.1799984 15.675467 7.1799984 15.675467 7.7103424 17.22139C 8.834061 20.499632 9.9482975 23.781013 11.0625 27.0625C 12.588078 31.526245 14.11909 35.988106 15.652344 40.44922C 16.027502 41.54204 16.402658 42.634865 16.789185 43.760803C 18.910904 49.917976 21.083296 56.05 23.378906 62.14453C 23.621477 62.788685 23.864046 63.43284 24.113968 64.09651C 25.191666 66.94452 26.285547 69.78546 27.394775 72.62134C 27.762077 73.57903 28.12938 74.53672 28.507812 75.52344C 28.823793 76.327 29.139776 77.13058 29.465332 77.958496C 30 80 30 80 29 83C 26.59375 83.91406 26.59375 83.91406 23.5 84.625C 22.499687 84.86477 21.499374 85.10453 20.46875 85.35156C 4.1935964 88.39399 -12.280401 88.4089 -28 83C -25.606743 73.60369 -22.58253 64.43608 -19.5 55.25C -19.240435 54.47341 -18.980871 53.69682 -18.71344 52.896698C -14.87384 41.414127 -10.929686 29.970865 -6.875 18.5625C -6.641438 17.9052 -6.407876 17.247898 -6.1672363 16.570679C -2.162338 5.319058 -2.162338 5.319058 0 0z" stroke="none"  fill="#0F6692" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 757 498)" d="M0 0C 0.66 0 1.32 0 2 0C 2.2804518 1.0038574 2.5609033 2.0077147 2.8498535 3.0419922C 5.792542 13.402045 9.225738 23.509195 12.960693 33.60797C 14.330906 37.314068 15.687969 41.02495 17.046051 44.735504C 17.79286 46.77573 18.540249 48.815742 19.288177 50.85556C 23.806057 63.181732 28.278662 75.52427 32.739506 87.87118C 34.24183 92.02933 35.74723 96.18634 37.255104 100.342476C 38.42271 103.5633 39.58605 106.78566 40.75 110.00781C 41.18047 111.19221 41.610943 112.3766 42.05446 113.59689C 42.443832 114.67754 42.833202 115.75819 43.234375 116.87158C 43.576942 117.818085 43.91951 118.76459 44.27246 119.73978C 45 122 45 122 45 124C 24.743637 116.40386 9.744207 98.25907 0.79296875 79.18359C -5.0464225 66.04574 -7.2599173 53.45261 -7.3125 39.125C -7.3154507 38.42503 -7.3184013 37.72506 -7.3214417 37.003876C -7.3312693 26.798555 -6.776863 17.638445 -3 8C -2.691914 7.1234374 -2.3838282 6.246875 -2.0664062 5.34375C -1.422904 3.5456386 -0.7217041 1.768175 0 0z" stroke="none"  fill="#0F6491" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 927 493)" d="M0 0C 0.66 0 1.32 0 2 0C 13.791383 27.911655 15.317746 53.547714 4 82C -3.5997066 99.251335 -16.983297 115.02686 -33 125C -34.32444 125.36708 -35.657227 125.707 -37 126C -29.274927 102.524284 -21.2863 79.1462 -13.14502 55.811523C -6.62375 37.09288 -1.1057805 19.933147 0 0z" stroke="none"  fill="#0F6490" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 866 432)" d="M0 0C 1.2400781 0.24492186 2.4801562 0.48984376 3.7578125 0.7421875C 18.727785 3.9361758 32.120296 10.326031 44 20C 45.056957 20.819443 46.114265 21.638432 47.171875 22.457031C 67.42373 38.483032 81.62133 63.087772 84.805176 88.87109C 87.7809 121.38776 78.83428 149.6742 58.57202 175.14722C 41.79537 194.92017 15.745204 208.24344 -10 211C -24.780207 211.73486 -38.92056 211.8972 -53 207C -54.085392 206.64937 -55.17078 206.29875 -56.289062 205.9375C -71.07519 200.75696 -83.59434 192.60344 -95 182C -95.96809 181.12473 -96.93617 180.24945 -97.93359 179.34766C -119.037 159.41504 -128.48854 131.06003 -129.3125 102.5625C -129.40634 74.51411 -115.78667 48.21701 -96.75 28.3125C -71.1118 3.5732574 -35.004303 -7.303056 0 0zM-90 25C -90.7657 25.635508 -91.5314 26.271015 -92.32031 26.925781C -110.42297 42.236336 -122.64469 65.62078 -126 89C -126.18798 93.23987 -126.185814 97.476906 -126.1853 101.7207C -126.18748 103.73069 -126.20564 105.74011 -126.22461 107.75C -126.23558 115.0918 -125.841934 121.88411 -124 129C -123.8321 129.68828 -123.6642 130.37656 -123.49121 131.0857C -119.57918 146.33134 -112.3637 160.20407 -102 172C -101.039 173.19302 -101.039 173.19302 -100.05859 174.41016C -82.87596 194.98367 -56.00465 207.28612 -29.61621 209.71338C -4.196744 211.54646 23.317265 203.48875 43.23828 187.42969C 55.19499 176.91049 66.44997 164.7548 73 150C 73 149.34 73 148.68 73 148C 72.34 148 71.68 148 71 148C 71.33 146.68 71.66 145.36 72 144C 72.61488 143.9523 73.22977 143.9046 73.86328 143.85547C 76.264 143.17122 76.264 143.17122 77.23047 140.48828C 77.54629 139.44028 77.86211 138.39227 78.1875 137.3125C 78.511055 136.27739 78.83461 135.24226 79.16797 134.17578C 80.515396 129.1567 80.515396 129.1567 81 124C 81.322266 122.89269 81.64453 121.78539 81.97656 120.64453C 83.30985 115.33614 83.38332 110.13888 83.375 104.6875C 83.374115 103.639496 83.37323 102.591484 83.372314 101.51172C 83.18122 88.72297 80.666405 76.87325 76 65C 75.34 65.66 74.68 66.32 74 67C 73.67 66.01 73.34 65.02 73 64C 72.67 63.67 72.34 63.34 72 63C 70.81356 58.372883 70.81356 58.372883 72 56C 70.83032 53.793 69.61912 51.76759 68.25 49.6875C 67.84821 49.07173 67.446434 48.455963 67.03247 47.821533C 63.981323 43.250927 60.698296 38.913925 57.1875 34.6875C 56.73214 34.130867 56.27678 33.574234 55.807617 33.000732C 53.434593 30.226528 51.75185 28.323586 48 28C 48 27.01 48 26.02 48 25C 35.32679 13.335472 15.27638 2.5827093 -2.0625 1.875C -2.701875 1.91625 -3.34125 1.9575 -4 2C -4 1.67 -4 1.34 -4 1C -4.94875 1.020625 -5.8975 1.04125 -6.875 1.0625C -10 1 -10 1 -12 0C -39.966316 -2.490927 -68.96801 6.2508435 -90 25zM73 61C 74 63 74 63 74 63zM72 145C 72 145.66 72 146.32 72 147C 72.66 147 73.32 147 74 147C 74 146.34 74 145.68 74 145C 73.34 145 72.68 145 72 145z" stroke="none"  fill="#E3EAEC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 604 688)" d="M0 0C 0.99 0.99 0.99 0.99 2 2C 1.9615283 5.9735756 1.4074413 7.5791783 -1.3789062 10.457031C -18.90659 23.425367 -43.32148 38.86034 -66 38C -66.33 38.66 -66.66 39.32 -67 40C -68.7926 39.94005 -70.58426 39.851276 -72.375 39.75C -73.372734 39.703594 -74.37047 39.65719 -75.39844 39.609375C -78 39 -78 39 -79.47656 37.203125C -80 35 -80 35 -79.34009 32.94409C -78 31 -78 31 -75.80786 30.16626C -74.90834 30.009558 -74.00881 29.852856 -73.08203 29.691406C -72.0826 29.504894 -71.083176 29.318384 -70.05347 29.12622C -68.98395 28.940193 -67.91443 28.754166 -66.8125 28.5625C -45.557064 24.457386 -26.790747 17.145245 -9.806641 3.4038086C -6.483349 0.7742967 -4.3037453 -0.83641696 0 0z" stroke="none"  fill="#B5CDD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 458 355)" d="M0 0C 1.3125 2.5625 1.3125 2.5625 2 5C -0.22514237 7.493694 -2.1910663 8.690043 -5.2890625 9.910156C -6.140085 10.255061 -6.9911084 10.599966 -7.86792 10.955322C -8.777757 11.320691 -9.6875925 11.68606 -10.625 12.0625C -25.208532 18.125063 -40.13235 25.571943 -51.101562 37.234375C -52.04129 38.10836 -52.04129 38.10836 -53 39C -56.523247 38.828133 -58.025917 37.982723 -61 36C -56.913963 23.197088 -39.27691 14.651905 -27.97461 8.779541C -6.249093 -2.2003849 -6.249093 -2.2003849 0 0z" stroke="none"  fill="#B7CFD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 289 457)" d="M0 0C 4.217793 1.5063547 5.7747674 4.240124 8 8C 8.917152 10.751456 9.125545 12.360957 9.126984 15.1936035C 9.131213 16.47757 9.131213 16.47757 9.135529 17.787476C 9.132492 19.193983 9.132492 19.193983 9.129395 20.628906C 9.132023 22.11602 9.132023 22.11602 9.134705 23.633179C 9.139133 26.94297 9.13617 30.252708 9.1328125 33.5625C 9.133488 35.902264 9.1344595 38.242023 9.135712 40.581787C 9.137192 45.52849 9.135033 50.475174 9.130371 55.421875C 9.125908 60.388035 9.125707 65.35416 9.130371 70.32031C 9.144978 86.005295 9.109375 101.689285 9.018875 117.37402C 8.99978 121.04231 8.987391 124.71059 8.975952 128.3789C 8.954716 134.32043 8.90623 140.2612 8.837856 146.20236C 8.815999 148.4166 8.802384 150.63092 8.797611 152.84525C 8.789198 155.89888 8.749774 158.95065 8.702637 162.0039C 8.706961 162.88873 8.711285 163.77354 8.71574 164.68518C 8.568668 171.07222 6.8317184 175.84961 3 181C 1.0718431 182.67117 -0.7479477 183.74886 -3 185C -3.66 185.66 -4.32 186.32 -5 187C -7.5159183 187.44144 -9.966023 187.79016 -12.5 188.0625C -17.397617 188.46886 -17.397617 188.46886 -22 190C -24.128368 190.04785 -26.258133 190.03688 -28.386719 190C -34.467697 189.94128 -40.509796 190.11162 -46.58203 190.4375C -47.414192 190.48189 -48.246353 190.52629 -49.10373 190.57202C -52.528175 190.75826 -55.952225 190.95117 -59.37622 191.14551C -71.26601 191.81343 -83.09121 192.12971 -95 192C -95 191.67 -95 191.34 -95 191C -84.288635 189.97324 -73.55823 189.49406 -62.8125 189C -60.939613 188.91237 -59.066727 188.82463 -57.193848 188.73682C -43.46492 188.09692 -29.73439 187.51067 -16 187C -16 186.34 -16 185.68 -16 185C -16.99 185.33 -17.98 185.66 -19 186C -20.335257 186.07973 -21.67412 186.10771 -23.011719 186.09766C -23.769043 186.09444 -24.526367 186.09122 -25.30664 186.08789C -26.092323 186.07951 -26.878008 186.07114 -27.6875 186.0625C -28.484785 186.05798 -29.28207 186.05348 -30.103516 186.04883C -32.069042 186.03706 -34.03453 186.01912 -36 186C -36 185.67 -36 185.34 -36 185C -33.03 185 -30.06 185 -27 185C -27 184.34 -27 183.68 -27 183C -26.301329 183.02321 -25.602655 183.0464 -24.882812 183.07031C -23.972734 183.08836 -23.062656 183.1064 -22.125 183.125C -21.220078 183.14821 -20.315157 183.1714 -19.382812 183.19531C -16.81193 183.24326 -16.81193 183.24326 -15 181C -14.352322 178.42938 -14.352322 178.42938 -14 176C -10.674652 174.89156 -8.378174 175.15546 -5 176C -5 176.66 -5 177.32 -5 178C -3.68 177.67 -2.36 177.34 -1 177C -1 176.34 -1 175.68 -1 175C -0.01 175 0.98 175 2 175C 2.33 174.01 2.66 173.02 3 172C 3.99 171.67 4.98 171.34 6 171C 6.3605227 138.65408 6.687039 106.307945 6.97348 73.96129C 6.9983563 71.18353 7.0240207 68.40579 7.050049 65.62805C 7.130014 57.039543 7.1969714 48.451317 7.2321777 39.862488C 7.2439785 37.47057 7.2609954 35.07867 7.2834473 32.68683C 7.312774 29.442131 7.323249 26.197939 7.328125 22.953125C 7.3404317 22.043179 7.352738 21.13323 7.3654175 20.19571C 7.3373427 12.215873 5.493174 6.0614333 0 0z" stroke="none"  fill="#78726E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 80 439)" d="M0 0C 1.0643826 3.4697325 1.1181601 6.6507077 1.1067963 10.269302C 1.1052806 11.557831 1.1037649 12.84636 1.1022034 14.173935C 1.0954748 15.599762 1.0886668 17.025589 1.0817871 18.451416C 1.0782304 19.954508 1.0751446 21.4576 1.0724945 22.960693C 1.0659463 26.195877 1.0563889 29.431032 1.0445995 32.666203C 1.0263125 37.77949 1.0167097 42.892773 1.0086823 48.00609C 0.98452485 62.545673 0.9486017 77.08522 0.90405273 91.624756C 0.879522 99.65596 0.86215013 107.68714 0.85249335 115.718376C 0.8459421 120.80391 0.8303663 125.88931 0.8081759 130.97481C 0.79646564 134.13469 0.7930797 137.29453 0.792078 140.45444C 0.7899263 141.92223 0.78432 143.39001 0.7750473 144.85777C 0.7628991 146.85771 0.7641914 148.85771 0.7662201 150.85768C 0.7630777 151.978 0.75993526 153.09831 0.75669765 154.25258C 0.7980145 157.2161 0.7980145 157.2161 3 160C 5 163 5 163 7 166C 8.32 165.67 9.64 165.34 11 165C 11.33 165.99 11.66 166.98 12 168C 11.01 168 10.02 168 9 168C 10.048008 169.41153 10.048008 169.41153 11.1171875 170.85156C 12.036863 172.10909 12.956112 173.36691 13.875 174.625C 14.335196 175.24246 14.795391 175.85992 15.269531 176.4961C 17.969683 180.2137 19.794544 183.49686 21 188C 14.392506 181.80548 9.048008 174.47412 3.625 167.25C 2.9778907 166.3889 2.3307812 165.52782 1.6640625 164.64062C -1.3999552 159.77847 -2.250039 155.31737 -2.2539673 149.62427C -2.2624269 147.73172 -2.2624269 147.73172 -2.2710571 145.80093C -2.267169 144.41547 -2.2630727 143.03 -2.258789 141.64453C -2.261501 140.17557 -2.2650633 138.7066 -2.2694092 137.23764C -2.2785099 133.25835 -2.2749987 129.27916 -2.268651 125.299866C -2.263579 121.13065 -2.2682858 116.96145 -2.2714233 112.79224C -2.2750595 105.79149 -2.2702732 98.79078 -2.2607422 91.79004C -2.2498584 83.702286 -2.253384 75.61463 -2.264395 67.52688C -2.273478 60.576393 -2.2747483 53.625942 -2.2695177 46.675453C -2.2664034 42.527077 -2.2659733 38.37875 -2.2725906 34.230377C -2.2783782 30.329905 -2.2743416 26.429562 -2.2628021 22.529102C -2.260163 21.099669 -2.260908 19.670225 -2.2653275 18.240795C -2.2708137 16.285778 -2.2627058 14.330737 -2.2539673 12.375732C -2.2532132 11.282905 -2.2524593 10.190077 -2.2516823 9.064133C -1.9815847 5.7757998 -1.1638914 3.0767198 0 0z" stroke="none"  fill="#656663" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 208 424)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 3.941091 1.0456865 3.941091 1.0456865 6.1484375 0.87890625C 7.40189 0.8062756 7.40189 0.8062756 8.680664 0.73217773C 10.01436 0.6481873 10.01436 0.6481873 11.375 0.5625C 18.926603 0.13026474 26.433483 -0.13841191 34 0C 34 0.33 34 0.66 34 1C 14.2 2.485 14.2 2.485 -6 4C -3.36 4.33 -0.72 4.66 2 5C 2 5.33 2 5.66 2 6C 0.21142578 6.090879 0.21142578 6.090879 -1.6132812 6.1835938C -3.1796992 6.2680116 -4.7461047 6.3526587 -6.3125 6.4375C -7.0981836 6.476816 -7.8838673 6.516133 -8.693359 6.5566406C -9.829346 6.6194825 -9.829346 6.6194825 -10.988281 6.6835938C -11.684778 6.7202516 -12.381274 6.7569094 -13.098877 6.7946777C -15.221657 6.9558115 -15.221657 6.9558115 -18 8C -20.746078 7.912329 -23.450914 7.7704325 -26.1875 7.5625C -26.93709 7.510293 -27.68668 7.458086 -28.458984 7.404297C -30.306372 7.2748623 -32.153244 7.138135 -34 7C -34 6.67 -34 6.34 -34 6C -51.33226 5.781068 -68.566864 6.298122 -85.87378 7.2268066C -89.21188 7.405371 -92.55018 7.5769033 -95.88885 7.7444143C -99.120125 7.907438 -102.35107 8.076907 -105.58203 8.246094C -107.369255 8.336783 -107.369255 8.336783 -109.19258 8.429306C -110.842926 8.519286 -110.842926 8.519286 -112.52661 8.611084C -113.490456 8.662158 -114.45429 8.713232 -115.44734 8.765854C -117.738335 8.975999 -119.79336 9.366863 -122 10C -121 8.5625 -121 8.5625 -119 7C -113.79825 6.1814976 -108.665245 5.8249025 -103.40088 5.626953C -102.687355 5.5991526 -101.97382 5.571352 -101.23868 5.5427094C -98.90141 5.452386 -96.564 5.366851 -94.22656 5.28125C -92.57716 5.218159 -90.92778 5.1547165 -89.27841 5.0909424C -84.9421 4.9240108 -80.60566 4.760924 -76.269165 4.598938C -69.36667 4.340664 -62.464363 4.077355 -55.562115 3.8124847C -53.181976 3.7218134 -50.80177 3.6332831 -48.42154 3.545044C -39.928585 3.2250292 -31.444178 2.8310654 -22.95994 2.3276062C -17.758188 2.022486 -12.584283 1.8713665 -7.375 1.9375C -6.280586 1.9442676 -6.280586 1.9442676 -5.1640625 1.9511719C -3.4426687 1.9624969 -1.7213194 1.9803901 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#7C7C7A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 905 628)" d="M0 0C 2.64 0 5.28 0 8 0C 5.198337 3.0101588 2.441992 5.0912824 -1.125 7.125C -3.763792 8.636237 -5.8465633 9.846563 -8 12C -9.9867525 12.705166 -11.98992 13.364373 -14 14C -15.853286 14.716178 -17.706446 15.432721 -19.556396 16.15747C -39.13579 23.770489 -58.633015 25.85762 -79.375 22.625C -80.18953 22.498995 -81.00405 22.372988 -81.84326 22.243164C -85.47724 21.625826 -87.8964 21.069067 -91 19C -89.62477 19.143635 -88.249825 19.290035 -86.875 19.4375C -86.1093 19.518711 -85.3436 19.599922 -84.55469 19.683594C -82.79058 19.902084 -81.03324 20.18062 -79.28589 20.50708C -73.74809 21.337812 -68.17787 21.224955 -62.5896 21.240234C -60.467525 21.249865 -58.346455 21.280901 -56.22461 21.3125C -47.477848 21.378366 -39.45317 20.415451 -31 18C -31 17.34 -31 16.68 -31 16C -31.99 15.34 -32.98 14.68 -34 14C -27.375 12.75 -27.375 12.75 -24 15C -23.34 15 -22.68 15 -22 15C -22 12.36 -22 9.72 -22 7C -21.0925 6.855625 -20.185 6.71125 -19.25 6.5625C -16.78523 6.1359053 -14.411529 5.6492577 -12 5C -12 5.66 -12 6.32 -12 7C -11.34 7 -10.68 7 -10 7C -10 7.66 -10 8.32 -10 9C -12.5 10.625 -12.5 10.625 -15 12C -13.015019 11.074873 -11.03714 10.134498 -9.0625 9.1875C -7.9603515 8.66543 -6.858203 8.143359 -5.7226562 7.6054688C -2.7125921 6.232053 -2.7125921 6.232053 -2 3C -1.34 2.67 -0.68 2.34 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#75A8BD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 951 493)" d="M0 0C 3.8527718 3.8527718 4.5912 9.855991 6 15C 6.3055077 16.094414 6.611016 17.188828 6.9257812 18.316406C 11.561768 37.629353 11.166451 61.607094 3 80C 1.8187928 76.55944 2.213372 74.55261 3.3125 71.125C 6.9262815 58.625957 9.039843 43.864616 6 31C 5.54625 31.639376 5.0925 32.27875 4.625 32.9375C 3 35 3 35 1 36C 0.67 31.71 0.34 27.42 0 23C 0.66 23 1.32 23 2 23C 1.7525 22.2575 1.7525 22.2575 1.5 21.5C 0.7931118 17.965559 0.93994844 14.603093 1 11C 1.66 12.32 2.32 13.64 3 15C 2.8672266 14.419922 2.7344532 13.839844 2.5976562 13.2421875C 1.60296 8.84229 0.68631667 4.4610586 0 0z" stroke="none"  fill="#72A5BB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 143 640)" d="M0 0C -0.3125 2.375 -0.3125 2.375 -1 5C -1.99 5.66 -2.98 6.32 -4 7C 2.1300228 9.1617775 7.7212973 9.261111 14.183594 9.1953125C 15.2390995 9.192483 16.294605 9.189652 17.382095 9.186737C 20.734015 9.175594 24.08566 9.1505 27.4375 9.125C 29.718096 9.114963 31.998693 9.105838 34.279297 9.097656C 39.85296 9.075674 45.42646 9.042255 51 9C 51 9.33 51 9.66 51 10C 43.208054 10.43107 35.41588 10.857855 27.623535 11.281738C 24.975674 11.425969 22.327913 11.571766 19.680176 11.718262C 15.86244 11.929321 12.044526 12.136861 8.2265625 12.34375C 7.0532436 12.409371 5.879925 12.474993 4.671051 12.542603C -14.850833 13.589123 -14.850833 13.589123 -21 11C -17.844938 10.063802 -15.034135 9.894288 -11.75 9.9375C -10.857968 9.946524 -9.965938 9.955547 -9.046875 9.964844C -8.371407 9.976445 -7.6959376 9.988047 -7 10C -6.67 9.01 -6.34 8.02 -6 7C -6.66 6.67 -7.32 6.34 -8 6C -7.34 5.34 -6.68 4.68 -6 4C -5.01 4 -4.02 4 -3 4C -3 3.34 -3 2.68 -3 2C -4.32 2.33 -5.64 2.66 -7 3C -7 2.34 -7 1.68 -7 1C -4.537212 -0.231394 -2.7204945 -0.07159196 0 0z" stroke="none"  fill="#747472" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 286 642)" d="M0 0C -2.7912169 2.7912169 -5.7432923 2.6587417 -9.5 3.0625C -14.397617 3.4688587 -14.397617 3.4688587 -19 5C -21.128368 5.0478497 -23.258133 5.036886 -25.386719 5C -31.467695 4.9412766 -37.509796 5.111619 -43.58203 5.4375C -44.414192 5.481892 -45.246353 5.526284 -46.10373 5.5720215C -49.528175 5.758248 -52.952225 5.951174 -56.37622 6.145508C -68.26601 6.813435 -80.09121 7.129708 -92 7C -92 6.67 -92 6.34 -92 6C -82.27921 5.0613475 -72.55045 4.6148753 -62.797516 4.184326C -48.962883 3.5680883 -35.131607 2.9035466 -21.3125 2C -19.571419 1.8937732 -19.571419 1.8937732 -17.795166 1.7854004C -13.233942 1.4720286 -3.7309813 -1.8654907 0 0z" stroke="none"  fill="#D0CFCC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 774 445)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C -3.94 6.445 -3.94 6.445 -10 12C -9.34 12 -8.68 12 -8 12C -8 12.66 -8 13.32 -8 14C -7.34 14 -6.68 14 -6 14C -7.393326 17.367205 -8.978929 18.985952 -12 21C -12 20.34 -12 19.68 -12 19C -12.66 19 -13.32 19 -14 19C -14.33 20.32 -14.66 21.64 -15 23C -15.99 22.34 -16.98 21.68 -18 21C -19.32 21.99 -20.64 22.98 -22 24C -20.383608 20.120659 -17.764633 17.285032 -14.9375 14.25C -14.210832 13.4625435 -14.210832 13.4625435 -13.469482 12.65918C -9.240985 8.136797 -4.742475 3.9813368 0 0z" stroke="none"  fill="#5897B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 242 425)" d="M0 0C 10.935518 1.5817447 20.5283 11.306145 29 18C 28.01 18 27.02 18 26 18C 25.319374 18.20625 24.63875 18.4125 23.9375 18.625C 23.298124 18.74875 22.65875 18.8725 22 19C 21.34 18.34 20.68 17.68 20 17C 20.33 16.67 20.66 16.34 21 16C 19.25579 14.679814 17.504398 13.369114 15.75 12.0625C 14.288203 10.966152 14.288203 10.966152 12.796875 9.847656C 10.373212 8.24655 8.824251 7.4236374 6 7C 6 5.68 6 4.36 6 3C 5.01 3.495 5.01 3.495 4 4C 4 4.99 4 5.98 4 7C 3.01 7 2.02 7 1 7C 0.67 4.69 0.34 2.38 0 0z" stroke="none"  fill="#50514E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 732 508)" d="M0 0C 0.33 0 0.66 0 1 0C 1 3.3 1 6.6 1 10C 1.33 10 1.66 10 2 10C 3.6277833 16.147789 4.201901 21.640121 4 28C 3.67 28 3.34 28 3 28C 3 25.36 3 22.72 3 20C 2.34 20 1.68 20 1 20C 0.67 18.68 0.34 17.36 0 16C 0 18.31 0 20.62 0 23C -0.33 23 -0.66 23 -1 23C -1.1037191 32.726547 -1.0451845 42.31338 0 52C -7.9541874 37.630753 -3.9390016 15.1341715 0 0z" stroke="none"  fill="#87B3C6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 878 426)" d="M0 0C -3.9456306 1.4932085 -7.0403156 0.4831424 -11.0625 -0.4375C -20.379305 -2.4168823 -29.519703 -2.2782261 -39 -2C -39 -1.67 -39 -1.34 -39 -1C -45.6 -1 -52.2 -1 -59 -1C -42.53899 -9.230506 -16.984592 -5.17823 0 0z" stroke="none"  fill="#B9CFD6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 738 580)" d="M0 0C 2.0963202 2.5155842 3.5485604 5.057424 4.9375 8C 6.1282372 10.458151 6.9869347 11.986935 8.9375 13.9375C 12.072742 17.072742 14.274612 20.918947 16 25C 15.67 25.66 15.34 26.32 15 27C 11.007667 22.029718 7.2970266 17.137096 4.2382812 11.53125C 2.66135 9.581227 1.4321259 9.368659 -1 9C -1.625 7.125 -1.625 7.125 -2 5C -1.34 4.34 -0.68 3.68 0 3C 0 2.01 0 1.02 0 0zM1 5C 2 7 2 7 2 7z" stroke="none"  fill="#97B6C4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 739 489)" d="M0 0C 0.93423647 3.0103173 1.0444992 3.8665028 0 7C 0.33 7.99 0.66 8.98 1 10C 0.80699897 12.195736 0.5257733 14.38441 0.1875 16.5625C 0.01605469 17.718788 -0.15539062 18.875078 -0.33203125 20.066406C -1 23 -1 23 -3 25C -4 18.25 -4 18.25 -4 16C -4.66 16.99 -5.32 17.98 -6 19C -6 18.01 -6 17.02 -6 16C -6.99 15.67 -7.98 15.34 -9 15C -7.68 15 -6.36 15 -5 15C -4.8904295 14.419922 -4.7808595 13.839844 -4.6679688 13.2421875C -3.6856635 8.402537 -2.4517808 4.3463387 0 0z" stroke="none"  fill="#5898B2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 271 444)" d="M0 0C 3.5851312 1.621845 6.5899234 3.7925625 9.75 6.125C 11.227265 7.2116795 11.227265 7.2116795 12.734375 8.3203125C 13.482031 8.874609 14.229688 9.428906 15 10C 13.152344 10.6171875 13.152344 10.6171875 11 11C 9.308244 9.698648 7.642952 8.362448 6 7C 1.7620846 5.6255407 -2.5868282 5.9252005 -7 6C -7 5.34 -7 4.68 -7 4C -3.535 3.505 -3.535 3.505 0 3C 0 2.01 0 1.02 0 0z" stroke="none"  fill="#353532" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 792 638)" d="M0 0C 4.455 0.99 4.455 0.99 9 2C 9 1.34 9 0.68 9 0C 14.538462 0.4923077 14.538462 0.4923077 16.875 3.0625C 17.431875 4.0215626 17.431875 4.0215626 18 5C 17.01 5.495 17.01 5.495 16 6C 17.98 6.66 19.96 7.32 22 8C 18.75734 9.087236 17.06461 8.946379 13.925781 7.6367188C 13.176191 7.3266993 12.426601 7.01668 11.654297 6.6972656C 10.881504 6.3640428 10.108711 6.0308204 9.3125 5.6875C 8.135908 5.2050686 8.135908 5.2050686 6.935547 4.7128906C 1.1358528 2.2717056 1.1358528 2.2717056 0 0z" stroke="none"  fill="#6098B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 905 628)" d="M0 0C 2.64 0 5.28 0 8 0C 5.198337 3.0101588 2.441992 5.0912824 -1.125 7.125C -3.763792 8.636237 -5.8465633 9.846563 -8 12C -11.620197 13.36763 -15.191728 14.327952 -19 15C -14.913094 11.675059 -10.334308 9.706619 -5.5820312 7.5C -2.7016563 6.207398 -2.7016563 6.207398 -2 3C -1.34 2.67 -0.68 2.34 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#8DB2C3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 931 610)" d="M0 0C 0 3 0 3 -1.7226562 5.0507812C -2.4948046 5.797148 -3.2669532 6.5435157 -4.0625 7.3125C -4.8771877 8.101406 -5.691875 8.890312 -6.53125 9.703125C -9.612713 12.570055 -12.76114 15.312435 -16 18C -16.66 17.67 -17.32 17.34 -18 17C -17.67 16.34 -17.34 15.68 -17 15C -17.66 14.67 -18.32 14.34 -19 14C -18.67 13.01 -18.34 12.02 -18 11C -16.329374 11.061875 -16.329374 11.061875 -14.625 11.125C -8.055517 10.406831 -4.1362605 4.670245 0 0z" stroke="none"  fill="#7EAABD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 793 632)" d="M0 0C 0.33 0.99 0.66 1.98 1 3C -1 5 -1 5 -3 6C -5.1640625 5.0039062 -5.1640625 5.0039062 -7.625 3.5625C -8.4422655 3.0894141 -9.259531 2.616328 -10.1015625 2.1289062C -10.728046 1.7563672 -11.354531 1.3838282 -12 1C -8.261167 -0.86941653 -3.9181535 -1.7414016 0 0z" stroke="none"  fill="#4388A7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 949 562)" d="M0 0C 0.33 0 0.66 0 1 0C 1.185625 1.3921875 1.185625 1.3921875 1.375 2.8125C 1.8153309 6.139252 1.8153309 6.139252 4 9C 3.763659 13.96316 3.5538924 17.29159 0 21C 0.6666667 17.666666 1.3333334 14.333333 2 11C 0.68 10.34 -0.64 9.68 -2 9C -1.34 6.03 -0.68 3.06 0 0z" stroke="none"  fill="#4D94B0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 754 607)" d="M0 0C 2.475 0.99 2.475 0.99 5 2C 5 2.66 5 3.32 5 4C 5.66 4 6.32 4 7 4C 9.610464 6.8477793 11.426765 9.130669 12 13C 10 13 10 13 7.6875 10.875C 0 2.6511629 0 2.6511629 0 0z" stroke="none"  fill="#7EA5BB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 292 629)" d="M0 0C 0.99 0 1.98 0 3 0C 2.5205536 4.2191277 1.8337488 6.796632 -1 10C -1.66 9.67 -2.32 9.34 -3 9C -2.67 8.34 -2.34 7.68 -2 7C -2.66 6.34 -3.32 5.68 -4 5C -4 4.34 -4 3.68 -4 3C -3.01 3 -2.02 3 -1 3C -0.67 2.01 -0.34 1.02 0 0z" stroke="none"  fill="#52514F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 102 626)" d="M0 0C 4.8000383 4.8000383 7.273419 7.340329 9 14C 5.407687 10.5103245 1.9813205 7.033551 -1 3C -0.67 2.01 -0.34 1.02 0 0z" stroke="none"  fill="#595855" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 894 433)" d="M0 0C 4.3486366 0.56444824 7.1782765 2.1542797 10.8125 4.5625C 12.276231 5.523496 12.276231 5.523496 13.769531 6.5039062C 14.505586 6.9976172 15.241641 7.4913282 16 8C 12.931098 9.534451 10.299057 8.549843 7 8C 6.95875 7.401875 6.9175 6.80375 6.875 6.1875C 6.122601 3.6489916 6.122601 3.6489916 2.9375 2.25C 1.968125 1.8375 0.99875 1.425 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#8AAFC1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 115 638)" d="M0 0C 1.32 0.33 2.64 0.66 4 1C 3.01 1.33 2.02 1.66 1 2C 2.9088469 6.54171 2.9088469 6.54171 5 11C 0.7257442 9.416943 -1.2653179 6.494316 -4 3C -3.01 2.67 -2.02 2.34 -1 2C -0.67 1.34 -0.34 0.68 0 0z" stroke="none"  fill="#363634" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 785 438)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C 1.34 2.65 0.68 4.3 0 6C -0.99 6 -1.98 6 -3 6C -3.33 6.99 -3.66 7.98 -4 9C -4.66 8.34 -5.32 7.68 -6 7C -6.99 6.67 -7.98 6.34 -9 6C -6.03 4.02 -3.06 2.04 0 0z" stroke="none"  fill="#6398B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 91 614)" d="M0 0C 1.75 0.125 1.75 0.125 4 1C 6.9828067 4.7723727 8.683325 8.391635 10 13C 6.075234 9.299506 3.0155115 5.480189 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#5D5C59" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 766 620)" d="M0 0C 4.8340726 0.41434908 6.8953767 2.3779395 10 6C 10.33 6.99 10.66 7.98 11 9C 8.034695 7.6419954 5.7086873 6.0757174 3.25 3.9375C 2.6364062 3.4102736 2.0228126 2.8830469 1.390625 2.3398438C 0.93171877 1.8976953 0.4728125 1.4555469 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#73A0B7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 910 442)" d="M0 0C 3.3659186 1.4425366 5.510471 3.3326473 8 6C 5.36 5.67 2.72 5.34 0 5C 0 3.3333333 0 1.6666666 0 0z" stroke="none"  fill="#6BA0B8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 247 427)" d="M0 0C 3.9367363 1.3122455 6.227193 2.9191034 9 6C 6.36 5.67 3.72 5.34 1 5C 0.67 3.35 0.34 1.7 0 0z" stroke="none"  fill="#80827E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 937 575)" d="M0 0C 2.0625 0.4375 2.0625 0.4375 4 1C 3.67 1.66 3.34 2.32 3 3C 2.34 2.67 1.68 2.34 1 2C 1 2.66 1 3.32 1 4C 1.66 4 2.32 4 3 4C 2.67 4.66 2.34 5.32 2 6C 1.34 5.67 0.68 5.34 0 5C -0.33 5.99 -0.66 6.98 -1 8C -1.0424173 5.6670523 -1.0409293 3.3329744 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#AEC7CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 837 637)" d="M0 0C 1.27875 -0.061875 2.5575 -0.12375 3.875 -0.1875C 4.953945 -0.23970702 4.953945 -0.23970702 6.0546875 -0.29296875C 6.6966405 -0.19628906 7.338594 -0.09960938 8 0C 8.66 0.99 9.32 1.98 10 3C 9.01 3 8.02 3 7 3C 7 2.34 7 1.68 7 1C 4.36 1.33 1.72 1.66 -1 2C -0.67 1.34 -0.34 0.68 0 0z" stroke="none"  fill="#B8CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 945 589)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 0.22265625 3.7265625 0.22265625 3.7265625 -0.9375 5.625C -1.618125 6.73875 -2.29875 7.8525 -3 9C -4 7 -4 7 -3.1875 3.9375C -2 1 -2 1 0 0z" stroke="none"  fill="#9AB9C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 271 444)" d="M0 0C 2.8843777 1.2929969 4.8721943 2.6594136 7 5C 4.69 4.34 2.38 3.68 0 3C 0 2.01 0 1.02 0 0z" stroke="none"  fill="#7B7B78" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 929 482)" d="M0 0C 2.475 0.99 2.475 0.99 5 2C 4.67 3.32 4.34 4.64 4 6C 2.68 5.34 1.36 4.68 0 4C 0 2.68 0 1.36 0 0z" stroke="none"  fill="#C0D2D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 751 469)" d="M0 0C 0 3.1055167 -0.5393715 4.352611 -2 7C -2.66 7 -3.32 7 -4 7C -2.925989 4.084827 -2.2218983 2.2218983 0 0z" stroke="none"  fill="#8CAEC0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 115 643)" d="M0 0C 0.66 0 1.32 0 2 0C 2.99 1.98 3.98 3.96 5 6C 1.8388423 4.630165 1.0072908 4.0109363 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#72716F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 84 434)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C 1.34 2.32 0.68 3.64 0 5C -0.99 5 -1.98 5 -3 5C -1.125 1.125 -1.125 1.125 0 0z" stroke="none"  fill="#5E5D5D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 942 518)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 3.3400815 5.1319175 3.0582585 5.883483 1 10C 0.67 6.7 0.34 3.4 0 0z" stroke="none"  fill="#B0C8CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 880 441)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 1.66 2 2.32 2 3 2C 3 1.34 3 0.68 3 0C 3.66 0 4.32 0 5 0C 4.34 1.32 3.68 2.64 3 4C 2.01 3.67 1.02 3.34 0 3C 0 2.01 0 1.02 0 0z" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 791 622)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.34 1.66 1.68 2.32 1 3C 0.01 2.67 -0.98 2.34 -2 2C -1.34 1.34 -0.68 0.68 0 0z" stroke="none"  fill="#B8CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 768 604)" d="M0 0C 1.32 0.33 2.64 0.66 4 1C 3.01 1.495 3.01 1.495 2 2C 1.67 2.66 1.34 3.32 1 4C 0.67 2.68 0.34 1.36 0 0z" stroke="none"  fill="#B5CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 876 438)" d="M0 0C 0.12375 0.639375 0.2475 1.27875 0.375 1.9375C 0.58125 2.618125 0.7875 3.29875 1 4C 1.66 4.33 2.32 4.66 3 5C 1.68 4.67 0.36 4.34 -1 4C -0.67 2.68 -0.34 1.36 0 0z" stroke="none"  fill="#B4CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 842 435)" d="M0 0C 2.125 0.375 2.125 0.375 4 1C 4 1.33 4 1.66 4 2C 2.02 2 0.04 2 -2 2C -1.34 1.34 -0.68 0.68 0 0z" stroke="none"  fill="#B6CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 846 637)" d="M0 0C 1.65 0 3.3 0 5 0C 4 2 4 2 2 3C 2 2.34 2 1.68 2 1C 1.34 0.67 0.68 0.34 0 0z" stroke="none"  fill="#B4CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 862 635)" d="M0 0C 0 0.99 0 1.98 0 3C -0.99 3 -1.98 3 -3 3C -2.01 2.01 -1.02 1.02 0 0z" stroke="none"  fill="#B9CCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 914 608)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C 0.68 1.99 -0.64 2.98 -2 4C -2.33 3.34 -2.66 2.68 -3 2C -2.01 1.34 -1.02 0.68 0 0z" stroke="none"  fill="#AFC9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 928 591)" d="M0 0C 0.99 0.99 1.98 1.98 3 3C 2.01 3.66 1.02 4.32 0 5C 0 3.35 0 1.7 0 0z" stroke="none"  fill="#ADC5CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 488 556)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 3 1.66 3 2.32 3 3C 2.01 3 1.02 3 0 3C 0 2.01 0 1.02 0 0z" stroke="none"  fill="#FFD144" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 852 636)" d="M0 0C 0.99 0.99 1.98 1.98 3 3C 2.01 3.33 1.02 3.66 0 4C 0 2.68 0 1.36 0 0z" stroke="none"  fill="#B6CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 920 601)" d="M0 0C 0.66 0 1.32 0 2 0C 2.33 0.99 2.66 1.98 3 3C 2.01 3 1.02 3 0 3C 0 2.01 0 1.02 0 0z" stroke="none"  fill="#ACC7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 641 558)" d="M0 0C 1.98 0 3.96 0 6 0C 5.34 0.66 4.68 1.32 4 2C 2.68 1.34 1.36 0.68 0 0z" stroke="none"  fill="#FC9F4C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 745 555)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C 2 1.99 2 2.98 2 4C 1.01 3.67 0.02 3.34 -1 3C -0.67 2.01 -0.34 1.02 0 0z" stroke="none"  fill="#B6CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 946 544)" d="M0 0C 0.33 0 0.66 0 1 0C 1 1.98 1 3.96 1 6C -0.0625 4.125 -0.0625 4.125 -1 2C -0.67 1.34 -0.34 0.68 0 0z" stroke="none"  fill="#B1CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 938 502)" d="M0 0C 1.32 0.33 2.64 0.66 4 1C 3.01 1.66 2.02 2.32 1 3C 0.67 2.01 0.34 1.02 0 0z" stroke="none"  fill="#B3CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 786 619)" d="M0 0C 1.98 0.99 1.98 0.99 4 2C 2.68 2 1.36 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#BCCDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 778 459)" d="M0 0C 0.33 0.99 0.66 1.98 1 3C 0.01 3.495 0.01 3.495 -1 4C -0.67 2.68 -0.34 1.36 0 0z" stroke="none"  fill="#B6CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 752 733)" d="M0 0C 1.485 0.99 1.485 0.99 3 2C 2.01 2 1.02 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#F1F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 899 638)" d="M0 0C 1.98 0.99 1.98 0.99 4 2C 2.68 1.67 1.36 1.34 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#86A9B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 757 592)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.99 1.34 1.98 1 3C 0.67 2.01 0.34 1.02 0 0z" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 476 556)" d="M0 0C 1.32 0 2.64 0 4 0C 3.67 0.66 3.34 1.32 3 2C 2.01 1.34 1.02 0.68 0 0z" stroke="none"  fill="#FFCE3F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 941 510)" d="M0 0C 0.33 0 0.66 0 1 0C 1 1.65 1 3.3 1 5C 0.67 5 0.34 5 0 5C 0 3.35 0 1.7 0 0z" stroke="none"  fill="#B5CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 656 483)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.01 1.33 1.02 1.66 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#F3AD68" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 929 477)" d="M0 0C 0.66 0.33 1.32 0.66 2 1C 1.67 1.66 1.34 2.32 1 3C 0.34 2.34 -0.32 1.68 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#BFD3D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 904 454)" d="M0 0C 0.99 0 1.98 0 3 0C 2.67 0.66 2.34 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B9CDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 872 437)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.01 1.33 1.02 1.66 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B6CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 867 436)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 1.68 1.33 0.36 1.66 -1 2C -0.67 1.34 -0.34 0.68 0 0z" stroke="none"  fill="#B6CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 790 432)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.01 1.33 1.02 1.66 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#9FBEC5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 467 830)" d="M0 0C 4 1 4 1 4 1L4 1L0 0z" stroke="none"  fill="#EEEEE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 740 725)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.01 1 1.02 1 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#F5F4EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 806 631)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.01 1.67 0.02 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#BBD2D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 801 627)" d="M0 0C 1.485 0.99 1.485 0.99 3 2C 2.01 2 1.02 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#BFD0D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 382 574)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.01 1.67 0.02 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#F9A454" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 391 556)" d="M0 0C 0.33 0.99 0.66 1.98 1 3C 0.34 2.67 -0.32 2.34 -1 2C -0.67 1.34 -0.34 0.68 0 0z" stroke="none"  fill="#F7A051" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 744 545)" d="M0 0C 0 3 0 3 0 3L0 3L0 0z" stroke="none"  fill="#B5CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 743 525)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.99 1.34 1.98 1 3C 0.67 2.01 0.34 1.02 0 0z" stroke="none"  fill="#AEC7D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 750 497)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.99 1.34 1.98 1 3C 0.67 2.01 0.34 1.02 0 0z" stroke="none"  fill="#B9CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 759 481)" d="M0 0C 0 3 0 3 0 3L0 3L0 0z" stroke="none"  fill="#BACED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 796 446)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.99 1.34 1.98 1 3C 0.67 2.01 0.34 1.02 0 0z" stroke="none"  fill="#B8CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 785 434)" d="M0 0C 0.99 0.33 1.98 0.66 3 1C 2.01 1 1.02 1 0 1C 0 0.67 0 0.34 0 0z" stroke="none"  fill="#A1BFC7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 474 257)" d="M0 0C 1 4 1 4 1 4L1 4L0 0z" stroke="none"  fill="#F7F8F3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 544 831)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#EFEEE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 464 829)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#EFEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 435 823)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#F1EFE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 548 712)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 0.34 1.67 -0.32 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#CCD9CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 129 654)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 0.34 1.67 -0.32 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#7A7974" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 858 653)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#8FB2BC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 260 647)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#CFCCC6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 876 632)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 1.01 1.495 1.01 1.495 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B8CED6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 888 626)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B9CCD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C4D4D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 895 622)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B5CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 753 613)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 1.01 1.495 1.01 1.495 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#A8C3C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CDCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 774 608)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 0.34 1.67 -0.32 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#B2C9CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 739 591)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#A0BDC4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 756 587)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 1.01 1.495 1.01 1.495 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B8CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 686 574)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#EBAA6C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 382 572)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#F9A453" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 484 556)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#FFD143" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 943 546)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B2C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 741 542)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#BACACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 406 534)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#FAAA52" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 946 531)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#AEC6CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 741 527)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B5CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C7D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C8D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 945 517)" d="M0 0C 0.33 0.66 0.66 1.32 1 2C 0.34 1.67 -0.32 1.34 -1 1C -0.67 0.67 -0.34 0.34 0 0z" stroke="none"  fill="#ADC8D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 944 514)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#B0C7D1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ED9956" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED0D4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 662 482)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#F3B06A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 770 467)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 780 458)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 1.01 1.495 1.01 1.495 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B8CDD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 789 449)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#BBCFD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 813 440)" d="M0 0C 0.99 0.495 0.99 0.495 2 1C 1.01 1.495 1.01 1.495 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B2CBCE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 823 437)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#B9CDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 855 434)" d="M0 0C 0.66 0.66 1.32 1.32 2 2C 1.34 2 0.68 2 0 2C 0 1.34 0 0.68 0 0z" stroke="none"  fill="#B7CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 857 434)" d="M0 0C 0.66 0 1.32 0 2 0C 1.67 0.66 1.34 1.32 1 2C 0.67 1.34 0.34 0.68 0 0z" stroke="none"  fill="#B6CBCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 839 433)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#B7CBCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 786 432)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#A4C4CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 835 420)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#9ABAC2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 287 413)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#ECE9E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 291 412)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#EEEBE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 323 406)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#F2EFE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 698 302)" d="M0 0C 3 1 3 1 3 1L3 1L0 0z" stroke="none"  fill="#F2F2ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 477 831)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#EFEDE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 432 822)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F0F1E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 422 819)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#EFEDE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 586 776)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F7F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DCD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 277 726)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F0EEE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ECEDE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 552 712)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#CFD6D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 475 678)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FCD563" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 721 657)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F4F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 236 657)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#D2D1CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 832 655)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#9CBCC5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 260 652)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#D9D7D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 252 652)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#D7D4CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2F1EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 880 647)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#82ACB2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 257 647)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#CDCBC4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 805 646)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#A7BFC7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 833 637)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#BACDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 786 637)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#A8C3CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 857 636)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B9CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 866 634)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#BACDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 878 633)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#BBCDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 881 632)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B7CED1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 518 631)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FECF49" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 879 630)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#BACDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 887 629)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B6CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C6CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 932 612)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#82A8B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 934 609)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#7BA4B0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 678 582)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F0AF71" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 748 572)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B9CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6BCBE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 745 561)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B6C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6BBBC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FD9F4B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 621 558)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FCAA51" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F89A4B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 334 556)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F59B4F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 330 556)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F59A4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CED1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#52514C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 589 542)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FFC840" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C7CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2CACE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 632 523)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FEA54B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCAB50" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#504E4A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A4B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#87A7B6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A8C5CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CCD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA24C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#99B0B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4C4B46" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCD4E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD242" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCFD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 755 485)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B8CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 757 480)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACFD6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3C6D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCCF52" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 772 465)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B9CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBCE50" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 772 462)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B3C9D1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 894 450)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B6CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 891 447)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B4CCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 807 443)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B8CDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 886 442)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B7CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 808 440)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B4CAD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CDD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 865 435)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B4CAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#514F4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 262 432)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#B1B0AB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 127 427)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#5B5B58" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 201 424)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#8B8A85" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#95B5BF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAD15F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 332 404)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F3F1E6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 562 401)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FCD867" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DEDEC8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 252 382)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F0EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 433 360)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#D3DDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EFE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 396 310)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F7F6EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 696 303)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F2F1ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 492 294)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FBFBF5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 446 287)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#F7F5EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFBF5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 476 270)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#FBF9F4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F6F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 717 225)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#DFE0DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EBECE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 689 210)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#E6E8E5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 497 208)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#EBEBE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 620 137)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#DFE0DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 584 137)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#E1E2DD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 610 136)" d="M0 0C 2 1 2 1 2 1L2 1L0 0z" stroke="none"  fill="#E1E2DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#1A1C1A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#50504E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2EEE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFECE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F1EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDECE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ECECE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFECE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2F0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EFE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EFE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F1E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEECE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F3EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F4EA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EDE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEEE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F1E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F6EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFECE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEECE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F4EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EFE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEFE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EDE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F5EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEFE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEE4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F3E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F2EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF6F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F3EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D1DED6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DBD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D7DCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F5EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CFDAD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D5DED0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DCD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DCD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D0DBD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EDE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D0DAD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EBEAE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CDDAD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEEE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DBCB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D1DBD6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DACA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F4EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CFDACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CDD8CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F8F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F0E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F8EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CDD7D1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEFE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0F1EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF7EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F6EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F7F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EDE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF7EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CBD9CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C9D6CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F7EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7DB86" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAD982" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F9EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CFD8CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1F0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F4EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F4EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED562" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDD460" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEDE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCD558" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED359" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EAE8E0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD35A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED35E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DDDBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCD255" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F3E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD65A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DAD9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F5EA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDD253" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD252" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED458" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#97B7BF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6C1C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBCC56" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0AEA9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7E7C78" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8CB1BE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8DB1BB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9AB8C3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E2DDD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#787673" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A2BEC5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD456" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DAD8CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DAD7CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D1CFC7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9DBBC3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F4EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#888783" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8AACB5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDD352" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A3C1C7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ADC2C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D0CEC8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CECCC6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6C1C6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCD049" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC8CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2CBCD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ABC1C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AEC6CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECE4B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDD1D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED1D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ACC2C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A4B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D1D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCD04C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD14C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#575550" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4C9D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD14E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A6B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BFCED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCD24D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD24C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD34B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BECDD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C5D4D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD04B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD040" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C2D3D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED1D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C2D1D3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BFD2D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECF45" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDD1D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#54524E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#85A9B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#53514B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7FA7B0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1CACD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCFD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD34B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A9B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C2D8DA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC7CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C7D8D9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C4D3D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCED3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#53504C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#85A9B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5C9CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D0D4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0CACE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDD150" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#53514D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C0D0D3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDD0D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#52514A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#84A9B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6C3C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED245" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCED4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A0BAC2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8D1D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7C8CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A2BFC6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8C64B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3C8CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCFCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD449" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5C7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A7B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CACD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CECE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6BDC6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC8CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5C7D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4CBCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A7C3C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C5CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C7CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AECAD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C8CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AEC7CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4C8CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4CBCE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9FBBC0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACDCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CACD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A3BEC5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2AF6F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCF48" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2A866" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3A764" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C9CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC1C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A4BCC4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEB37B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBCE46" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD048" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7EA4B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CED0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3A45E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CCCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECF43" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C9CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AEC4CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C9CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#779CAF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4A058" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A6B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C9CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6A45B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD443" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBAC5F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4A055" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2A868" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD242" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9EB8BD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCF43" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD045" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFA866" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9A251" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A6B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A4BBBE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EBA96C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCE3D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAA556" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C9D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A4BBBC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9A04F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1C8CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCD0D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAA453" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3C9CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5C84D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A8B5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C9CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7EA6B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A3BAB9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A8B9BC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCF3F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E5A872" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AEC6CB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A3B6BB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA14E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDA54F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBA751" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F89E4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1CBCE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC745" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A6B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFCACE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCFD8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A7B8BA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCC3F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEC849" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0974D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD144" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCE3C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#76A0B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B1CACE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E0A26B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA74A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCA550" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4F504B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCA451" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CDD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D29F74" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A5C2C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC647" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A2B1AC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A1B1AD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D08A52" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC93E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A2B1AC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC941" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#514F4B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAA952" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFA747" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC83E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B0C8D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CCA573" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC845" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC843" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4E4F4A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDA54B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC948" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA54A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDA549" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC943" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AECBD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA64D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA84C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFAC54" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7C34F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC8D0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EC944E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4D4D48" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EF954A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C9D1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EC9550" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ED9650" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA350" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFC847" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E9964F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0974F" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4D4C48" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CDD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDA14D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B9CED3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCD0D4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EB9852" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA350" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCED3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C8CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAA14E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4D4D47" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACFD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FEA554" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED14A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECF49" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCA352" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECD3B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAA857" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C3D3D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3CBCC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCE4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#98B2B5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2AA65" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCD4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0A25A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8F7C6A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D4D9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BFD3D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A4AF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCED4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9AB8BB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCD4A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7EA5B2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9DB8BE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3B16B" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCE4E" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5A864" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D2D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3AA66" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D1D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C5D5D9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C9DADC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CFCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0AD6C" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FECD44" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4E4D49" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CAD8DD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CAD9DC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#99B3BA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CBD8DC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#988D80" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7EA8B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCD1D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED4D4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4E4D49" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#81A8B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7D3D6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CCD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCD2D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCD4D6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9BB8C1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C1D2D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#4D4D49" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED1D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD051" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BAD0D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C2D3D8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFCBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B9CDD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A0BDC5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#C0D1D7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFCF51" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BFD2D6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBD3D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD242" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BECDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#80A6B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#AFC7CD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCCD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BECFD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCED1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7DA3AF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A9B2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCBD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCFD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B9CDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9EBDC2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BED0D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCCD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8CAFB8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7C4D1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ADC6C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD348" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CCD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CFD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACED1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B9CFD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CBCE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A2C0C6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FFD347" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#81A8B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B4CCD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A7C3C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CBD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B2C8CE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B3C7CF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED14A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CDD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BACED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BDCFD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBCED3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CCCF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9CBCC8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#82A7B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7FA5B3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B7CBD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B9CDD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BBD0D2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A7C4CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B8CAD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B6CCD0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#BCCFD3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#83ABB6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#B5CACF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A1A09D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7DA8B2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#79A6B1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#505049" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9CD4A" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8BACB7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#504D4D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#81A7B5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A4C1C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#81A7B4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#686963" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#696866" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#8AADB8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A5C3C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#7A7A71" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#86ACB7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#88A9B8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#898886" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#888A82" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#A6A6A1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#9DBBC3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E5E9E5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EAE9DF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E8E4E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DDDBD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FED359" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#ECEAE1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EBE9E0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEAE1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEFE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFEDE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E0DDD7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EDEEE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EDE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2EEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DEE2DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E3E8E1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EAEDE5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2EFE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E4E3DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9D86D" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F2E7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EAE6E1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F4EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DBDEC6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEBE3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D6DACB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F4EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DACA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D8DCD1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DAC8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F6F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DDCE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F1EFE7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D4DED2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3D9D5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F3EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3D9C8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CFD9C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F5EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2D9CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D1DACC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D1D9CA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2D8CC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#CEDCD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F6EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D0DDD4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F8EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EFF0E8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DCE0C9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DDD7" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D2DBD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F7EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DED6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F5EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2EFE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D3DCD5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#D5DFD2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F8EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F5EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F5F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F7F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F9F1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F5ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E6E4DD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F6ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F3ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F2EC" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F8F3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDFAF3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F2EA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F6F4F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F5F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDFBF2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBF9F3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F1ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCFCF3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFDF2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF8F3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDFBF5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAFBF3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F7F5EF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F8F6F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFAF4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F0EEEB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF9F4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FDF9F5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEE8" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFBF4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F6F0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFAF5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFAF4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBF9F5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF8F4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAFAF3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FBFBF5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F9F8F4" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E6E8E6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FCF8F5" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#FAF6F3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F1EA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F3F1EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F1EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E4E6E3" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F2EB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2F1E9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F5F3ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E2E4E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E0E2E1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E3E6E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F4F3EE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#F2F3ED" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E0E2E0" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEDE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E2E5E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEDE9" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E3E6E1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EEEEEA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#EAEAE6" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E1E4E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DDE1DF" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E4E7E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E3E4E1" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E2E3E2" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DFE0DD" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DEE2DE" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#DEDFDB" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E1E0DA" fill-rule="nonzero" />
	<path transform="matrix(1 0 0 1 0 0)" d="" stroke="none"  fill="#E0E3DE" fill-rule="nonzero" />
</g>
</svg>
