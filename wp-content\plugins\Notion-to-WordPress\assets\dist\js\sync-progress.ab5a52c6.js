"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[426],{3735:(t,e,n)=>{n(2675),n(9463),n(2259),n(5700),n(3792),n(9572),n(2892),n(6099),n(7764),n(2953);var r=n(3040);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,c(r.key),r)}}function i(t,e,n){return(e=c(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function c(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}var u=function(){return t=function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),i(this,"element",null),i(this,"progressBar",null),i(this,"statusText",null),this.element=document.querySelector(e),this.element&&this.init()},(e=[{key:"init",value:function(){this.element&&(this.element.innerHTML='\n      <div class="sync-progress">\n        <div class="progress-bar">\n          <div class="progress-fill"></div>\n        </div>\n        <div class="status-text">准备中...</div>\n      </div>\n    ',this.progressBar=this.element.querySelector(".progress-fill"),this.statusText=this.element.querySelector(".status-text"),r.Bt.on("sync:start",this.onSyncStart.bind(this)),r.Bt.on("sync:progress",this.onSyncProgress.bind(this)),r.Bt.on("sync:complete",this.onSyncComplete.bind(this)),r.Bt.on("sync:error",this.onSyncError.bind(this)))}},{key:"onSyncStart",value:function(){this.updateProgress(0,"开始同步...")}},{key:"onSyncProgress",value:function(t,e){this.updateProgress(e.progress,e.message)}},{key:"onSyncComplete",value:function(){this.updateProgress(100,"同步完成")}},{key:"onSyncError",value:function(t,e){this.updateProgress(0,"同步失败: ".concat(e.message))}},{key:"updateProgress",value:function(t,e){this.progressBar&&(this.progressBar.style.width="".concat(t,"%")),this.statusText&&(this.statusText.textContent=e)}},{key:"destroy",value:function(){r.Bt.off("sync:start",this.onSyncStart.bind(this)),r.Bt.off("sync:progress",this.onSyncProgress.bind(this)),r.Bt.off("sync:complete",this.onSyncComplete.bind(this)),r.Bt.off("sync:error",this.onSyncError.bind(this))}}])&&o(t.prototype,e),n&&o(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,n}();document.addEventListener("DOMContentLoaded",function(){document.querySelector(".sync-progress-container")&&new u(".sync-progress-container")})}},t=>{t.O(0,[96,76],()=>{return e=3735,t(t.s=e);var e});t.O()}]);
//# sourceMappingURL=sync-progress.ab5a52c6.js.map