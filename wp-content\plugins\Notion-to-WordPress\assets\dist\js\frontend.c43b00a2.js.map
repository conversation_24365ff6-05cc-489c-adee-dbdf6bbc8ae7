{"version": 3, "file": "js/frontend.c43b00a2.js", "mappings": ";0rDA6BO,IAAMA,EAAgB,WAQ3B,SAAAA,IAA0D,IAA9CC,EAAuCC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACrD,G,4FADsDG,CAAA,KAAAL,GAAAM,EAAA,sBAAAA,EAAA,oBAJjC,GAACA,EAAA,oCAAAA,EAAA,sBAEwB,MAG1CN,EAAiBO,SACnB,OAAOP,EAAiBO,SAG1BP,EAAiBO,SAAWC,KAE5BA,KAAKP,OAAMQ,EAAA,CACTC,gBAAiB,CACf,mCACA,gBACA,iBACA,YACA,gBAEFC,sBAAuB,mBAAoBC,SAASC,gBAAgBC,MACpEC,kBAAmB,IACnBC,aAAc,IACXf,GAGLO,KAAKS,qBAAuBT,KAAKP,OAAOU,sBACxCH,KAAKU,MACP,CAEA,O,EAAAlB,E,EAAA,EAAAmB,IAAA,OAAAC,MAaA,WACEZ,KAAKa,qBACLb,KAAKc,sBACLd,KAAKe,qBAGLC,EAAAA,EAAAA,IAAK,gCACP,GAEA,CAAAL,IAAA,sBAAAC,MAGA,WAEER,SAASa,iBAAiB,QAASjB,KAAKkB,kBAAkBC,KAAKnB,OAG/DoB,OAAOH,iBAAiB,aAAcjB,KAAKqB,SAASrB,KAAKsB,iBAAiBH,KAAKnB,MAAO,MAGtFoB,OAAOH,iBAAiB,SAAUjB,KAAKqB,SAASrB,KAAKa,mBAAmBM,KAAKnB,MAAO,MAGhF,mBAAoBoB,QACtBpB,KAAKuB,qBAET,GAEA,CAAAZ,IAAA,sBAAAC,MAGA,WAAoC,IAAAY,EAAA,KAClCxB,KAAKyB,eAAiB,IAAIC,eAAe1B,KAAKqB,SAAS,WACrDG,EAAKX,oBACP,EAAG,MAGHb,KAAKP,OAAOS,gBAAgByB,QAAQ,SAAAC,GACjBxB,SAASyB,iBAAiBD,GAClCD,QAAQ,SAAAG,GACXA,aAAmBC,aACrBP,EAAKC,eAAgBO,QAAQF,EAEjC,EACF,EACF,GAEA,CAAAnB,IAAA,wBAAAC,MAGA,WACE,IAAIqB,EAAY,EAYhB,OAVAjC,KAAKP,OAAOS,gBAAgByB,QAAQ,SAAAC,GAClC,IAAME,EAAU1B,SAAS8B,cAAcN,GACvC,GAAIE,EAAS,CACX,IAAMxB,EAAQc,OAAOe,iBAAiBL,GACf,UAAnBxB,EAAM8B,UAA2C,WAAnB9B,EAAM8B,WACtCH,EAAYI,KAAKC,IAAIL,EAAWH,EAAQS,cAE5C,CACF,GAEON,EAAYjC,KAAKP,OAAOe,YACjC,GAEA,CAAAG,IAAA,qBAAAC,MAGA,WACE,IAAM4B,EAAYxC,KAAKyC,wBAEnBD,IAAcxC,KAAK0C,eACrB1C,KAAK0C,aAAeF,EACpBpC,SAASC,gBAAgBC,MAAMqC,YAAY,sBAAuB,GAAFC,OAAK5C,KAAK0C,aAAY,QAEtF1B,EAAAA,EAAAA,IAAK,+BAAgC,CAAE6B,OAAQ7C,KAAK0C,eAGxD,GAEA,CAAA/B,IAAA,iBAAAC,MAGA,SAAekC,GACb,IAAKA,IAAaA,EAASC,WAAW,kBACpC,OAAO,EAGT,IAAMC,EAAUF,EAASG,QAAQ,IAAK,IAChCC,EAAS9C,SAAS+C,eAAeH,GAEvC,IAAKE,EAEH,OAAO,EAGT,IAAME,EAA6B,CACjCC,GAAIL,EACJlB,QAASoB,EACTI,KAAMJ,EAAOK,yBAef,OAXAvD,KAAKwD,cAAcJ,GAGnBpD,KAAKyD,eAAeP,GAGpBlD,KAAK0D,UAAUZ,IAEf9B,EAAAA,EAAAA,IAAK,kBAAmBoC,IAGjB,CACT,GAEA,CAAAzC,IAAA,gBAAAC,MAGA,SAAsBwC,GAAkC,IAAAO,EAAA,KAC9C7B,EAAYsB,EAAZtB,QAGF8B,EAAuC,CAC3CC,MAAO,SACPC,SAAU9D,KAAKS,qBAAuB,SAAW,QAGnDqB,EAAQiC,eAAeH,GAGvBI,WAAW,WACT,IAAMV,EAAOxB,EAAQyB,wBACrB,GAAID,EAAKW,IAAMN,EAAKjB,aAAc,CAChC,IAAMG,EAASS,EAAKW,IAAMN,EAAKjB,aAE3BiB,EAAKlD,qBACPW,OAAO8C,SAAS,CAAED,IAAKpB,EAAQiB,SAAU,WAEzC1C,OAAO8C,SAAS,EAAGrB,EAEvB,CACF,EAAG7C,KAAKS,qBAAuB,IAAM,EACvC,GAEA,CAAAE,IAAA,iBAAAC,MAGA,SAAuBkB,GACrB,GAAKA,GAAYA,EAAQqC,UAAzB,CAGArC,EAAQqC,UAAUC,OAAO,0BAGzBtC,EAAQuC,YAGRvC,EAAQqC,UAAUG,IAAI,0BAGtB,IAAMC,EAAkB,WACtBzC,EAAQqC,UAAUC,OAAO,0BACzBtC,EAAQ0C,oBAAoB,eAAgBD,EAC9C,EAEAzC,EAAQb,iBAAiB,eAAgBsD,EAAiB,CAAEE,MAAM,IAGlET,WAAW,WACTlC,EAAQqC,UAAUC,OAAO,yBAC3B,EAAGpE,KAAKP,OAAOc,oBAEfS,EAAAA,EAAAA,IAAK,2BAA4B,CAAEc,QAAAA,EAASuB,GAAIvB,EAAQuB,IAxBd,CAyB5C,GAEA,CAAA1C,IAAA,YAAAC,MAGA,SAAkBkC,GAChB,GAAI1B,OAAOsD,SAAWtD,OAAOsD,QAAQC,aACnC,IACEvD,OAAOsD,QAAQC,aAAa,KAAM,GAAI7B,IACtC9B,EAAAA,EAAAA,IAAK,qBAAsB,CAAE4D,KAAM9B,GACrC,CAAE,MAAO+B,GAET,CAEJ,GAEA,CAAAlE,IAAA,oBAAAC,MAGA,SAA0BkE,GACxB,IACMC,EADSD,EAAM5B,OACD8B,QAAQ,6BAE5B,GAAID,EAAM,CACRD,EAAMG,iBACN,IAAMC,EAAOH,EAAKI,aAAa,QAC3BD,GACFlF,KAAKoF,eAAeF,EAExB,CACF,GAEA,CAAAvE,IAAA,mBAAAC,MAGA,WACE,IAAMgE,EAAOxD,OAAOiE,SAAST,KACzBA,GAAQA,EAAK7B,WAAW,mBAC1B/C,KAAKoF,eAAeR,EAExB,GAEA,CAAAjE,IAAA,oBAAAC,MAGA,WAAkC,IAAA0E,EAAA,KAC1BV,EAAOxD,OAAOiE,SAAST,KACzBA,GAAQA,EAAK7B,WAAW,mBAE1BiB,WAAW,WACTsB,EAAKF,eAAeR,EACtB,EAAG,IAEP,GAEA,CAAAjE,IAAA,WAAAC,MAGA,SACE2E,EACAC,GACkC,IAC9BC,EAD8BC,EAAA,KAGlC,OAAO,WAA4B,QAAAC,EAAAjG,UAAAC,OAAxBiG,EAAI,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAJF,EAAIE,GAAApG,UAAAoG,GACbC,aAAaN,GACbA,EAAUzB,WAAW,kBAAMuB,EAAKS,MAAMN,EAAME,EAAK,EAAEJ,EACrD,CACF,GAEA,CAAA7E,IAAA,YAAAC,MAGA,WACE,OAAAX,EAAA,GAAYD,KAAKP,OACnB,GAEA,CAAAkB,IAAA,eAAAC,MAGA,SAAaqF,GACXjG,KAAKP,OAAMQ,EAAAA,EAAA,GAAQD,KAAKP,QAAWwG,IACnCjF,EAAAA,EAAAA,IAAK,wBAAyBhB,KAAKP,OACrC,GAEA,CAAAkB,IAAA,kBAAAC,MAGA,WACE,OAAOZ,KAAK0C,YACd,GAEA,CAAA/B,IAAA,gBAAAC,MAGA,WACE,IAAMsF,EAA0B,GAahC,OAZiB9F,SAASyB,iBAAiB,yBAElCF,QAAQ,SAAAG,GACXA,aAAmBC,aACrBmE,EAAQC,KAAK,CACX9C,GAAIvB,EAAQuB,GACZvB,QAAAA,EACAwB,KAAMxB,EAAQyB,yBAGpB,GAEO2C,CACT,GAEA,CAAAvF,IAAA,UAAAC,MAGA,WAEER,SAASoE,oBAAoB,QAASxE,KAAKkB,mBAC3CE,OAAOoD,oBAAoB,aAAcxE,KAAKsB,kBAC9CF,OAAOoD,oBAAoB,SAAUxE,KAAKa,oBAGtCb,KAAKyB,iBACPzB,KAAKyB,eAAe2E,aACpBpG,KAAKyB,eAAiB,MAIxBrB,SAASC,gBAAgBC,MAAM+F,eAAe,uBAE9C7G,EAAiBO,SAAW,MAC5BiB,EAAAA,EAAAA,IAAK,8BAEP,I,EAAC,EAAAL,IAAA,cAAAC,MA/TD,SAAmBnB,GAIjB,OAHKD,EAAiBO,WACpBP,EAAiBO,SAAW,IAAIP,EAAiBC,IAE5CD,EAAiBO,QAC1B,I,4FAAC,CAzC0B,GAAAD,EAAhBN,EAAgB,WACwB,MAsW9C,IAAM8G,EAAmB9G,EAAiB+G,eAGjDC,EAAAA,EAAAA,IAAM,WAEN,G,2RCxYA,IAAAC,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAA5G,KAAAsF,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAA/H,OAAA+G,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAM,EAAAf,EAAA,GAAAN,EAAA,GAAAI,EAAAiB,IAAAnB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAC,IAAAjB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAmB,KAAAf,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAmB,EAAAd,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAM,GAAA,GAAAP,EAAA,QAAAQ,UAAA,oCAAAN,GAAA,IAAAD,GAAAK,EAAAL,EAAAM,GAAAd,EAAAQ,EAAAL,EAAAW,GAAAtB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAAwB,KAAAjB,EAAAI,IAAA,MAAAY,UAAA,wCAAAvB,EAAAyB,KAAA,OAAAzB,EAAAW,EAAAX,EAAA9F,MAAAsG,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAmB,SAAA1B,EAAAwB,KAAAjB,GAAAC,EAAA,IAAAG,EAAAY,UAAA,oCAAAlB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAuB,KAAArB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAA7G,MAAA8F,EAAAyB,KAAAR,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAiB,IAAA,UAAAC,IAAA,CAAA5B,EAAAY,OAAAiB,eAAA,IAAArB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAiB,EAAAnB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAkB,eAAAlB,OAAAkB,eAAA/B,EAAA6B,IAAA7B,EAAAgC,UAAAH,EAAAd,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA4B,EAAAlB,UAAAmB,EAAAd,EAAAH,EAAA,cAAAiB,GAAAd,EAAAc,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAAlB,EAAAc,EAAAvB,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAsB,EAAA,kBAAAC,EAAA3B,EAAA4B,EAAApB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAAwB,eAAA,IAAA7B,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAsC,QAAApC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA/F,MAAAiG,EAAAmC,YAAAtC,EAAAuC,cAAAvC,EAAAwC,UAAAxC,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAAyC,EAAAtC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAArG,KAAA,OAAAiG,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAkB,KAAAzB,EAAAW,GAAA+B,QAAAC,QAAAhC,GAAAiC,KAAA3C,EAAAI,EAAA,UAAAwC,EAAA1C,GAAA,sBAAAH,EAAA,KAAAD,EAAA/G,UAAA,WAAA0J,QAAA,SAAAzC,EAAAI,GAAA,IAAAe,EAAAjB,EAAAb,MAAAU,EAAAD,GAAA,SAAA+C,EAAA3C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,OAAA5C,EAAA,UAAA4C,EAAA5C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,QAAA5C,EAAA,CAAA2C,OAAA,eAAAE,EAAAjD,EAAAE,GAAA,IAAAD,EAAAY,OAAAqC,KAAAlD,GAAA,GAAAa,OAAAsC,sBAAA,KAAA7C,EAAAO,OAAAsC,sBAAAnD,GAAAE,IAAAI,EAAAA,EAAA8C,OAAA,SAAAlD,GAAA,OAAAW,OAAAwC,yBAAArD,EAAAE,GAAAqC,UAAA,IAAAtC,EAAAP,KAAAH,MAAAU,EAAAK,EAAA,QAAAL,CAAA,UAAAzG,EAAAwG,GAAA,QAAAE,EAAA,EAAAA,EAAAjH,UAAAC,OAAAgH,IAAA,KAAAD,EAAA,MAAAhH,UAAAiH,GAAAjH,UAAAiH,GAAA,GAAAA,EAAA,EAAA+C,EAAApC,OAAAZ,IAAA,GAAA/E,QAAA,SAAAgF,GAAA7G,EAAA2G,EAAAE,EAAAD,EAAAC,GAAA,GAAAW,OAAAyC,0BAAAzC,OAAA0C,iBAAAvD,EAAAa,OAAAyC,0BAAArD,IAAAgD,EAAApC,OAAAZ,IAAA/E,QAAA,SAAAgF,GAAAW,OAAAwB,eAAArC,EAAAE,EAAAW,OAAAwC,yBAAApD,EAAAC,GAAA,UAAAF,CAAA,UAAAwD,EAAAxD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAhH,OAAA+G,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAiC,WAAAjC,EAAAiC,aAAA,EAAAjC,EAAAkC,cAAA,YAAAlC,IAAAA,EAAAmC,UAAA,GAAA5B,OAAAwB,eAAArC,EAAAyD,EAAAnD,EAAApG,KAAAoG,EAAA,WAAAjH,EAAA2G,EAAAE,EAAAD,GAAA,OAAAC,EAAAuD,EAAAvD,MAAAF,EAAAa,OAAAwB,eAAArC,EAAAE,EAAA,CAAA/F,MAAA8F,EAAAsC,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAzC,EAAAE,GAAAD,EAAAD,CAAA,UAAAyD,EAAAxD,GAAA,IAAAO,EAAA,SAAAP,EAAAC,GAAA,aAAAwD,EAAAzD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAwD,aAAA,YAAA3D,EAAA,KAAAQ,EAAAR,EAAAyB,KAAAxB,EAAAC,GAAA,wBAAAwD,EAAAlD,GAAA,OAAAA,EAAA,UAAAgB,UAAA,kEAAAtB,EAAA0D,OAAAC,QAAA5D,EAAA,CAAA6D,CAAA7D,EAAA,0BAAAyD,EAAAlD,GAAAA,EAAAA,EAAA,GAuCO,IAAMuD,EAAU,WAUrB,SAAAA,IAAkD,IAAtC/K,EAA+BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC7C,GAlDJ,SAAAoI,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAoB,UAAA,qCAiDkDpI,CAAA,KAAA2K,GAAA1K,EAAA,sBAAAA,EAAA,gBANA,MAAIA,EAAA,4CAAAA,EAAA,oBAE7B,IAAI2K,KAAa3K,EAAA,mBAClB,IAAI2K,KAAa3K,EAAA,kBAClB,IAAI4K,KAGnBF,EAAWzK,SACb,OAAOyK,EAAWzK,SAGpByK,EAAWzK,SAAWC,KAEtBA,KAAKP,OAAMQ,EAAA,CACT0K,WAAY,WACZC,UAAW,GACXC,aAAc,sBACdC,YAAa,qBACbC,WAAY,oBACZC,cAAe,uBACfC,cAAe,EACfC,WAAY,KACTzL,GAGLO,KAAKmL,6BAA+B,yBAA0B/J,OAC9DpB,KAAKU,MACP,CAEA,OAxEF+F,EAwEE+D,EAxEF7D,EAwEE,EAAAhG,IAAA,OAAAC,MAaA,WACMZ,KAAKmL,8BACPnL,KAAKoL,iBACLpL,KAAKqL,iBAELrL,KAAKsL,gBAIPtK,EAAAA,EAAAA,IAAK,0BAA2B,CAAEuK,kBAAmBvL,KAAKmL,8BAC5D,GAEA,CAAAxK,IAAA,iBAAAC,MAGA,WAA+B,IAAAY,EAAA,KAC7BxB,KAAKwL,SAAW,IAAIC,qBAAqB,SAACC,GACxCA,EAAQ/J,QAAQ,SAAAgK,GACd,GAAIA,EAAMC,eAAgB,CACxB,IAAMC,EAAMF,EAAMzI,OAClB1B,EAAKsK,UAAUD,GACfrK,EAAKgK,SAAUO,UAAUF,EAC3B,CACF,EACF,EAAG,CACDlB,WAAY3K,KAAKP,OAAOkL,WACxBC,UAAW5K,KAAKP,OAAOmL,WAE3B,GAEA,CAAAjK,IAAA,gBAAAC,MAGA,WAA8B,IAAA+C,EAAA,KACtBqI,EAAa5L,SAASyB,iBAAiB,4CAE7CmK,EAAWrK,QAAQ,SAAAkK,GACbA,aAAeI,mBACjBJ,EAAI1H,UAAUG,IAAIX,EAAKlE,OAAOuL,eAC9BrH,EAAK6H,SAAUxJ,QAAQ6J,GAE3B,GAEIG,EAAWrM,MAGjB,GAEA,CAAAgB,IAAA,YAAAC,OAAAsL,EAAA3C,EAAAZ,IAAAE,EAGA,SAAAsD,EAAwBN,GAAqB,IAAAO,EAAAC,EAAA,OAAA1D,IAAAC,EAAA,SAAA0D,GAAA,cAAAA,EAAA5E,EAAA4E,EAAAzF,GAAA,OAChB,GAArBuF,EAAMP,EAAIU,QAAQH,IACd,CAAFE,EAAAzF,EAAA,eAAAyF,EAAAxE,EAAA,UAImB,OAD3B+D,EAAI1H,UAAUG,IAAItE,KAAKP,OAAOoL,cAC9BgB,EAAIW,iBAAmBJ,EAAIE,EAAA5E,EAAA,EAAA4E,EAAAzF,EAAA,EAGnB7G,KAAKyM,aAAaL,GAAI,OAG5BP,EAAIO,IAAMA,EACVP,EAAI1H,UAAUC,OAAOpE,KAAKP,OAAOoL,cACjCgB,EAAI1H,UAAUG,IAAItE,KAAKP,OAAOqL,aAE9B9K,KAAK0M,aAAapI,IAAI8H,UAGfP,EAAIU,QAAQH,IAGnBP,EAAIc,cAAc,IAAIC,YAAY,aAAc,CAC9CC,OAAQ,CAAET,IAAAA,EAAKtK,QAAS+J,OAG1B7K,EAAAA,EAAAA,IAAK,oBAAqB,CAAEoL,IAAAA,EAAKtK,QAAS+J,IAAOS,EAAAzF,EAAA,sBAAAyF,EAAA5E,EAAA,EAAA2E,EAAAC,EAAAzE,EAAAyE,EAAAzF,EAAA,EAG3C7G,KAAK8M,iBAAiBjB,EAAGQ,GAAiB,cAAAC,EAAAxE,EAAA,KAAAqE,EAAA,iBAEnD,SA/BsBY,GAAA,OAAAb,EAAAlG,MAAC,KAADtG,UAAA,IAiCvB,CAAAiB,IAAA,eAAAC,MAGA,SAAqBwL,GACnB,OAAO,IAAIhD,QAAQ,SAACC,EAAS2D,GAC3B,IAAMC,EAAc,IAAIC,MAExBD,EAAYE,OAAS,kBAAM9D,GAAS,EACpC4D,EAAYG,QAAU,kBAAMJ,EAAO,IAAIK,MAAM,WAADzK,OAAYwJ,IAAO,EAE/Da,EAAYb,IAAMA,CACpB,EACF,GAEA,CAAAzL,IAAA,mBAAAC,OAAA0M,EAAA/D,EAAAZ,IAAAE,EAGA,SAAA0E,EAA+B1B,EAAuBhH,GAAY,IAAAuH,EAAAoB,EAAAlI,EAAA,YAAAqD,IAAAC,EAAA,SAAA6E,GAAA,cAAAA,EAAA5G,GAAA,OAErB,GADrCuF,EAAMP,EAAIW,kBAAoBX,EAAIU,QAAQH,KAAO,MACjDoB,EAAa3B,EAAI6B,iBAAmB,GAEzB1N,KAAKP,OAAOwL,eAAa,CAAAwC,EAAA5G,EAAA,QAQM,OAN9CgF,EAAI6B,gBAAkBF,EAAa,EAInCxJ,WAAW,WACTsB,EAAKwG,UAAUD,EACjB,EAAG7L,KAAKP,OAAOyL,YAAcsC,EAAa,IAAIC,EAAA3F,EAAA,UAMhD+D,EAAI1H,UAAUC,OAAOpE,KAAKP,OAAOoL,cACjCgB,EAAI1H,UAAUG,IAAItE,KAAKP,OAAOsL,YAE9B/K,KAAK2N,YAAYrJ,IAAI8H,GAGrBpM,KAAK4N,oBAAoB/B,GAGzBA,EAAIc,cAAc,IAAIC,YAAY,YAAa,CAC7CC,OAAQ,CAAET,IAAAA,EAAKvH,MAAAA,EAAO/C,QAAS+J,EAAK2B,WAAAA,OAGtCxM,EAAAA,EAAAA,IAAK,mBAAoB,CAAEoL,IAAAA,EAAKvH,MAAAA,EAAO/C,QAAS+J,EAAK2B,WAAAA,IAEJ,cAAAC,EAAA3F,EAAA,KAAAyF,EAAA,SAClD,SAlC6BM,EAAAC,GAAA,OAAAR,EAAAtH,MAAC,KAADtG,UAAA,IAoC9B,CAAAiB,IAAA,sBAAAC,MAGA,SAA4BiL,GAE1BA,EAAIO,IADgB,oWAEtB,GAEA,CAAAzL,IAAA,eAAAC,MAGA,WAA6B,IAAA8E,EAAA,KACRtF,SAASyB,iBAAiB,iBAElCF,QAAQ,SAAAkK,GACbA,aAAeI,kBACjBvG,EAAKoG,UAAUD,EAEnB,EAGF,GAEA,CAAAlL,IAAA,UAAAC,MAGA,WACOZ,KAAKmL,+BAEVnL,KAAKqL,iBACLrK,EAAAA,EAAAA,IAAK,yBACP,GAEA,CAAAL,IAAA,gBAAAC,OAAAmN,EAAAxE,EAAAZ,IAAAE,EAGA,SAAAmF,EAAoBC,GAAc,IAAAC,EAAAC,EAAA,YAAAxF,IAAAC,EAAA,SAAAwF,GAAA,cAAAA,EAAAvH,GAAA,UAC3BhB,MAAMwI,QAAQJ,GAAO,CAAFG,EAAAvH,EAAA,eAAAuH,EAAAtG,EAAA,UAStB,OAPIoG,EAAWD,EAAKK,IAAG,eAAAC,EAAAhF,EAAAZ,IAAAE,EAAC,SAAA2F,EAAOC,GAAG,OAAA9F,IAAAC,EAAA,SAAA8F,GAAA,cAAAA,EAAAhH,EAAAgH,EAAA7H,GAAA,cAAA6H,EAAAhH,EAAA,EAAAgH,EAAA7H,EAAA,EAE1BsH,EAAK1B,aAAagC,GAAI,OACOC,EAAA7H,EAAA,eAAA6H,EAAAhH,EAAA,EAAAgH,EAAA7G,EAES,cAAA6G,EAAA5G,EAAA,KAAA0G,EAAA,iBAE/C,gBAAAG,GAAA,OAAAJ,EAAAvI,MAAA,KAAAtG,UAAA,EAPwB,IAOvB0O,EAAAvH,EAAA,EAEIuC,QAAQwF,WAAWV,GAAS,QAClClN,EAAAA,EAAAA,IAAK,yBAA0B,CAAEiN,KAAAA,EAAMY,MAAOZ,EAAKtO,SAAU,cAAAyO,EAAAtG,EAAA,KAAAkG,EAAA,IAC9D,SAdkBc,GAAA,OAAAf,EAAA/H,MAAC,KAADtG,UAAA,IAgBnB,CAAAiB,IAAA,oBAAAC,MAGA,SAAkBiL,GACZA,EAAIU,QAAQH,KACdpM,KAAK8L,UAAUD,EAEnB,GAEA,CAAAlL,IAAA,WAAAC,MAGA,WAKE,MAAO,CACLmO,YALkB3O,SAASyB,iBAAiB,iBAAiBlC,OAM7D+M,aALmBtM,SAASyB,iBAAiB,IAADe,OAAK5C,KAAKP,OAAOqL,cAAenL,OAM5EgO,YALkBvN,SAASyB,iBAAiB,IAADe,OAAK5C,KAAKP,OAAOsL,aAAcpL,OAM1E4L,kBAAmBvL,KAAKmL,6BACxBF,cAAejL,KAAKgP,WAAWC,KAEnC,GAEA,CAAAtO,IAAA,YAAAC,MAGA,WACE,OAAAX,EAAA,GAAYD,KAAKP,OACnB,GAEA,CAAAkB,IAAA,eAAAC,MAGA,SAAaqF,GACXjG,KAAKP,OAAMQ,EAAAA,EAAA,GAAQD,KAAKP,QAAWwG,GAG/BjG,KAAKwL,WAAavF,EAAU0E,YAAc1E,EAAU2E,aACtD5K,KAAKwL,SAASpF,aACdpG,KAAKoL,iBACLpL,KAAKqL,kBAGPrK,EAAAA,EAAAA,IAAK,sBAAuBhB,KAAKP,OACnC,GAEA,CAAAkB,IAAA,cAAAC,MAGA,WACE,OAAOZ,KAAKwL,QACd,GAEA,CAAA7K,IAAA,sBAAAC,MAGA,WACE,OAAOZ,KAAKmL,4BACd,GAEA,CAAAxK,IAAA,UAAAC,MAGA,WACMZ,KAAKwL,WACPxL,KAAKwL,SAASpF,aACdpG,KAAKwL,SAAW,MAGlBxL,KAAK0M,aAAawC,QAClBlP,KAAK2N,YAAYuB,QACjBlP,KAAKgP,WAAWE,QAEhB1E,EAAWzK,SAAW,MACtBiB,EAAAA,EAAAA,IAAK,wBAEP,IAjWF0F,EAiWG,EAAA/F,IAAA,cAAAC,MAtRD,SAAmBnB,GAIjB,OAHK+K,EAAWzK,WACdyK,EAAWzK,SAAW,IAAIyK,EAAW/K,IAEhC+K,EAAWzK,QACpB,IAhFF4G,GAAAsD,EAAAxD,EAAAU,UAAAR,GAAAD,GAAAuD,EAAAxD,EAAAC,GAAAY,OAAAwB,eAAArC,EAAA,aAAAyC,UAAA,IAAAzC,EAAA,IAAAA,EAAAE,EAAAD,EA+PEqH,EAxEAT,EAlDApB,CArDC,CAzCoB,GAAApM,EAAV0K,EAAU,WACwB,MA6TxC,IAAM2E,EAAa3E,EAAWjE,eAGrCC,EAAAA,EAAAA,IAAM,WAEN,G,yRC1WA,IAAAC,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAA5G,KAAAsF,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAA/H,OAAA+G,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAM,EAAAf,EAAA,GAAAN,EAAA,GAAAI,EAAAiB,IAAAnB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAC,IAAAjB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAmB,KAAAf,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAmB,EAAAd,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAM,GAAA,GAAAP,EAAA,QAAAQ,UAAA,oCAAAN,GAAA,IAAAD,GAAAK,EAAAL,EAAAM,GAAAd,EAAAQ,EAAAL,EAAAW,GAAAtB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAAwB,KAAAjB,EAAAI,IAAA,MAAAY,UAAA,wCAAAvB,EAAAyB,KAAA,OAAAzB,EAAAW,EAAAX,EAAA9F,MAAAsG,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAmB,SAAA1B,EAAAwB,KAAAjB,GAAAC,EAAA,IAAAG,EAAAY,UAAA,oCAAAlB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAuB,KAAArB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAA7G,MAAA8F,EAAAyB,KAAAR,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAiB,IAAA,UAAAC,IAAA,CAAA5B,EAAAY,OAAAiB,eAAA,IAAArB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAiB,EAAAnB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAkB,eAAAlB,OAAAkB,eAAA/B,EAAA6B,IAAA7B,EAAAgC,UAAAH,EAAAd,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA4B,EAAAlB,UAAAmB,EAAAd,EAAAH,EAAA,cAAAiB,GAAAd,EAAAc,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAAlB,EAAAc,EAAAvB,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAsB,EAAA,kBAAAC,EAAA3B,EAAA4B,EAAApB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAAwB,eAAA,IAAA7B,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAsC,QAAApC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA/F,MAAAiG,EAAAmC,YAAAtC,EAAAuC,cAAAvC,EAAAwC,UAAAxC,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAAyC,EAAAtC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAArG,KAAA,OAAAiG,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAkB,KAAAzB,EAAAW,GAAA+B,QAAAC,QAAAhC,GAAAiC,KAAA3C,EAAAI,EAAA,UAAAwC,EAAA1C,GAAA,sBAAAH,EAAA,KAAAD,EAAA/G,UAAA,WAAA0J,QAAA,SAAAzC,EAAAI,GAAA,IAAAe,EAAAjB,EAAAb,MAAAU,EAAAD,GAAA,SAAA+C,EAAA3C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,OAAA5C,EAAA,UAAA4C,EAAA5C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,QAAA5C,EAAA,CAAA2C,OAAA,eAAAE,EAAAjD,EAAAE,GAAA,IAAAD,EAAAY,OAAAqC,KAAAlD,GAAA,GAAAa,OAAAsC,sBAAA,KAAA7C,EAAAO,OAAAsC,sBAAAnD,GAAAE,IAAAI,EAAAA,EAAA8C,OAAA,SAAAlD,GAAA,OAAAW,OAAAwC,yBAAArD,EAAAE,GAAAqC,UAAA,IAAAtC,EAAAP,KAAAH,MAAAU,EAAAK,EAAA,QAAAL,CAAA,UAAAzG,EAAAwG,GAAA,QAAAE,EAAA,EAAAA,EAAAjH,UAAAC,OAAAgH,IAAA,KAAAD,EAAA,MAAAhH,UAAAiH,GAAAjH,UAAAiH,GAAA,GAAAA,EAAA,EAAA+C,EAAApC,OAAAZ,IAAA,GAAA/E,QAAA,SAAAgF,GAAA7G,EAAA2G,EAAAE,EAAAD,EAAAC,GAAA,GAAAW,OAAAyC,0BAAAzC,OAAA0C,iBAAAvD,EAAAa,OAAAyC,0BAAArD,IAAAgD,EAAApC,OAAAZ,IAAA/E,QAAA,SAAAgF,GAAAW,OAAAwB,eAAArC,EAAAE,EAAAW,OAAAwC,yBAAApD,EAAAC,GAAA,UAAAF,CAAA,UAAAwD,EAAAxD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAhH,OAAA+G,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAiC,WAAAjC,EAAAiC,aAAA,EAAAjC,EAAAkC,cAAA,YAAAlC,IAAAA,EAAAmC,UAAA,GAAA5B,OAAAwB,eAAArC,EAAAyD,EAAAnD,EAAApG,KAAAoG,EAAA,WAAAjH,EAAA2G,EAAAE,EAAAD,GAAA,OAAAC,EAAAuD,EAAAvD,MAAAF,EAAAa,OAAAwB,eAAArC,EAAAE,EAAA,CAAA/F,MAAA8F,EAAAsC,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAzC,EAAAE,GAAAD,EAAAD,CAAA,UAAAyD,EAAAxD,GAAA,IAAAO,EAAA,SAAAP,EAAAC,GAAA,aAAAwD,EAAAzD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAwD,aAAA,YAAA3D,EAAA,KAAAQ,EAAAR,EAAAyB,KAAAxB,EAAAC,GAAA,wBAAAwD,EAAAlD,GAAA,OAAAA,EAAA,UAAAgB,UAAA,kEAAAtB,EAAA0D,OAAAC,QAAA5D,EAAA,CAAA6D,CAAA7D,EAAA,0BAAAyD,EAAAlD,GAAAA,EAAAA,EAAA,GA0CO,IAAMmI,EAAiB,WAO5B,SAAAA,IAAyD,IAA7C3P,EAAsCC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpD,GAlDJ,SAAAoI,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAoB,UAAA,qCAiDyDpI,CAAA,KAAAuP,GAAAtP,EAAA,sBAAAA,EAAA,qBAH/B,IAAI4K,KAAsB5K,EAAA,kBAC7B,IAAI4K,KAGnB0E,EAAkBrP,SACpB,OAAOqP,EAAkBrP,SAG3BqP,EAAkBrP,SAAWC,KAE7BA,KAAKP,OAAMQ,EAAA,CACToP,aAAc,IACdpE,cAAe,EACfC,WAAY,IACZoE,UAAW,IACR7P,GAGLO,KAAKU,MACP,CAEA,OAnEF+F,EAmEE2I,EAnEFzI,EAmEE,EAAAhG,IAAA,OAAAC,MAaA,WACEZ,KAAKc,uBAELE,EAAAA,EAAAA,IAAK,iCACP,GAEA,CAAAL,IAAA,sBAAAC,MAGA,WAEER,SAASa,iBAAiB,QAASjB,KAAKuP,oBAAoBpO,KAAKnB,MACnE,GAEA,CAAAW,IAAA,sBAAAC,MAGA,SAA4BkE,GAC1B,IACM0K,EADS1K,EAAM5B,OACC8B,QAAQ,4BAE1BwK,IACF1K,EAAMG,iBACNjF,KAAKyP,SAASD,GAElB,GAEA,CAAA7O,IAAA,WAAAC,OAAA8O,EAAAnG,EAAAZ,IAAAE,EAGA,SAAAsD,EAAeqD,GAAyB,IAAAG,EAAAC,EAAAC,EAAAxD,EAAA,OAAA1D,IAAAC,EAAA,SAAA0D,GAAA,cAAAA,EAAA5E,EAAA4E,EAAAzF,GAAA,OACyB,GAAzD8I,EAAYH,EAAOxK,QAAQ,+BACjB,CAAFsH,EAAAzF,EAAA,QACwB,OAAAyF,EAAAxE,EAAA,UAMtC,GAFM8H,EAAcD,EAAUtM,IAAMrD,KAAK8P,uBAGrC9P,KAAK+P,cAAcC,IAAIJ,GAAc,CAAFtD,EAAAzF,EAAA,eAAAyF,EAAAxE,EAAA,UAMK,OANLwE,EAAA5E,EAAA,EAKrC1H,KAAKiQ,gBAAgBT,GAAQ,GAC7BxP,KAAK+P,cAAcG,IAAIN,GAAa,GAAMtD,EAAAzF,EAAA,EAEvB7G,KAAKmQ,cAAcR,GAAU,OAAtC,MAAJE,EAAIvD,EAAAzE,IAEEgI,EAAKO,QAAQzQ,OAAS,GAAC,CAAA2M,EAAAzF,EAAA,eAAAyF,EAAAzF,EAAA,EAC3B7G,KAAKqQ,cAAcV,EAAWE,EAAKO,SAAQ,OAG5CP,EAAKS,SACRtQ,KAAKuQ,mBAAmBf,GAI1BL,EAAWqB,WAEXxP,EAAAA,EAAAA,IAAK,2BAA4B,CAC/B4O,YAAAA,EACAa,YAAaZ,EAAKO,QAAQzQ,OAC1B2Q,QAAST,EAAKS,UAG2ChE,EAAAzF,EAAA,eAE3D7G,KAAKuQ,mBAAmBf,GACS,OAAAlD,EAAAzF,EAAA,eAAAyF,EAAA5E,EAAA,EAAA2E,EAAAC,EAAAzE,EAInC7H,KAAK0Q,gBAAgBlB,EAAMnD,IAC3BrL,EAAAA,EAAAA,IAAK,yBAA0B,CAAE4O,YAAAA,EAAa/K,MAAKwH,IAAI,OAGZ,OAHYC,EAAA5E,EAAA,EAEvD1H,KAAKiQ,gBAAgBT,GAAQ,GAC7BxP,KAAK+P,cAAcG,IAAIN,GAAa,GAAOtD,EAAA7E,EAAA,iBAAA6E,EAAAxE,EAAA,KAAAqE,EAAA,qBAE9C,SAlDaY,GAAA,OAAA2C,EAAA1J,MAAC,KAADtG,UAAA,IAoDd,CAAAiB,IAAA,gBAAAC,OAAA+P,EAAApH,EAAAZ,IAAAE,EAGA,SAAA0E,EAA4BoC,GAAsB,IAAAiB,EAAAf,EAAAgB,EAAAC,EAAAC,EAAAvP,EAAA,YAAAmH,IAAAC,EAAA,SAAA6E,GAAA,cAAAA,EAAA/F,EAAA+F,EAAA5G,GAAA,OACH,KAAvC+J,EAAcjB,EAAUpD,QAAQ6D,SAErB,CAAF3C,EAAA5G,EAAA,QAKX,OALW4G,EAAA/F,EAAA,EAGLmI,EAAOmB,KAAKC,MAAMC,KAAKN,IAE7BnD,EAAA5G,EAAA,EACM,IAAIuC,QAAQ,SAAAC,GAAO,OAAIrF,WAAWqF,EAAS7H,EAAK/B,OAAO4P,aAAa,GAAC,cAAA5B,EAAA3F,EAAA,EAEpE+H,GAAI,aAAApC,EAAA/F,EAAA,EAAA+F,EAAA5F,EAEL,IAAIwF,MAAM,UAAS,OAIgB,GAArCwD,EAAWlB,EAAUpD,QAAQsE,SACpB,CAAFpD,EAAA5G,EAAA,cACL,IAAIwG,MAAM,WAAU,OAGgB,OAAtCyD,EAAS9Q,KAAKmR,cAAcxB,GAAUlC,EAAA5G,EAAA,GACrBuK,EAAAA,EAAAA,IAAKP,EAAUC,GAAO,OAA/B,KAARC,EAAQtD,EAAA5F,GAEDgI,KAAKwB,QAAS,CAAF5D,EAAA5G,EAAA,eAAA4G,EAAA3F,EAAA,EAChBiJ,EAASlB,KAAKA,MAAI,aAEnB,IAAIxC,MAAM0D,EAASlB,KAAKyB,SAAW,UAAS,cAAA7D,EAAA3F,EAAA,KAAAyF,EAAA,iBAGvD,SA/B0BM,GAAA,OAAA8C,EAAA3K,MAAC,KAADtG,UAAA,IAiC3B,CAAAiB,IAAA,gBAAAC,MAGA,SAAsB+O,GACpB,IAAMmB,EAA8B,CAClCS,WAAYvR,KAAKP,OAAO6P,WAW1B,OAPAhI,OAAOqC,KAAKgG,EAAUpD,SAAS5K,QAAQ,SAAAhB,GACrC,GAAIA,EAAIoC,WAAW,SAAU,CAC3B,IAAMyO,EAAY7Q,EAAIsC,QAAQ,QAAS,IAAIwO,cAC3CX,EAAOU,GAAa7B,EAAUpD,QAAQ5L,EACxC,CACF,GAEOmQ,CACT,GAEA,CAAAnQ,IAAA,gBAAAC,OAAA8Q,EAAAnI,EAAAZ,IAAAE,EAGA,SAAA2F,EAA4BmB,EAAwBS,GAAyB,IAAAuB,EAAAC,EAAAC,EAAAlO,EAAA,YAAAgF,IAAAC,EAAA,SAAA8F,GAAA,cAAAA,EAAA7H,GAAA,OACI,GAAzE8K,EAAmBhC,EAAUzN,cAAc,+BAC1B,CAAFwM,EAAA7H,EAAA,cACb,IAAIwG,MAAM,WAAU,OAGtBuE,EAAOxB,EAAQ9B,IAAI,SAAAwD,GAAM,OAAInO,EAAKoO,aAAaD,EAAO,GAAEE,KAAK,KAG7DH,EAAUzR,SAAS6R,cAAc,QAC/BC,UAAYN,EACpBC,EAAQvR,MAAM6R,QAAU,IACxBN,EAAQvR,MAAM8R,WAAa,2BAE3BT,EAAiBU,YAAYR,GAG7B7N,WAAW,WACT6N,EAAQvR,MAAM6R,QAAU,GAC1B,EAAG,IAGHnO,WAAW,WACT,KAAO6N,EAAQS,YACbX,EAAiBU,YAAYR,EAAQS,YAEvCT,EAAQzN,QACV,EAAG,KAAK,cAAAsK,EAAA5G,EAAA,KAAA0G,EAAA,IACT,SA5B0BV,EAAAgB,GAAA,OAAA4C,EAAA1L,MAAC,KAADtG,UAAA,IA8B3B,CAAAiB,IAAA,eAAAC,MAGA,SAAqBkR,GACnB,IAAMS,EAAQvS,KAAKwS,aAAaV,EAAOW,YACjCpP,EAAKyO,EAAOzO,GAAGqP,UAAU,EAAG,GAElC,MAAO,+DAAP9P,OACwDkP,EAAOzO,GAAE,iDAAAT,OAC1B5C,KAAK2S,WAAWJ,GAAM,uNAAA3P,OAIfS,EAAE,+LAAAT,OAIF5C,KAAK4S,WAAWd,EAAOe,cAAa,gEAKpF,GAEA,CAAAlS,IAAA,eAAAC,MAGA,SAAqB6R,GACnB,IAAK,IAALK,EAAA,EAAAC,EAAuBzL,OAAO0L,OAAOP,GAAWK,EAAAC,EAAApT,OAAAmT,IAAE,CAA7C,IAAMG,EAAQF,EAAAD,GACjB,GAAsB,UAAlBG,EAASC,MAAoBD,EAASV,OAASU,EAASV,MAAM5S,OAAS,EACzE,OAAOsT,EAASV,MAAM,GAAGY,YAAc,KAE3C,CACA,MAAO,KACT,GAEA,CAAAxS,IAAA,kBAAAC,MAGA,SAAwB4O,EAA2B4D,GACjD,IAAMC,EAAc7D,EAAOtN,cAAc,wBACnCoR,EAAiB9D,EAAOtN,cAAc,2BACtCqR,EAAa/D,EAAOtN,cAAc,uBAEpCkR,GACF5D,EAAOgE,UAAW,EACdD,IAAYA,EAAWjT,MAAMmT,QAAU,QACvCJ,IAAaA,EAAY/S,MAAMmT,QAAU,UACzCH,IAAgBA,EAAehT,MAAMmT,QAAU,YAEnDjE,EAAOgE,UAAW,EACdD,IAAYA,EAAWjT,MAAMmT,QAAU,UACvCJ,IAAaA,EAAY/S,MAAMmT,QAAU,QACzCH,IAAgBA,EAAehT,MAAMmT,QAAU,QAEvD,GAEA,CAAA9S,IAAA,kBAAAC,MAGA,SAAwB4O,EAA2B3K,GAAoB,IAAAS,EAAA,KAC/D+N,EAAc7D,EAAOtN,cAAc,wBAErCmR,IACFA,EAAYK,YAAc,WAC1BL,EAAY/S,MAAMmT,QAAU,UAM9BzP,WAAW,WACLqP,IACFA,EAAYK,YAAc,UAE5BpO,EAAK2K,gBAAgBT,GAAQ,EAC/B,EAAG,IACL,GAEA,CAAA7O,IAAA,qBAAAC,MAGA,SAA2B4O,GACzB,IAAMmE,EAAkBnE,EAAOoE,cAC3BD,IACFA,EAAgBrT,MAAM8R,WAAa,wBACnCuB,EAAgBrT,MAAM6R,QAAU,IAEhCnO,WAAW,WACT2P,EAAgBrT,MAAMmT,QAAU,MAClC,EAAG,KAEP,GAEA,CAAA9S,IAAA,sBAAAC,MAGA,WACE,MAAO,eAAPgC,OAAsBiR,KAAKC,MAAK,KAAAlR,OAAIP,KAAK0R,SAASC,SAAS,IAAItB,UAAU,EAAG,GAC9E,GAEA,CAAA/R,IAAA,aAAAC,MAGA,SAAmBqT,GACjB,IAAMC,EAAM9T,SAAS6R,cAAc,OAEnC,OADAiC,EAAIR,YAAcO,EACXC,EAAIhC,SACb,GAEA,CAAAvR,IAAA,aAAAC,MAGA,SAAmBuT,GACjB,IAEE,OADa,IAAIN,KAAKM,GACVC,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,UACPC,IAAK,WAET,CAAE,MAAAC,GACA,OAAOL,CACT,CACF,GAEA,CAAAxT,IAAA,YAAAC,MAGA,WACE,OAAAX,EAAA,GAAYD,KAAKP,OACnB,GAEA,CAAAkB,IAAA,eAAAC,MAGA,SAAaqF,GACXjG,KAAKP,OAAMQ,EAAAA,EAAA,GAAQD,KAAKP,QAAWwG,IACnCjF,EAAAA,EAAAA,IAAK,6BAA8BhB,KAAKP,OAC1C,GAEA,CAAAkB,IAAA,mBAAAC,MAGA,WACE,OAAO,IAAI8J,IAAI1K,KAAK+P,cACtB,GAEA,CAAApP,IAAA,oBAAAC,MAGA,SAAkBgP,GAChB5P,KAAK+P,cAAc0E,OAAO7E,GAC1B5P,KAAK0U,WAAWD,OAAO7E,EACzB,GAEA,CAAAjP,IAAA,UAAAC,MAGA,WACER,SAASoE,oBAAoB,QAASxE,KAAKuP,qBAE3CvP,KAAK+P,cAAcb,QACnBlP,KAAK0U,WAAWxF,QAEhBE,EAAkBrP,SAAW,MAC7BiB,EAAAA,EAAAA,IAAK,+BAEP,IAlaF0F,EAkaG,EAAA/F,IAAA,cAAAC,MA5VD,SAAmBnB,GAIjB,OAHK2P,EAAkBrP,WACrBqP,EAAkBrP,SAAW,IAAIqP,EAAkB3P,IAE9C2P,EAAkBrP,QAC3B,IA3EF4G,GAAAsD,EAAAxD,EAAAU,UAAAR,GAAAD,GAAAuD,EAAAxD,EAAAC,GAAAY,OAAAwB,eAAArC,EAAA,aAAAyC,UAAA,IAAAzC,EAAA,IAAAA,EAAAE,EAAAD,EAyNEgL,EAvDAf,EAvDAjB,CAhCC,CAjC2B,GAAA5P,EAAjBsP,EAAiB,WACwB,MA2X/C,IAAMuF,EAAoBvF,EAAkB7I,eAInDC,EAAAA,EAAAA,IAAM,WAEN,G,sxCC5aA,IAAAC,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,EAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAA5G,KAAAsF,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAA/H,OAAA+G,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAM,EAAAf,EAAA,GAAAN,EAAA,GAAAI,EAAAiB,IAAAnB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAC,IAAAjB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAmB,KAAAf,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAmB,EAAAd,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAM,GAAA,GAAAP,EAAA,QAAAQ,UAAA,oCAAAN,GAAA,IAAAD,GAAAK,EAAAL,EAAAM,GAAAd,EAAAQ,EAAAL,EAAAW,GAAAtB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAAwB,KAAAjB,EAAAI,IAAA,MAAAY,UAAA,wCAAAvB,EAAAyB,KAAA,OAAAzB,EAAAW,EAAAX,EAAA9F,MAAAsG,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAmB,SAAA1B,EAAAwB,KAAAjB,GAAAC,EAAA,IAAAG,EAAAY,UAAA,oCAAAlB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAuB,KAAArB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAA7G,MAAA8F,EAAAyB,KAAAR,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAiB,IAAA,UAAAC,IAAA,CAAA5B,EAAAY,OAAAiB,eAAA,IAAArB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,EAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAiB,EAAAnB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAkB,eAAAlB,OAAAkB,eAAA/B,EAAA6B,IAAA7B,EAAAgC,UAAAH,EAAAd,EAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA4B,EAAAlB,UAAAmB,EAAAd,EAAAH,EAAA,cAAAiB,GAAAd,EAAAc,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAAlB,EAAAc,EAAAvB,EAAA,qBAAAS,EAAAH,GAAAG,EAAAH,EAAAN,EAAA,aAAAS,EAAAH,EAAAR,EAAA,yBAAAW,EAAAH,EAAA,oDAAAsB,EAAA,kBAAAC,EAAA3B,EAAA4B,EAAApB,EAAA,cAAAD,EAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAAwB,eAAA,IAAA7B,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,EAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,EAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAsC,QAAApC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA/F,MAAAiG,EAAAmC,YAAAtC,EAAAuC,cAAAvC,EAAAwC,UAAAxC,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,EAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAAyC,EAAAtC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAArG,KAAA,OAAAiG,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAkB,KAAAzB,EAAAW,GAAA+B,QAAAC,QAAAhC,GAAAiC,KAAA3C,EAAAI,EAAA,UAAAwC,EAAA1C,GAAA,sBAAAH,EAAA,KAAAD,EAAA/G,UAAA,WAAA0J,QAAA,SAAAzC,EAAAI,GAAA,IAAAe,EAAAjB,EAAAb,MAAAU,EAAAD,GAAA,SAAA+C,EAAA3C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,OAAA5C,EAAA,UAAA4C,EAAA5C,GAAAsC,EAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,QAAA5C,EAAA,CAAA2C,OAAA,eAAAE,EAAAjD,EAAAE,GAAA,IAAAD,EAAAY,OAAAqC,KAAAlD,GAAA,GAAAa,OAAAsC,sBAAA,KAAA7C,EAAAO,OAAAsC,sBAAAnD,GAAAE,IAAAI,EAAAA,EAAA8C,OAAA,SAAAlD,GAAA,OAAAW,OAAAwC,yBAAArD,EAAAE,GAAAqC,UAAA,IAAAtC,EAAAP,KAAAH,MAAAU,EAAAK,EAAA,QAAAL,CAAA,UAAAzG,EAAAwG,GAAA,QAAAE,EAAA,EAAAA,EAAAjH,UAAAC,OAAAgH,IAAA,KAAAD,EAAA,MAAAhH,UAAAiH,GAAAjH,UAAAiH,GAAA,GAAAA,EAAA,EAAA+C,EAAApC,OAAAZ,IAAA,GAAA/E,QAAA,SAAAgF,GAAA7G,EAAA2G,EAAAE,EAAAD,EAAAC,GAAA,GAAAW,OAAAyC,0BAAAzC,OAAA0C,iBAAAvD,EAAAa,OAAAyC,0BAAArD,IAAAgD,EAAApC,OAAAZ,IAAA/E,QAAA,SAAAgF,GAAAW,OAAAwB,eAAArC,EAAAE,EAAAW,OAAAwC,yBAAApD,EAAAC,GAAA,UAAAF,CAAA,UAAAwD,EAAAxD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAhH,OAAA+G,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAiC,WAAAjC,EAAAiC,aAAA,EAAAjC,EAAAkC,cAAA,YAAAlC,IAAAA,EAAAmC,UAAA,GAAA5B,OAAAwB,eAAArC,EAAAyD,EAAAnD,EAAApG,KAAAoG,EAAA,WAAAjH,EAAA2G,EAAAE,EAAAD,GAAA,OAAAC,EAAAuD,EAAAvD,MAAAF,EAAAa,OAAAwB,eAAArC,EAAAE,EAAA,CAAA/F,MAAA8F,EAAAsC,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAzC,EAAAE,GAAAD,EAAAD,CAAA,UAAAyD,EAAAxD,GAAA,IAAAO,EAAA,SAAAP,EAAAC,GAAA,aAAAwD,EAAAzD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAwD,aAAA,YAAA3D,EAAA,KAAAQ,EAAAR,EAAAyB,KAAAxB,EAAAC,GAAA,wBAAAwD,EAAAlD,GAAA,OAAAA,EAAA,UAAAgB,UAAA,kEAAAtB,EAAA0D,OAAAC,QAAA5D,EAAA,CAAA6D,CAAA7D,EAAA,0BAAAyD,EAAAlD,GAAAA,EAAAA,EAAA,GAkEO,IAAM2N,EAAiB,WAW5B,SAAAA,IAAmD,IAAvCnV,EAAgCC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,GA9EJ,SAAAoI,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAoB,UAAA,qCA6EmDpI,CAAA,KAAA+U,GAAA9U,EAAA,sBAAAA,EAAA,uBAAAA,EAAA,4BAAAA,EAAA,qBALzB,IAAI4K,KAAkB5K,EAAA,uBACpB,IAAI2K,KAAa3K,EAAA,4BACiB,MAAIA,EAAA,wBACd,MAG5C8U,EAAkB7U,SACpB,OAAO6U,EAAkB7U,SAG3B6U,EAAkB7U,SAAWC,KAE7BA,KAAKP,OAAMQ,EAAA,CACT4U,IAAK,CACHC,SAAS,EACTC,QAAS,GACTC,iBAAiB,EACjBvP,QAAS,KAEXwP,YAAa,CACXC,UAAU,EACVC,iBAAkB,EAClBlK,cAAe,EACfC,WAAY,KAEdkK,YAAa,CACXN,SAAS,EACTO,eAAgB,IAChBC,gBAAiB,IAEnBC,kBAAmB,CACjBT,SAAS,EACTU,WAAY,IACZC,gBAAiB,GACjBC,eAAgB,EAChBC,oBAAqB,IAEvBC,WAAY,CACVd,SAAS,EACTe,aAAc,SACdC,IAAK,MACLC,oBAAoB,EACpBC,cAAc,IAEbvW,GAGLO,KAAKiW,QAAU,CACbC,UAAW,GACXC,OAAQ,GACRC,UAAW,EACXC,cAAe,EACfC,eAAgB,EAChBC,mBAAoB,EACpBC,UAAW,GAGbxW,KAAKyW,aAAe,CAClBC,YAAa,EACbC,aAAc,GACdC,cAAe,GACfC,aAAchD,KAAKC,OAGrB9T,KAAKU,MACP,CAEA,OA3IF+F,EA2IEmO,EA3IFjO,EA2IE,EAAAhG,IAAA,OAAAC,MAaA,WACEZ,KAAK8W,4BACL9W,KAAK+W,yBACL/W,KAAKgX,6BACLhX,KAAKiX,mBAGLjW,EAAAA,EAAAA,IAAK,iCACP,GAEA,CAAAL,IAAA,4BAAAC,MAGA,WAA0C,IAAAY,EAAA,KACpC0V,EAAc9V,OAAO+V,QACrBC,EAAkBvD,KAAKC,MAGrBuD,EAAerX,KAAKsX,SAAS,WACjC,IAAMC,EAAiBnW,OAAO+V,QACxBK,EAAc3D,KAAKC,MACnB2D,EAAWpV,KAAKqV,IAAIH,EAAiBL,GACrCS,EAAOH,EAAcJ,EAEvBO,EAAO,IACTnW,EAAKiV,aAAaC,YAAce,EAAWE,GAG7CT,EAAcK,EACdH,EAAkBI,EAClBhW,EAAKiV,aAAaI,aAAeW,CACnC,EAAG,KA+BHpW,OAAOH,iBAAiB,SAAUoW,EAAc,CAAEO,SAAS,IAC3DxX,SAASa,iBAAiB,YA7BF,SAACwF,GACvB,IAAMvD,EAASuD,EAAEvD,OACjB,GAAuB,MAAnBA,EAAO2U,QAAiB,CAC1B,IAAM3S,EAAQhC,EAA6BgC,KACvCA,IAAS1D,EAAKiV,aAAaE,aAAamB,SAAS5S,KACnD1D,EAAKiV,aAAaE,aAAaxQ,KAAKjB,GACpC1D,EAAKuW,gBAAgB7S,GAEzB,CACA1D,EAAKiV,aAAaI,aAAehD,KAAKC,KACxC,EAmBwD,CAAE8D,SAAS,IACnExX,SAASa,iBAAiB,QAjBN,SAACwF,GACnB,IAAMvD,EAASuD,EAAEvD,OACjB,GAAuB,MAAnBA,EAAO2U,QAAiB,CAC1B,IAAM3S,EAAQhC,EAA6BgC,KACvCA,IACF1D,EAAKiV,aAAaG,cAAczQ,KAAKjB,GAEjC1D,EAAKiV,aAAaG,cAAcjX,OAAS,IAC3C6B,EAAKiV,aAAaG,cAAcoB,QAGtC,CACAxW,EAAKiV,aAAaI,aAAehD,KAAKC,KACxC,EAIgD,CAAE8D,SAAS,GAC7D,GAEA,CAAAjX,IAAA,yBAAAC,MAGA,WAAuC,IAAA+C,EAAA,KAChC3D,KAAKP,OAAO8V,kBAAkBT,UAGnC9U,KAAKiY,qBAAuB,IAAIxM,qBAAqB,SAACC,GACpDA,EAAQ/J,QAAQ,SAAAgK,GACd,GAAIA,EAAMC,eAAgB,CACxB,IAAM9J,EAAU6J,EAAMzI,OACtBS,EAAKuU,6BAA6BpW,EACpC,CACF,EACF,EAAG,CACD6I,WAAY,QACZC,UAAW,KAIbxK,SAASyB,iBAAiB,WAAWF,QAAQ,SAAAoD,GAC3CpB,EAAKsU,qBAAsBjW,QAAQ+C,EACrC,GACF,GAEA,CAAApE,IAAA,+BAAAC,MAGA,SAAqCkB,GACnC,GAAwB,MAApBA,EAAQ+V,QAAZ,CAEA,IAAM3S,EAAQpD,EAA8BoD,KACvCA,IAAQlF,KAAKmY,gBAAgBC,IAAIlT,IAGnBlF,KAAKqY,8BAA8BnT,IAEpClF,KAAKP,OAAO8V,kBAAkBI,qBAC9C3V,KAAK+X,gBAAgB7S,EATY,CAWrC,GAEA,CAAAvE,IAAA,gCAAAC,MAGA,SAAsCsE,GACpC,IAAIoT,EAAa,EAGbtY,KAAKyW,aAAaE,aAAamB,SAAS5S,KAC1CoT,GAAc,IAIhB,IAAMC,EAAavY,KAAKyW,aAAaG,cAAc/M,OAAO,SAAA2O,GAAO,OAAIA,IAAYtT,CAAI,GAAEvF,OAcvF,OAbA2Y,GAAcjW,KAAKoW,IAAiB,GAAbF,EAAkB,IAGrCvY,KAAKyW,aAAaC,YAAc,IAClC4B,GAAc,IAIczE,KAAKC,MAAQ9T,KAAKyW,aAAaI,aACjC,MAC1ByB,GAAc,IAGTjW,KAAKoW,IAAIH,EAAY,EAC9B,GAEA,CAAA3X,IAAA,kBAAAC,MAGA,SAAwBsE,GAAoB,IAAAI,EAAA,KACtCtF,KAAKmY,gBAAgBlJ,MAAQjP,KAAKP,OAAO8V,kBAAkBG,iBAI/D1V,KAAKmY,gBAAgB7T,IAAIY,GACzBlF,KAAKiW,QAAQM,qBAGbvS,WAAW,WACTsB,EAAKoT,gBAAgBxT,EACvB,EAAGlF,KAAKP,OAAO8V,kBAAkBC,YACnC,GAEA,CAAA7U,IAAA,kBAAAC,OAAA+X,EAAApP,EAAAZ,IAAAE,EAGA,SAAAsD,EAA8BjH,GAAY,IAAAH,EAAA,OAAA4D,IAAAC,EAAA,SAAA0D,GAAA,cAAAA,EAAAzF,GAAA,OACxC,KACQ9B,EAAO3E,SAAS6R,cAAc,SAC/B2G,IAAM,WACX7T,EAAKG,KAAOA,EACZ9E,SAASyY,KAAKxG,YAAYtN,GAE1B/E,KAAKiW,QAAQK,kBACbtV,EAAAA,EAAAA,IAAK,qBAAsB,CAAEkE,KAAAA,EAAMmM,SAAS,GAE9C,CAAE,MAAOxM,IAEP7D,EAAAA,EAAAA,IAAK,qBAAsB,CAAEkE,KAAAA,EAAMmM,SAAS,EAAOxM,MAAAA,GACrD,CAAC,cAAAyH,EAAAxE,EAAA,KAAAqE,EAAA,SACF,SAd4BY,GAAA,OAAA4L,EAAA3S,MAAC,KAADtG,UAAA,IAgB7B,CAAAiB,IAAA,6BAAAC,MAGA,WAA2C,IAAA8E,EAAA,KACpC1F,KAAKP,OAAO2V,YAAYN,UAGzB,wBAAyB1T,QACV,IAAI0X,oBAAoB,SAACC,GACxCA,EAAKC,aAAarX,QAAQ,SAAAgK,GACA,aAApBA,EAAMsN,YACRvT,EAAKuQ,QAAQC,UAAU/P,KAAKwF,EAAMuN,UAClCxT,EAAKuQ,QAAQI,gBAEjB,EACF,GAESrU,QAAQ,CAAEmX,WAAY,CAAC,cAI9BnZ,KAAKP,OAAO2V,YAAYC,eAAiB,IAC3CrV,KAAKoZ,iBAAmBC,YAAY,WAClC3T,EAAK4T,0BACP,EAAGtZ,KAAKP,OAAO2V,YAAYC,iBAE/B,GAEA,CAAA1U,IAAA,2BAAAC,MAGA,WACE,IAAMqV,EAAUjW,KAAKuZ,yBAGrBvY,EAAAA,EAAAA,IAAK,8BAA+BiV,GAGhCjW,KAAKP,OAAO2V,YAAYE,iBAC1BtV,KAAKwZ,oBAAoBvD,EAE7B,GAEA,CAAAtV,IAAA,sBAAAC,OAAA6Y,EAAAlQ,EAAAZ,IAAAE,EAGA,SAAA0E,EAAkC0I,GAAY,OAAAtN,IAAAC,EAAA,SAAA6E,GAAA,cAAAA,EAAA/F,EAAA+F,EAAA5G,GAAA,cAAA4G,EAAA/F,EAAA,EAAA+F,EAAA5G,EAAA,EAEpC6S,MAAM1Z,KAAKP,OAAO2V,YAAYE,gBAAiB,CACnDqE,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAM7I,KAAK8I,UAAU7D,KACrB,OAAAxI,EAAA5G,EAAA,eAAA4G,EAAA/F,EAAA,EAAA+F,EAAA5F,EAEoC,cAAA4F,EAAA3F,EAAA,KAAAyF,EAAA,iBAEzC,SAZgCM,GAAA,OAAA4L,EAAAzT,MAAC,KAADtG,UAAA,IAcjC,CAAAiB,IAAA,kBAAAC,MAGA,WAAgC,IAAAuN,EAAA,KACzBnO,KAAKP,OAAOmW,WAAWd,UAG5B9U,KAAK+Z,uBAGLV,YAAY,WACVlL,EAAK6L,mBACP,EAAG,KACL,GAEA,CAAArZ,IAAA,uBAAAC,MAGA,WACE,IACE,IAAMqZ,EAASC,aAAaC,QAAQ,yBACpC,GAAIF,EAAQ,CACV,IAAMpK,EAAOmB,KAAKC,MAAMgJ,GACxBja,KAAKoa,cAAgB,IAAI1P,IAAImF,EAAKnE,SAClC1L,KAAKiW,QAAQO,UAAY3G,EAAKZ,MAAQ,CACxC,CACF,CAAE,MAAOpK,GAET,CACF,GAEA,CAAAlE,IAAA,oBAAAC,MAGA,WACE,IAGuDyZ,EAHjDvG,EAAMD,KAAKC,MACbwG,EAAU,EAAEC,E,goBAAAC,CAEWxa,KAAKoa,cAAc1O,WAAS,IAAvD,IAAA6O,EAAAE,MAAAJ,EAAAE,EAAA1T,KAAAsB,MAAyD,KAAAuS,EAAAC,EAAAN,EAAAzZ,MAAA,GAA7CD,EAAG+Z,EAAA,GAAE9Z,EAAK8Z,EAAA,GAChB9Z,EAAMga,SAAWha,EAAMga,QAAU9G,IACnC9T,KAAKoa,cAAc3F,OAAO9T,GAC1B2Z,IAEJ,CAAC,OAAAO,GAAAN,EAAA9T,EAAAoU,EAAA,SAAAN,EAAA9S,GAAA,CAEG6S,EAAU,GACZta,KAAK8a,oBAGT,GAEA,CAAAna,IAAA,qBAAAC,MAGA,WACE,IACE,IAAMiP,EAAO,CACXnE,QAAS7F,MAAMkV,KAAK/a,KAAKoa,cAAc1O,WACvCuD,KAAMjP,KAAKiW,QAAQO,UACnBwE,UAAWnH,KAAKC,OAGlBoG,aAAae,QAAQ,wBAAyBjK,KAAK8I,UAAUjK,GAC/D,CAAE,MAAOhL,GAET,CACF,GAEA,CAAAlE,IAAA,WAAAC,MAGA,SACE2E,EACA2V,GACkC,IAAAC,EAAA,KAC9BC,GAAa,EAEjB,OAAO,WACL,IAAKA,EAAY,SAAAzV,EAAAjG,UAAAC,OADRiG,EAAI,IAAAC,MAAAF,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAJF,EAAIE,GAAApG,UAAAoG,GAEXP,EAAKS,MAAMmV,EAAMvV,GACjBwV,GAAa,EACbpX,WAAW,WACToX,GAAa,CACf,EAAGF,EACL,CACF,CACF,GAEA,CAAAva,IAAA,wBAAAC,MAGA,WAKE,IAAMya,EAAkBrb,KAAKiW,QAAQC,UAAUvW,OAAS,EACpDK,KAAKiW,QAAQC,UAAUoF,OAAO,SAACxT,EAAGyT,GAAC,OAAKzT,EAAIyT,CAAC,EAAE,GAAKvb,KAAKiW,QAAQC,UAAUvW,OAC3E,EAEE6b,EAAexb,KAAKiW,QAAQI,cAAgB,EAC9CrW,KAAKiW,QAAQG,UAAYpW,KAAKiW,QAAQI,cACtC,EAEEoF,EAAoBzb,KAAKiW,QAAQM,mBAAqB,EACxDvW,KAAKiW,QAAQK,eAAiBtW,KAAKiW,QAAQM,mBAC3C,EAEJ,OAAAtW,EAAAA,EAAA,GACKD,KAAKiW,SAAO,IACfoF,gBAAAA,EACAG,aAAAA,EACAC,kBAAAA,GAEJ,GAEA,CAAA9a,IAAA,YAAAC,MAGA,WACE,OAAAX,EAAA,GAAYD,KAAKP,OACnB,GAEA,CAAAkB,IAAA,eAAAC,MAGA,SAAaqF,GACXjG,KAAKP,OAAMQ,EAAAA,EAAA,GAAQD,KAAKP,QAAWwG,IACnCjF,EAAAA,EAAAA,IAAK,0BAA2BhB,KAAKP,OACvC,GAEA,CAAAkB,IAAA,UAAAC,MAGA,WACMZ,KAAKiY,uBACPjY,KAAKiY,qBAAqB7R,aAC1BpG,KAAKiY,qBAAuB,MAG1BjY,KAAKoZ,mBACPsC,cAAc1b,KAAKoZ,kBACnBpZ,KAAKoZ,iBAAmB,MAG1BpZ,KAAK8a,qBACL9a,KAAKoa,cAAclL,QACnBlP,KAAKmY,gBAAgBjJ,QAErB0F,EAAkB7U,SAAW,MAC7BiB,EAAAA,EAAAA,IAAK,+BAEP,IA1hBF0F,EA0hBG,EAAA/F,IAAA,cAAAC,MA5YD,SAAmBnB,GAIjB,OAHKmV,EAAkB7U,WACrB6U,EAAkB7U,SAAW,IAAI6U,EAAkBnV,IAE9CmV,EAAkB7U,QAC3B,IAnJF4G,GAAAsD,EAAAxD,EAAAU,UAAAR,GAAAD,GAAAuD,EAAAxD,EAAAC,GAAAY,OAAAwB,eAAArC,EAAA,aAAAyC,UAAA,IAAAzC,EAAA,IAAAA,EAAAE,EAAAD,EAiXE+S,EA9DAd,CAhKC,CAjF2B,GAAA7Y,EAAjB8U,EAAiB,WACwB,MA2d/C,IAAM+G,EAAoB/G,EAAkBrO,eAGnDC,EAAAA,EAAAA,IAAM,WAEN,G,s4CCxgBO,IAAMoV,GAAe,WAW1B,SAAAA,IAAgD,IAApCnc,EAA6BC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC3C,G,4FAD4CG,CAAA,KAAA+b,GAAA9b,GAAA,oBATxB,GAAKA,GAAA,sBAG3BA,GAAA,gCAAAA,GAAA,0BAAAA,GAAA,iCAAAA,GAAA,iCAOM8b,EAAgB7b,SAClB,OAAO6b,EAAgB7b,SAGzB6b,EAAgB7b,SAAWC,KAE3BA,KAAKP,OAAMQ,GAAA,CACT4b,wBAAwB,EACxBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,6BAA6B,GAC1Bxc,GAILO,KAAKsG,iBAAmBA,EACxBtG,KAAKmP,WAAaA,EAClBnP,KAAK2U,kBAAoBA,EACzB3U,KAAK2b,kBAAoBA,CAC3B,CAEA,O,EAAAC,E,EA+VC,EAAAjb,IAAA,cAAAC,MA5VD,SAAmBnB,GAIjB,OAHKmc,EAAgB7b,WACnB6b,EAAgB7b,SAAW,IAAI6b,EAAgBnc,IAE1Cmc,EAAgB7b,QACzB,K,EARA,EAAAY,IAAA,OAAAC,MAaA,WACE,IAAIZ,KAAKkc,YAOT,IACElc,KAAKmc,2BACLnc,KAAKoc,6BACLpc,KAAKgX,6BACLhX,KAAKqc,0BACLrc,KAAKsc,qBAELtc,KAAKkc,aAAc,GAEnBlb,EAAAA,EAAAA,IAAK,+BAGP,CAAE,MAAO6D,GAEP,MAAMA,CACR,CACF,GAEA,CAAAlE,IAAA,2BAAAC,MAGA,WAAyC,IAAAY,EAAA,KAEvCJ,OAAOH,iBAAiB,eAAgB,WACtCO,EAAK+a,SACP,GAGAnc,SAASa,iBAAiB,mBAAoB,WACxCb,SAASoc,QACXxb,EAAAA,EAAAA,IAAK,yBAELA,EAAAA,EAAAA,IAAK,wBAET,GAGI,qBAAsBI,QACP,IAAIqb,iBAAiB,SAACC,GACrC,IAAIC,GAAgB,EAEpBD,EAAU/a,QAAQ,SAAAib,GACM,cAAlBA,EAAS1J,MAAwB0J,EAASC,WAAWld,OAAS,GAChEid,EAASC,WAAWlb,QAAQ,SAAAmb,GAC1B,GAAIA,EAAKC,WAAaC,KAAKC,aAAc,CACvC,IAAMnb,EAAUgb,EACZhb,EAAQI,gBACVJ,EAAQI,cAAc,kBACtBJ,EAAQI,cAAc,0BACtBJ,EAAQI,cAAc,kCAEtBya,GAAgB,EAEpB,CACF,EAEJ,GAEIA,GACFnb,EAAK0b,sBAET,GAESlb,QAAQ5B,SAASyZ,KAAM,CAC9BsD,WAAW,EACXC,SAAS,GAKf,GAEA,CAAAzc,IAAA,6BAAAC,MAGA,WAA2C,IAAA+C,EAAA,MAEzC0Z,EAAAA,EAAAA,IAAG,oBAAqB,WACtB1Z,EAAK2C,iBAAiBzF,oBACxB,IAGAwc,EAAAA,EAAAA,IAAG,2BAA4B,WAC7B1Z,EAAKwL,WAAWqB,SAClB,IAGA6M,EAAAA,EAAAA,IAAG,kBAAmB,WACpB,EAIJ,GAEA,CAAA1c,IAAA,6BAAAC,MAGA,WAA2C,IAAA0E,EAAA,KACpCtF,KAAKP,OAAOwc,8BAGjB7a,OAAOH,iBAAiB,OAAQ,WAC9B+C,WAAW,WACTsB,EAAKgY,uBACP,EAAG,IACL,IAGAD,EAAAA,EAAAA,IAAG,kBAAmB,SAACE,EAAQ1N,GAC7BvK,EAAKkY,0BAA0B,oBAAqB3N,EACtD,IAEAwN,EAAAA,EAAAA,IAAG,oBAAqB,SAACE,EAAQ1N,GAC/BvK,EAAKkY,0BAA0B,eAAgB3N,EACjD,IAEAwN,EAAAA,EAAAA,IAAG,2BAA4B,SAACE,EAAQ1N,GACtCvK,EAAKkY,0BAA0B,sBAAuB3N,EACxD,GAGF,GAEA,CAAAlP,IAAA,0BAAAC,MAGA,WAAwC,IAAA8E,EAAA,KAEhC+X,EAAkBrc,OAAesc,YAAc,CAAC,EAGtDD,EAAeE,SAAW,CACxBrX,iBAAkBtG,KAAKsG,iBACvB6I,WAAYnP,KAAKmP,WACjBwF,kBAAmB3U,KAAK2U,kBACxBgH,kBAAmB3b,KAAK2b,mBAI1B8B,EAAeG,gBAAkB5d,KAGjCyd,EAAerY,eAAiB,SAACtC,GAC/B,OAAO4C,EAAKY,iBAAiBlB,eAAetC,EAC9C,EAEA2a,EAAeI,mBAAqB,WAClCnY,EAAKyJ,WAAWqB,SAClB,EAECpP,OAAesc,WAAaD,CAG/B,GAEA,CAAA9c,IAAA,qBAAAC,MAGA,WAEOZ,KAAKP,OAAOoc,uBAIZ7b,KAAKP,OAAOqc,kBAIZ9b,KAAKP,OAAOsc,yBAIZ/b,KAAKP,OAAOuc,0BAKnB,GAEA,CAAArb,IAAA,uBAAAC,MAGA,WAIEZ,KAAKmP,WAAWqB,UAGhBxQ,KAAKsG,iBAAiBzF,sBAEtBG,EAAAA,EAAAA,IAAK,oCACP,GAEA,CAAAL,IAAA,wBAAAC,MAGA,WACE,GAAM,gBAAiBQ,OAAvB,CAEA,IAAM0c,EAAa1I,YAAY2I,iBAAiB,cAAc,GAC9D,GAAKD,EAAL,CAEA,IAAM7H,EAAU,CACd+H,iBAAkBF,EAAWG,yBAA2BH,EAAWI,2BACnEC,aAAcL,EAAWM,aAAeN,EAAWO,eACnDC,WAAY,EACZC,qBAAsB,GAIHnJ,YAAY2I,iBAAiB,SACrCpc,QAAQ,SAAAgK,GACA,gBAAfA,EAAM6S,KACRvI,EAAQqI,WAAa3S,EAAM8S,UACH,2BAAf9S,EAAM6S,OACfvI,EAAQsI,qBAAuB5S,EAAM8S,UAEzC,IAGAzd,EAAAA,EAAAA,IAAK,8BAA+BiV,EApBb,CAHe,CAwBxC,GAEA,CAAAtV,IAAA,4BAAAC,MAGA,SAAkC8d,EAAmB7O,GACnD,IAAMmL,EAAYnH,KAAKC,OAGvB9S,EAAAA,EAAAA,IAAK,iCAAkC,CAAE0d,UAAAA,EAAW7O,KAAAA,EAAMmL,UAAAA,GAC5D,GAEA,CAAAra,IAAA,sBAAAC,MAGA,WACE,OAAOZ,KAAKsG,gBACd,GAAC,CAAA3F,IAAA,gBAAAC,MAED,WACE,OAAOZ,KAAKmP,UACd,GAAC,CAAAxO,IAAA,uBAAAC,MAED,WACE,OAAOZ,KAAK2U,iBACd,GAAC,CAAAhU,IAAA,uBAAAC,MAED,WACE,OAAOZ,KAAK2b,iBACd,GAEA,CAAAhb,IAAA,YAAAC,MAGA,WACE,OAAAX,GAAA,GAAYD,KAAKP,OACnB,GAEA,CAAAkB,IAAA,eAAAC,MAGA,SAAaqF,GACXjG,KAAKP,OAAMQ,GAAAA,GAAA,GAAQD,KAAKP,QAAWwG,GACnCjG,KAAKsc,sBACLtb,EAAAA,EAAAA,IAAK,0BAA2BhB,KAAKP,OACvC,GAEA,CAAAkB,IAAA,gBAAAC,MAGA,WACE,OAAOZ,KAAKkc,WACd,GAEA,CAAAvb,IAAA,kBAAAC,MAGA,WAUE,MAAO,CACLsb,YAAalc,KAAKkc,YAClByC,iBAAkB,CAChBrY,kBAAkB,EAClB6I,WAAYnP,KAAKmP,WAAWyP,sBAC5BjK,mBAAmB,EACnBgH,mBAAmB,GAErBlc,OAAQO,KAAKP,OAEjB,GAEA,CAAAkB,IAAA,UAAAC,MAGA,WACE,GAAKZ,KAAKkc,YAIV,IACElc,KAAKsG,iBAAiBuY,UACtB7e,KAAKmP,WAAW0P,UAChB7e,KAAK2U,kBAAkBkK,UACvB7e,KAAK2b,kBAAkBkD,UAEvB7e,KAAKkc,aAAc,EACnBN,EAAgB7b,SAAW,MAE3BiB,EAAAA,EAAAA,IAAK,6BAEP,CAAE,MAAO6D,GAET,CACF,GAEA,CAAAlE,IAAA,UAAAC,MAGA,WACEZ,KAAKuc,SACP,M,2FAvVC,CA1CyB,GAAAzc,GAAf8b,GAAe,WACwB,MAoY7C,IAAMgC,GAAkBhC,GAAgBrV,eAG/CC,EAAAA,EAAAA,IAAM,WACJoX,GAAgBld,MAClB,G,uPCraA,IAAA+F,EAAAC,EAAAC,EAAA,mBAAAC,OAAAA,OAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,aAAA,yBAAAC,EAAAN,EAAAE,EAAAE,EAAAE,GAAA,IAAAC,EAAAL,GAAAA,EAAAM,qBAAAC,EAAAP,EAAAO,EAAAC,EAAAC,OAAAC,OAAAL,EAAAC,WAAA,OAAAK,GAAAH,EAAA,mBAAAV,EAAAE,EAAAE,GAAA,IAAAE,EAAAC,EAAAG,EAAAI,EAAA,EAAAC,EAAAX,GAAA,GAAAY,GAAA,EAAAC,EAAA,CAAAF,EAAA,EAAAb,EAAA,EAAAgB,EAAApB,EAAAqB,EAAAC,EAAAN,EAAAM,EAAA5G,KAAAsF,EAAA,GAAAsB,EAAA,SAAArB,EAAAC,GAAA,OAAAM,EAAAP,EAAAQ,EAAA,EAAAG,EAAAZ,EAAAmB,EAAAf,EAAAF,EAAAmB,CAAA,YAAAC,EAAApB,EAAAE,GAAA,IAAAK,EAAAP,EAAAU,EAAAR,EAAAH,EAAA,GAAAiB,GAAAF,IAAAV,GAAAL,EAAAgB,EAAA/H,OAAA+G,IAAA,KAAAK,EAAAE,EAAAS,EAAAhB,GAAAqB,EAAAH,EAAAF,EAAAM,EAAAf,EAAA,GAAAN,EAAA,GAAAI,EAAAiB,IAAAnB,KAAAQ,EAAAJ,GAAAC,EAAAD,EAAA,OAAAC,EAAA,MAAAD,EAAA,GAAAA,EAAA,GAAAR,GAAAQ,EAAA,IAAAc,KAAAhB,EAAAJ,EAAA,GAAAoB,EAAAd,EAAA,KAAAC,EAAA,EAAAU,EAAAC,EAAAhB,EAAAe,EAAAf,EAAAI,EAAA,IAAAc,EAAAC,IAAAjB,EAAAJ,EAAA,GAAAM,EAAA,GAAAJ,GAAAA,EAAAmB,KAAAf,EAAA,GAAAN,EAAAM,EAAA,GAAAJ,EAAAe,EAAAf,EAAAmB,EAAAd,EAAA,OAAAH,GAAAJ,EAAA,SAAAmB,EAAA,MAAAH,GAAA,EAAAd,CAAA,iBAAAE,EAAAW,EAAAM,GAAA,GAAAP,EAAA,QAAAQ,UAAA,oCAAAN,GAAA,IAAAD,GAAAK,EAAAL,EAAAM,GAAAd,EAAAQ,EAAAL,EAAAW,GAAAtB,EAAAQ,EAAA,EAAAT,EAAAY,KAAAM,GAAA,CAAAV,IAAAC,EAAAA,EAAA,GAAAA,EAAA,IAAAU,EAAAf,GAAA,GAAAkB,EAAAb,EAAAG,IAAAO,EAAAf,EAAAQ,EAAAO,EAAAC,EAAAR,GAAA,OAAAI,EAAA,EAAAR,EAAA,IAAAC,IAAAH,EAAA,QAAAL,EAAAO,EAAAF,GAAA,MAAAL,EAAAA,EAAAwB,KAAAjB,EAAAI,IAAA,MAAAY,UAAA,wCAAAvB,EAAAyB,KAAA,OAAAzB,EAAAW,EAAAX,EAAA9F,MAAAsG,EAAA,IAAAA,EAAA,YAAAA,IAAAR,EAAAO,EAAAmB,SAAA1B,EAAAwB,KAAAjB,GAAAC,EAAA,IAAAG,EAAAY,UAAA,oCAAAlB,EAAA,YAAAG,EAAA,GAAAD,EAAAR,CAAA,UAAAC,GAAAiB,EAAAC,EAAAf,EAAA,GAAAQ,EAAAV,EAAAuB,KAAArB,EAAAe,MAAAE,EAAA,YAAApB,GAAAO,EAAAR,EAAAS,EAAA,EAAAG,EAAAX,CAAA,SAAAe,EAAA,UAAA7G,MAAA8F,EAAAyB,KAAAR,EAAA,GAAAhB,EAAAI,EAAAE,IAAA,GAAAI,CAAA,KAAAS,EAAA,YAAAV,IAAA,UAAAiB,IAAA,UAAAC,IAAA,CAAA5B,EAAAY,OAAAiB,eAAA,IAAArB,EAAA,GAAAL,GAAAH,EAAAA,EAAA,GAAAG,QAAAW,GAAAd,EAAA,GAAAG,EAAA,yBAAAH,GAAAW,EAAAiB,EAAAnB,UAAAC,EAAAD,UAAAG,OAAAC,OAAAL,GAAA,SAAAO,EAAAhB,GAAA,OAAAa,OAAAkB,eAAAlB,OAAAkB,eAAA/B,EAAA6B,IAAA7B,EAAAgC,UAAAH,EAAAd,GAAAf,EAAAM,EAAA,sBAAAN,EAAAU,UAAAG,OAAAC,OAAAF,GAAAZ,CAAA,QAAA4B,EAAAlB,UAAAmB,EAAAd,GAAAH,EAAA,cAAAiB,GAAAd,GAAAc,EAAA,cAAAD,GAAAA,EAAAK,YAAA,oBAAAlB,GAAAc,EAAAvB,EAAA,qBAAAS,GAAAH,GAAAG,GAAAH,EAAAN,EAAA,aAAAS,GAAAH,EAAAR,EAAA,yBAAAW,GAAAH,EAAA,oDAAAsB,GAAA,kBAAAC,EAAA3B,EAAA4B,EAAApB,EAAA,cAAAD,GAAAf,EAAAE,EAAAE,EAAAH,GAAA,IAAAO,EAAAK,OAAAwB,eAAA,IAAA7B,EAAA,gBAAAR,GAAAQ,EAAA,EAAAO,GAAA,SAAAf,EAAAE,EAAAE,EAAAH,GAAA,SAAAK,EAAAJ,EAAAE,GAAAW,GAAAf,EAAAE,EAAA,SAAAF,GAAA,YAAAsC,QAAApC,EAAAE,EAAAJ,EAAA,GAAAE,EAAAM,EAAAA,EAAAR,EAAAE,EAAA,CAAA/F,MAAAiG,EAAAmC,YAAAtC,EAAAuC,cAAAvC,EAAAwC,UAAAxC,IAAAD,EAAAE,GAAAE,GAAAE,EAAA,UAAAA,EAAA,WAAAA,EAAA,cAAAS,GAAAf,EAAAE,EAAAE,EAAAH,EAAA,UAAAyC,GAAAtC,EAAAH,EAAAD,EAAAE,EAAAI,EAAAe,EAAAZ,GAAA,QAAAD,EAAAJ,EAAAiB,GAAAZ,GAAAG,EAAAJ,EAAArG,KAAA,OAAAiG,GAAA,YAAAJ,EAAAI,EAAA,CAAAI,EAAAkB,KAAAzB,EAAAW,GAAA+B,QAAAC,QAAAhC,GAAAiC,KAAA3C,EAAAI,EAAA,UAAAkD,GAAAxD,EAAAE,GAAA,QAAAD,EAAA,EAAAA,EAAAC,EAAAhH,OAAA+G,IAAA,KAAAK,EAAAJ,EAAAD,GAAAK,EAAAiC,WAAAjC,EAAAiC,aAAA,EAAAjC,EAAAkC,cAAA,YAAAlC,IAAAA,EAAAmC,UAAA,GAAA5B,OAAAwB,eAAArC,EAAAyD,GAAAnD,EAAApG,KAAAoG,EAAA,WAAAmD,GAAAxD,GAAA,IAAAO,EAAA,SAAAP,EAAAC,GAAA,aAAAwD,GAAAzD,KAAAA,EAAA,OAAAA,EAAA,IAAAD,EAAAC,EAAAE,OAAAwD,aAAA,YAAA3D,EAAA,KAAAQ,EAAAR,EAAAyB,KAAAxB,EAAAC,GAAA,wBAAAwD,GAAAlD,GAAA,OAAAA,EAAA,UAAAgB,UAAA,kEAAAtB,EAAA0D,OAAAC,QAAA5D,EAAA,CAAA6D,CAAA7D,EAAA,0BAAAyD,GAAAlD,GAAAA,EAAAA,EAAA,GAUA,IAgMM6X,GAAc,IA7LH,WACY,OAd7BrY,EAaiB,SAAAsY,IAbjB,IAAAtY,EAAAE,EAAAD,GAAA,SAAAoB,EAAAjB,GAAA,KAAAiB,aAAAjB,GAAA,UAAAoB,UAAA,qCAaiBpI,CAAA,KAAAkf,GAbjBtY,EAaiB,KAbjBC,GAcwB,GAdxBC,EAAAuD,GAAAvD,EAaiB,kBAbjBF,EAAAa,OAAAwB,eAAArC,EAAAE,EAAA,CAAA/F,MAAA8F,EAAAsC,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAzC,EAAAE,GAAAD,CAc6B,EAd7BC,EAc6B,EAAAhG,IAAA,OAAAC,MAK3B,WACMZ,KAAKkc,cAOTlc,KAAKgf,uBAGLhf,KAAKif,aAELjf,KAAKkc,aAAc,GACnBlb,EAAAA,EAAAA,IAAK,wBAGP,GAEA,CAAAL,IAAA,uBAAAC,MAGA,WAEEZ,KAAKkf,4BAGLlf,KAAKmf,mBAGLnf,KAAKof,kBAGLpf,KAAKqf,qBAELre,EAAAA,EAAAA,IAAK,2BACP,GAEA,CAAAL,IAAA,mBAAAC,MAGA,WACuBR,SAASyB,iBAAiB,iBAGlCF,QAAQ,SAAAkC,GAEnB,IAAMyb,EAAYzb,EAAMsB,aAAa,oBACrCnE,EAAAA,EAAAA,IAAK,sBAAuB,CAAE6C,MAAAA,EAAOyb,UAAAA,GACvC,EACF,GAEA,CAAA3e,IAAA,kBAAAC,MAGA,WACE,GAAI,yBAA0BQ,OAAQ,CACpC,IAAM4K,EAAa5L,SAASyB,iBAAiB,iBAE7C,GAAImK,EAAWrM,OAAS,EAAG,CACzB,IAAM4f,EAAgB,IAAI9T,qBAAqB,SAACC,GAC9CA,EAAQ/J,QAAQ,SAAAgK,GACd,GAAIA,EAAMC,eAAgB,CACxB,IAAMC,EAAMF,EAAMzI,OACZkJ,EAAMP,EAAI1G,aAAa,YAEzBiH,IACFP,EAAIO,IAAMA,EACVP,EAAI2T,gBAAgB,YACpBD,EAAcxT,UAAUF,IAExB7K,EAAAA,EAAAA,IAAK,wBAAyB,CAAE6K,IAAAA,EAAKO,IAAAA,IAEzC,CACF,EACF,GAEAJ,EAAWrK,QAAQ,SAAAkK,GAAG,OAAI0T,EAAcvd,QAAQ6J,EAAI,EAEtD,CACF,CACF,GAEA,CAAAlL,IAAA,oBAAAC,MAGA,WACE,IAAM6e,EAAerf,SAASyB,iBAAiB,oBAE3C4d,EAAa9f,OAAS,GAIxBK,KAAK0f,mBAAmBpW,KAAK,WAC3BmW,EAAa9d,QAAQ,SAAAG,IACnBd,EAAAA,EAAAA,IAAK,uBAAwB,CAAEc,QAAAA,GACjC,EACF,EAEJ,GAEA,CAAAnB,IAAA,mBAAAC,OAxHFiG,EAwHE8B,KAAAE,EAGA,SAAAsD,IAAA,OAAAxD,KAAAC,EAAA,SAAA0D,GAAA,cAAAA,EAAAzF,EAAA,OAAAyF,EAAAxE,EAAA,EAES,IAAIsB,QAAQ,SAACC,GAElBrF,WAAWqF,EAAS,IACtB,GAAE,EAAA8C,EAAA,GARJwT,EAxHF,eAAAjZ,EAAA,KAAAD,EAAA/G,UAAA,WAAA0J,QAAA,SAAAzC,EAAAI,GAAA,IAAAe,EAAAjB,EAAAb,MAAAU,EAAAD,GAAA,SAAA+C,EAAA3C,GAAAsC,GAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,OAAA5C,EAAA,UAAA4C,EAAA5C,GAAAsC,GAAArB,EAAAnB,EAAAI,EAAAyC,EAAAC,EAAA,QAAA5C,EAAA,CAAA2C,OAAA,MAiIG,WAN6B,OAAAmW,EAAA3Z,MAAC,KAADtG,UAAA,IAQ9B,CAAAiB,IAAA,4BAAAC,MAGA,WAEEgd,GAAgBld,OAGhBkf,EAAAA,GAASvC,GAAG,+BAAgC,WAE5C,GAEAuC,EAAAA,GAASvC,GAAG,6BAA8B,WAE1C,GAGAuC,EAAAA,GAASvC,GAAG,8BAA+B,SAACE,EAAQtH,GAEpD,EAGF,GAEA,CAAAtV,IAAA,aAAAC,MAGA,WAEE,IAAIif,EACJze,OAAOH,iBAAiB,SAAU,WAChC8E,aAAa8Z,GACbA,EAAgB7b,WAAW,YACzBhD,EAAAA,EAAAA,IAAK,kBAAmB,CACtBmW,QAAS/V,OAAO+V,QAChB2I,QAAS1e,OAAO0e,SAEpB,EAAG,IACL,GAGA1e,OAAOH,iBAAiB,SAAU,YAChCD,EAAAA,EAAAA,IAAK,kBAAmB,CACtB+e,MAAO3e,OAAO4e,WACdC,OAAQ7e,OAAO8e,aAEnB,EACF,GAEA,CAAAvf,IAAA,UAAAC,MAGA,WACOZ,KAAKkc,cAKV0B,GAAgBiB,WAEhB7d,EAAAA,EAAAA,IAAK,oBACL4e,EAAAA,GAASO,qBACTngB,KAAKkc,aAAc,EAGrB,IApMFvV,GAAAsD,GAAAxD,EAAAU,UAAAR,GAAAD,GAAAuD,GAAAxD,EAAAC,GAAAY,OAAAwB,eAAArC,EAAA,aAAAyC,UAAA,IAAAzC,EAAA,IAAAA,EAAAE,EAAAD,EAAAG,EAwHE8Y,CA4EC,CAvLc,IAwMjBve,OAAOgf,iBAAmBtB,IAK1BtY,EAAAA,EAAAA,IAAM,WACJsY,GAAYpe,MACd,E", "sources": ["webpack://notion-to-wordpress/./src/frontend/components/AnchorNavigation.ts", "webpack://notion-to-wordpress/./src/frontend/components/LazyLoader.ts", "webpack://notion-to-wordpress/./src/frontend/components/ProgressiveLoader.ts", "webpack://notion-to-wordpress/./src/frontend/components/ResourceOptimizer.ts", "webpack://notion-to-wordpress/./src/frontend/FrontendContent.ts", "webpack://notion-to-wordpress/./src/frontend/frontend.ts"], "sourcesContent": ["/**\r\n * 锚点导航系统 - 现代化TypeScript版本\r\n * \r\n * 从原有anchor-navigation.js完全迁移，包括：\r\n * - 平滑滚动到Notion区块锚点\r\n * - 固定头部偏移处理\r\n * - 区块高亮效果\r\n * - URL状态管理\r\n */\r\n\r\nimport { emit } from '../../shared/core/EventBus';\r\nimport { ready } from '../../shared/utils/dom';\r\n\r\nexport interface AnchorNavigationConfig {\r\n  headerSelectors: string[];\r\n  smoothScrollSupported: boolean;\r\n  highlightDuration: number;\r\n  scrollOffset: number;\r\n}\r\n\r\nexport interface ScrollTarget {\r\n  id: string;\r\n  element: HTMLElement;\r\n  rect: DOMRect;\r\n}\r\n\r\n/**\r\n * 锚点导航系统类\r\n */\r\nexport class AnchorNavigation {\r\n  private static instance: AnchorNavigation | null = null;\r\n\r\n  private config!: AnchorNavigationConfig;\r\n  private headerOffset = 0;\r\n  private supportsSmoothScroll!: boolean;\r\n  private resizeObserver: ResizeObserver | null = null;\r\n\r\n  constructor(config: Partial<AnchorNavigationConfig> = {}) {\r\n    if (AnchorNavigation.instance) {\r\n      return AnchorNavigation.instance;\r\n    }\r\n    \r\n    AnchorNavigation.instance = this;\r\n    \r\n    this.config = {\r\n      headerSelectors: [\r\n        'header[style*=\"position: fixed\"]',\r\n        '.fixed-header',\r\n        '.sticky-header',\r\n        '#masthead',\r\n        '.site-header'\r\n      ],\r\n      smoothScrollSupported: 'scrollBehavior' in document.documentElement.style,\r\n      highlightDuration: 2000,\r\n      scrollOffset: 20,\r\n      ...config\r\n    };\r\n    \r\n    this.supportsSmoothScroll = this.config.smoothScrollSupported;\r\n    this.init();\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  static getInstance(config?: Partial<AnchorNavigationConfig>): AnchorNavigation {\r\n    if (!AnchorNavigation.instance) {\r\n      AnchorNavigation.instance = new AnchorNavigation(config);\r\n    }\r\n    return AnchorNavigation.instance;\r\n  }\r\n\r\n  /**\r\n   * 初始化锚点导航系统\r\n   */\r\n  private init(): void {\r\n    this.updateHeaderOffset();\r\n    this.setupEventListeners();\r\n    this.handleInitialHash();\r\n    \r\n    console.log('🔗 [锚点导航] 已初始化');\r\n    emit('anchor:navigation:initialized');\r\n  }\r\n\r\n  /**\r\n   * 设置事件监听器\r\n   */\r\n  private setupEventListeners(): void {\r\n    // 点击事件委托\r\n    document.addEventListener('click', this.handleAnchorClick.bind(this));\r\n    \r\n    // Hash变化监听\r\n    window.addEventListener('hashchange', this.debounce(this.handleHashChange.bind(this), 100));\r\n    \r\n    // 窗口大小变化监听\r\n    window.addEventListener('resize', this.debounce(this.updateHeaderOffset.bind(this), 250));\r\n    \r\n    // 使用ResizeObserver监听头部元素变化\r\n    if ('ResizeObserver' in window) {\r\n      this.setupHeaderObserver();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置头部观察器\r\n   */\r\n  private setupHeaderObserver(): void {\r\n    this.resizeObserver = new ResizeObserver(this.debounce(() => {\r\n      this.updateHeaderOffset();\r\n    }, 100));\r\n\r\n    // 观察所有可能的头部元素\r\n    this.config.headerSelectors.forEach(selector => {\r\n      const elements = document.querySelectorAll(selector);\r\n      elements.forEach(element => {\r\n        if (element instanceof HTMLElement) {\r\n          this.resizeObserver!.observe(element);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 检测固定头部高度\r\n   */\r\n  private calculateHeaderOffset(): number {\r\n    let maxHeight = 0;\r\n    \r\n    this.config.headerSelectors.forEach(selector => {\r\n      const element = document.querySelector(selector) as HTMLElement;\r\n      if (element) {\r\n        const style = window.getComputedStyle(element);\r\n        if (style.position === 'fixed' || style.position === 'sticky') {\r\n          maxHeight = Math.max(maxHeight, element.offsetHeight);\r\n        }\r\n      }\r\n    });\r\n    \r\n    return maxHeight + this.config.scrollOffset;\r\n  }\r\n\r\n  /**\r\n   * 更新头部偏移\r\n   */\r\n  updateHeaderOffset(): void {\r\n    const newOffset = this.calculateHeaderOffset();\r\n    \r\n    if (newOffset !== this.headerOffset) {\r\n      this.headerOffset = newOffset;\r\n      document.documentElement.style.setProperty('--ntw-header-offset', `${this.headerOffset}px`);\r\n      \r\n      emit('anchor:header:offset:updated', { offset: this.headerOffset });\r\n      console.log(`🔗 [锚点导航] 头部偏移已更新: ${this.headerOffset}px`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 平滑滚动到锚点\r\n   */\r\n  scrollToAnchor(targetId: string): boolean {\r\n    if (!targetId || !targetId.startsWith('#notion-block-')) {\r\n      return false;\r\n    }\r\n    \r\n    const cleanId = targetId.replace('#', '');\r\n    const target = document.getElementById(cleanId);\r\n    \r\n    if (!target) {\r\n      console.warn(`🔗 [锚点导航] 未找到目标元素: ${targetId}`);\r\n      return false;\r\n    }\r\n\r\n    const scrollTarget: ScrollTarget = {\r\n      id: cleanId,\r\n      element: target,\r\n      rect: target.getBoundingClientRect()\r\n    };\r\n\r\n    // 执行滚动\r\n    this.performScroll(scrollTarget);\r\n    \r\n    // 高亮效果\r\n    this.highlightBlock(target);\r\n    \r\n    // 更新URL\r\n    this.updateURL(targetId);\r\n    \r\n    emit('anchor:scrolled', scrollTarget);\r\n    console.log(`🔗 [锚点导航] 滚动到: ${targetId}`);\r\n    \r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * 执行滚动操作\r\n   */\r\n  private performScroll(scrollTarget: ScrollTarget): void {\r\n    const { element } = scrollTarget;\r\n    \r\n    // 首先滚动到元素中心\r\n    const scrollOptions: ScrollIntoViewOptions = { \r\n      block: 'center',\r\n      behavior: this.supportsSmoothScroll ? 'smooth' : 'auto'\r\n    };\r\n    \r\n    element.scrollIntoView(scrollOptions);\r\n\r\n    // 调整头部偏移\r\n    setTimeout(() => {\r\n      const rect = element.getBoundingClientRect();\r\n      if (rect.top < this.headerOffset) {\r\n        const offset = rect.top - this.headerOffset;\r\n        \r\n        if (this.supportsSmoothScroll) {\r\n          window.scrollBy({ top: offset, behavior: 'smooth' });\r\n        } else {\r\n          window.scrollBy(0, offset);\r\n        }\r\n      }\r\n    }, this.supportsSmoothScroll ? 100 : 0);\r\n  }\r\n\r\n  /**\r\n   * 高亮区块\r\n   */\r\n  private highlightBlock(element: HTMLElement): void {\r\n    if (!element || !element.classList) return;\r\n    \r\n    // 移除现有高亮\r\n    element.classList.remove('notion-block-highlight');\r\n    \r\n    // 强制重绘\r\n    element.offsetWidth;\r\n    \r\n    // 添加高亮\r\n    element.classList.add('notion-block-highlight');\r\n    \r\n    // 监听动画结束事件\r\n    const removeHighlight = () => {\r\n      element.classList.remove('notion-block-highlight');\r\n      element.removeEventListener('animationend', removeHighlight);\r\n    };\r\n    \r\n    element.addEventListener('animationend', removeHighlight, { once: true });\r\n    \r\n    // 备用定时器（防止动画事件不触发）\r\n    setTimeout(() => {\r\n      element.classList.remove('notion-block-highlight');\r\n    }, this.config.highlightDuration);\r\n    \r\n    emit('anchor:block:highlighted', { element, id: element.id });\r\n  }\r\n\r\n  /**\r\n   * 更新URL\r\n   */\r\n  private updateURL(targetId: string): void {\r\n    if (window.history && window.history.replaceState) {\r\n      try {\r\n        window.history.replaceState(null, '', targetId);\r\n        emit('anchor:url:updated', { hash: targetId });\r\n      } catch (error) {\r\n        console.warn('🔗 [锚点导航] URL更新失败:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理锚点点击\r\n   */\r\n  private handleAnchorClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const link = target.closest('a[href^=\"#notion-block-\"]') as HTMLAnchorElement;\r\n    \r\n    if (link) {\r\n      event.preventDefault();\r\n      const href = link.getAttribute('href');\r\n      if (href) {\r\n        this.scrollToAnchor(href);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理hash变化\r\n   */\r\n  private handleHashChange(): void {\r\n    const hash = window.location.hash;\r\n    if (hash && hash.startsWith('#notion-block-')) {\r\n      this.scrollToAnchor(hash);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理初始hash\r\n   */\r\n  private handleInitialHash(): void {\r\n    const hash = window.location.hash;\r\n    if (hash && hash.startsWith('#notion-block-')) {\r\n      // 延迟处理，确保页面完全加载\r\n      setTimeout(() => {\r\n        this.scrollToAnchor(hash);\r\n      }, 500);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 防抖函数\r\n   */\r\n  private debounce<T extends (...args: any[]) => any>(\r\n    func: T,\r\n    wait: number\r\n  ): (...args: Parameters<T>) => void {\r\n    let timeout: NodeJS.Timeout;\r\n    \r\n    return (...args: Parameters<T>) => {\r\n      clearTimeout(timeout);\r\n      timeout = setTimeout(() => func.apply(this, args), wait);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取当前配置\r\n   */\r\n  getConfig(): AnchorNavigationConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * 更新配置\r\n   */\r\n  updateConfig(newConfig: Partial<AnchorNavigationConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n    emit('anchor:config:updated', this.config);\r\n  }\r\n\r\n  /**\r\n   * 获取头部偏移\r\n   */\r\n  getHeaderOffset(): number {\r\n    return this.headerOffset;\r\n  }\r\n\r\n  /**\r\n   * 获取所有可滚动的锚点\r\n   */\r\n  getAllAnchors(): ScrollTarget[] {\r\n    const anchors: ScrollTarget[] = [];\r\n    const elements = document.querySelectorAll('[id^=\"notion-block-\"]');\r\n    \r\n    elements.forEach(element => {\r\n      if (element instanceof HTMLElement) {\r\n        anchors.push({\r\n          id: element.id,\r\n          element,\r\n          rect: element.getBoundingClientRect()\r\n        });\r\n      }\r\n    });\r\n    \r\n    return anchors;\r\n  }\r\n\r\n  /**\r\n   * 销毁实例\r\n   */\r\n  destroy(): void {\r\n    // 移除事件监听器\r\n    document.removeEventListener('click', this.handleAnchorClick);\r\n    window.removeEventListener('hashchange', this.handleHashChange);\r\n    window.removeEventListener('resize', this.updateHeaderOffset);\r\n    \r\n    // 清理ResizeObserver\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n      this.resizeObserver = null;\r\n    }\r\n    \r\n    // 清理CSS变量\r\n    document.documentElement.style.removeProperty('--ntw-header-offset');\r\n    \r\n    AnchorNavigation.instance = null;\r\n    emit('anchor:navigation:destroyed');\r\n    console.log('🔗 [锚点导航] 已销毁');\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const anchorNavigation = AnchorNavigation.getInstance();\r\n\r\n// 自动初始化\r\nready(() => {\r\n  anchorNavigation;\r\n});\r\n\r\nexport default AnchorNavigation;\r\n", "/**\r\n * 懒加载系统 - 现代化TypeScript版本\r\n * \r\n * 从原有lazy-loading.js完全迁移，包括：\r\n * - Intersection Observer API图片懒加载\r\n * - 渐进式内容加载\r\n * - 外部特色图像处理\r\n * - 错误处理和降级支持\r\n */\r\n\r\nimport { emit } from '../../shared/core/EventBus';\r\nimport { ready } from '../../shared/utils/dom';\r\n\r\nexport interface LazyLoadConfig {\r\n  rootMargin: string;\r\n  threshold: number;\r\n  loadingClass: string;\r\n  loadedClass: string;\r\n  errorClass: string;\r\n  observedClass: string;\r\n  retryAttempts: number;\r\n  retryDelay: number;\r\n}\r\n\r\nexport interface LazyLoadStats {\r\n  totalImages: number;\r\n  loadedImages: number;\r\n  errorImages: number;\r\n  observerSupported: boolean;\r\n  retryAttempts: number;\r\n}\r\n\r\nexport interface LazyImageElement extends HTMLImageElement {\r\n  _lazyRetryCount?: number;\r\n  _lazyOriginalSrc?: string;\r\n}\r\n\r\n/**\r\n * 懒加载系统类\r\n */\r\nexport class LazyLoader {\r\n  private static instance: LazyLoader | null = null;\r\n\r\n  private config!: LazyLoadConfig;\r\n  private observer: IntersectionObserver | null = null;\r\n  private supportsIntersectionObserver!: boolean;\r\n  private loadedImages = new Set<string>();\r\n  private errorImages = new Set<string>();\r\n  private retryQueue = new Map<HTMLImageElement, number>();\r\n\r\n  constructor(config: Partial<LazyLoadConfig> = {}) {\r\n    if (LazyLoader.instance) {\r\n      return LazyLoader.instance;\r\n    }\r\n    \r\n    LazyLoader.instance = this;\r\n    \r\n    this.config = {\r\n      rootMargin: '50px 0px',\r\n      threshold: 0.1,\r\n      loadingClass: 'notion-lazy-loading',\r\n      loadedClass: 'notion-lazy-loaded',\r\n      errorClass: 'notion-lazy-error',\r\n      observedClass: 'notion-lazy-observed',\r\n      retryAttempts: 3,\r\n      retryDelay: 1000,\r\n      ...config\r\n    };\r\n    \r\n    this.supportsIntersectionObserver = 'IntersectionObserver' in window;\r\n    this.init();\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  static getInstance(config?: Partial<LazyLoadConfig>): LazyLoader {\r\n    if (!LazyLoader.instance) {\r\n      LazyLoader.instance = new LazyLoader(config);\r\n    }\r\n    return LazyLoader.instance;\r\n  }\r\n\r\n  /**\r\n   * 初始化懒加载系统\r\n   */\r\n  private init(): void {\r\n    if (this.supportsIntersectionObserver) {\r\n      this.createObserver();\r\n      this.observeImages();\r\n    } else {\r\n      this.fallbackLoad();\r\n    }\r\n    \r\n    console.log(`🖼️ [懒加载] 已初始化 (${this.supportsIntersectionObserver ? 'Observer模式' : '降级模式'})`);\r\n    emit('lazy:loader:initialized', { observerSupported: this.supportsIntersectionObserver });\r\n  }\r\n\r\n  /**\r\n   * 创建Intersection Observer\r\n   */\r\n  private createObserver(): void {\r\n    this.observer = new IntersectionObserver((entries) => {\r\n      entries.forEach(entry => {\r\n        if (entry.isIntersecting) {\r\n          const img = entry.target as LazyImageElement;\r\n          this.loadImage(img);\r\n          this.observer!.unobserve(img);\r\n        }\r\n      });\r\n    }, {\r\n      rootMargin: this.config.rootMargin,\r\n      threshold: this.config.threshold\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 观察所有懒加载图片\r\n   */\r\n  private observeImages(): void {\r\n    const lazyImages = document.querySelectorAll('img[data-src]:not(.notion-lazy-observed)');\r\n    \r\n    lazyImages.forEach(img => {\r\n      if (img instanceof HTMLImageElement) {\r\n        img.classList.add(this.config.observedClass);\r\n        this.observer!.observe(img);\r\n      }\r\n    });\r\n    \r\n    if (lazyImages.length > 0) {\r\n      console.log(`🖼️ [懒加载] 观察图片数量: ${lazyImages.length}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载图片\r\n   */\r\n  private async loadImage(img: LazyImageElement): Promise<void> {\r\n    const src = img.dataset.src;\r\n    if (!src) return;\r\n\r\n    // 添加加载状态\r\n    img.classList.add(this.config.loadingClass);\r\n    img._lazyOriginalSrc = src;\r\n\r\n    try {\r\n      await this.preloadImage(src);\r\n      \r\n      // 加载成功\r\n      img.src = src;\r\n      img.classList.remove(this.config.loadingClass);\r\n      img.classList.add(this.config.loadedClass);\r\n      \r\n      this.loadedImages.add(src);\r\n      \r\n      // 清理数据属性\r\n      delete img.dataset.src;\r\n      \r\n      // 触发自定义事件\r\n      img.dispatchEvent(new CustomEvent('lazyLoaded', {\r\n        detail: { src, element: img }\r\n      }));\r\n      \r\n      emit('lazy:image:loaded', { src, element: img });\r\n      \r\n    } catch (error) {\r\n      await this.handleImageError(img, error as Error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 预加载图片\r\n   */\r\n  private preloadImage(src: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      const imageLoader = new Image();\r\n      \r\n      imageLoader.onload = () => resolve();\r\n      imageLoader.onerror = () => reject(new Error(`图片加载失败: ${src}`));\r\n      \r\n      imageLoader.src = src;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 处理图片加载错误\r\n   */\r\n  private async handleImageError(img: LazyImageElement, error: Error): Promise<void> {\r\n    const src = img._lazyOriginalSrc || img.dataset.src || '';\r\n    const retryCount = img._lazyRetryCount || 0;\r\n    \r\n    if (retryCount < this.config.retryAttempts) {\r\n      // 重试加载\r\n      img._lazyRetryCount = retryCount + 1;\r\n      \r\n      console.warn(`🖼️ [懒加载] 重试加载图片 (${retryCount + 1}/${this.config.retryAttempts}): ${src}`);\r\n      \r\n      setTimeout(() => {\r\n        this.loadImage(img);\r\n      }, this.config.retryDelay * (retryCount + 1));\r\n      \r\n      return;\r\n    }\r\n    \r\n    // 重试次数用完，显示错误状态\r\n    img.classList.remove(this.config.loadingClass);\r\n    img.classList.add(this.config.errorClass);\r\n    \r\n    this.errorImages.add(src);\r\n    \r\n    // 设置错误占位符\r\n    this.setErrorPlaceholder(img);\r\n    \r\n    // 触发自定义事件\r\n    img.dispatchEvent(new CustomEvent('lazyError', {\r\n      detail: { src, error, element: img, retryCount }\r\n    }));\r\n    \r\n    emit('lazy:image:error', { src, error, element: img, retryCount });\r\n    \r\n    console.error(`🖼️ [懒加载] 图片加载失败: ${src}`, error);\r\n  }\r\n\r\n  /**\r\n   * 设置错误占位符\r\n   */\r\n  private setErrorPlaceholder(img: HTMLImageElement): void {\r\n    const placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';\r\n    img.src = placeholder;\r\n  }\r\n\r\n  /**\r\n   * 降级处理（不支持Intersection Observer时）\r\n   */\r\n  private fallbackLoad(): void {\r\n    const lazyImages = document.querySelectorAll('img[data-src]');\r\n    \r\n    lazyImages.forEach(img => {\r\n      if (img instanceof HTMLImageElement) {\r\n        this.loadImage(img);\r\n      }\r\n    });\r\n    \r\n    console.log(`🖼️ [懒加载] 降级模式加载图片数量: ${lazyImages.length}`);\r\n  }\r\n\r\n  /**\r\n   * 刷新懒加载图片\r\n   */\r\n  refresh(): void {\r\n    if (!this.supportsIntersectionObserver) return;\r\n    \r\n    this.observeImages();\r\n    emit('lazy:loader:refreshed');\r\n  }\r\n\r\n  /**\r\n   * 预加载指定图片\r\n   */\r\n  async preloadImages(urls: string[]): Promise<void> {\r\n    if (!Array.isArray(urls)) return;\r\n\r\n    const promises = urls.map(async (url) => {\r\n      try {\r\n        await this.preloadImage(url);\r\n        console.log(`🖼️ [预加载] ${url} 完成`);\r\n      } catch (error) {\r\n        console.warn(`🖼️ [预加载] ${url} 失败:`, error);\r\n      }\r\n    });\r\n\r\n    await Promise.allSettled(promises);\r\n    emit('lazy:preload:completed', { urls, count: urls.length });\r\n  }\r\n\r\n  /**\r\n   * 手动触发图片加载\r\n   */\r\n  loadImageManually(img: HTMLImageElement): void {\r\n    if (img.dataset.src) {\r\n      this.loadImage(img);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取懒加载统计信息\r\n   */\r\n  getStats(): LazyLoadStats {\r\n    const totalImages = document.querySelectorAll('img[data-src]').length;\r\n    const loadedImages = document.querySelectorAll(`.${this.config.loadedClass}`).length;\r\n    const errorImages = document.querySelectorAll(`.${this.config.errorClass}`).length;\r\n    \r\n    return {\r\n      totalImages,\r\n      loadedImages,\r\n      errorImages,\r\n      observerSupported: this.supportsIntersectionObserver,\r\n      retryAttempts: this.retryQueue.size\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取配置\r\n   */\r\n  getConfig(): LazyLoadConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * 更新配置\r\n   */\r\n  updateConfig(newConfig: Partial<LazyLoadConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n    \r\n    // 如果Observer相关配置改变，重新创建Observer\r\n    if (this.observer && (newConfig.rootMargin || newConfig.threshold)) {\r\n      this.observer.disconnect();\r\n      this.createObserver();\r\n      this.observeImages();\r\n    }\r\n    \r\n    emit('lazy:config:updated', this.config);\r\n  }\r\n\r\n  /**\r\n   * 获取Observer实例\r\n   */\r\n  getObserver(): IntersectionObserver | null {\r\n    return this.observer;\r\n  }\r\n\r\n  /**\r\n   * 检查是否支持Intersection Observer\r\n   */\r\n  isObserverSupported(): boolean {\r\n    return this.supportsIntersectionObserver;\r\n  }\r\n\r\n  /**\r\n   * 销毁懒加载系统\r\n   */\r\n  destroy(): void {\r\n    if (this.observer) {\r\n      this.observer.disconnect();\r\n      this.observer = null;\r\n    }\r\n    \r\n    this.loadedImages.clear();\r\n    this.errorImages.clear();\r\n    this.retryQueue.clear();\r\n    \r\n    LazyLoader.instance = null;\r\n    emit('lazy:loader:destroyed');\r\n    console.log('🖼️ [懒加载] 已销毁');\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const lazyLoader = LazyLoader.getInstance();\r\n\r\n// 自动初始化\r\nready(() => {\r\n  lazyLoader;\r\n});\r\n\r\nexport default LazyLoader;\r\n", "/**\r\n * 渐进式加载器 - 现代化TypeScript版本\r\n * \r\n * 从原有lazy-loading.js的渐进式加载功能完全迁移，包括：\r\n * - 数据库视图的渐进式加载\r\n * - 加载状态管理\r\n * - 内容渲染和集成\r\n */\r\n\r\nimport { emit } from '../../shared/core/EventBus';\r\nimport { post } from '../../shared/utils/ajax';\r\nimport { lazyLoader } from './LazyLoader';\r\n\r\nexport interface ProgressiveLoadConfig {\r\n  loadingDelay: number;\r\n  retryAttempts: number;\r\n  retryDelay: number;\r\n  batchSize: number;\r\n}\r\n\r\nexport interface DatabaseRecord {\r\n  id: string;\r\n  properties: Record<string, any>;\r\n  created_time: string;\r\n  last_edited_time: string;\r\n}\r\n\r\nexport interface ProgressiveLoadData {\r\n  records: DatabaseRecord[];\r\n  hasMore: boolean;\r\n  nextCursor?: string;\r\n}\r\n\r\nexport interface LoadMoreOptions {\r\n  container: HTMLElement;\r\n  button: HTMLButtonElement;\r\n  endpoint?: string;\r\n  params?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * 渐进式加载器类\r\n */\r\nexport class ProgressiveLoader {\r\n  private static instance: ProgressiveLoader | null = null;\r\n\r\n  private config!: ProgressiveLoadConfig;\r\n  private loadingStates = new Map<string, boolean>();\r\n  private loadedData = new Map<string, ProgressiveLoadData>();\r\n\r\n  constructor(config: Partial<ProgressiveLoadConfig> = {}) {\r\n    if (ProgressiveLoader.instance) {\r\n      return ProgressiveLoader.instance;\r\n    }\r\n    \r\n    ProgressiveLoader.instance = this;\r\n    \r\n    this.config = {\r\n      loadingDelay: 500,\r\n      retryAttempts: 3,\r\n      retryDelay: 1000,\r\n      batchSize: 10,\r\n      ...config\r\n    };\r\n    \r\n    this.init();\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  static getInstance(config?: Partial<ProgressiveLoadConfig>): ProgressiveLoader {\r\n    if (!ProgressiveLoader.instance) {\r\n      ProgressiveLoader.instance = new ProgressiveLoader(config);\r\n    }\r\n    return ProgressiveLoader.instance;\r\n  }\r\n\r\n  /**\r\n   * 初始化渐进式加载器\r\n   */\r\n  private init(): void {\r\n    this.setupEventListeners();\r\n    console.log('📄 [渐进式加载] 已初始化');\r\n    emit('progressive:loader:initialized');\r\n  }\r\n\r\n  /**\r\n   * 设置事件监听器\r\n   */\r\n  private setupEventListeners(): void {\r\n    // 使用事件委托处理加载更多按钮\r\n    document.addEventListener('click', this.handleLoadMoreClick.bind(this));\r\n  }\r\n\r\n  /**\r\n   * 处理加载更多按钮点击\r\n   */\r\n  private handleLoadMoreClick(event: Event): void {\r\n    const target = event.target as HTMLElement;\r\n    const button = target.closest('.notion-load-more-button') as HTMLButtonElement;\r\n    \r\n    if (button) {\r\n      event.preventDefault();\r\n      this.loadMore(button);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 加载更多内容\r\n   */\r\n  async loadMore(button: HTMLButtonElement): Promise<void> {\r\n    const container = button.closest('.notion-progressive-loading') as HTMLElement;\r\n    if (!container) {\r\n      console.error('📄 [渐进式加载] 未找到容器元素');\r\n      return;\r\n    }\r\n\r\n    const containerId = container.id || this.generateContainerId();\r\n    \r\n    // 防止重复加载\r\n    if (this.loadingStates.get(containerId)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      this.setLoadingState(button, true);\r\n      this.loadingStates.set(containerId, true);\r\n\r\n      const data = await this.fetchMoreData(container);\r\n      \r\n      if (data && data.records.length > 0) {\r\n        await this.renderRecords(container, data.records);\r\n        \r\n        // 如果没有更多数据，隐藏按钮\r\n        if (!data.hasMore) {\r\n          this.hideLoadMoreButton(button);\r\n        }\r\n        \r\n        // 刷新懒加载\r\n        lazyLoader.refresh();\r\n        \r\n        emit('progressive:load:success', { \r\n          containerId, \r\n          recordCount: data.records.length,\r\n          hasMore: data.hasMore \r\n        });\r\n        \r\n        console.log(`📄 [渐进式加载] 加载完成，记录数: ${data.records.length}`);\r\n      } else {\r\n        this.hideLoadMoreButton(button);\r\n        console.log('📄 [渐进式加载] 没有更多数据');\r\n      }\r\n      \r\n    } catch (error) {\r\n      this.handleLoadError(button, error as Error);\r\n      emit('progressive:load:error', { containerId, error });\r\n    } finally {\r\n      this.setLoadingState(button, false);\r\n      this.loadingStates.set(containerId, false);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取更多数据\r\n   */\r\n  private async fetchMoreData(container: HTMLElement): Promise<ProgressiveLoadData | null> {\r\n    const recordsData = container.dataset.records;\r\n    \r\n    if (recordsData) {\r\n      // 从数据属性中解析数据（静态数据）\r\n      try {\r\n        const data = JSON.parse(atob(recordsData));\r\n        \r\n        // 模拟API延迟\r\n        await new Promise(resolve => setTimeout(resolve, this.config.loadingDelay));\r\n        \r\n        return data;\r\n      } catch (error) {\r\n        throw new Error('数据解析失败');\r\n      }\r\n    } else {\r\n      // 从API获取数据（动态数据）\r\n      const endpoint = container.dataset.endpoint;\r\n      if (!endpoint) {\r\n        throw new Error('未配置数据端点');\r\n      }\r\n      \r\n      const params = this.getLoadParams(container);\r\n      const response = await post(endpoint, params);\r\n      \r\n      if (response.data.success) {\r\n        return response.data.data;\r\n      } else {\r\n        throw new Error(response.data.message || '数据获取失败');\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取加载参数\r\n   */\r\n  private getLoadParams(container: HTMLElement): Record<string, any> {\r\n    const params: Record<string, any> = {\r\n      batch_size: this.config.batchSize\r\n    };\r\n    \r\n    // 从数据属性中获取参数\r\n    Object.keys(container.dataset).forEach(key => {\r\n      if (key.startsWith('param')) {\r\n        const paramName = key.replace('param', '').toLowerCase();\r\n        params[paramName] = container.dataset[key];\r\n      }\r\n    });\r\n    \r\n    return params;\r\n  }\r\n\r\n  /**\r\n   * 渲染记录\r\n   */\r\n  private async renderRecords(container: HTMLElement, records: DatabaseRecord[]): Promise<void> {\r\n    const contentContainer = container.querySelector('.notion-progressive-content') as HTMLElement;\r\n    if (!contentContainer) {\r\n      throw new Error('未找到内容容器');\r\n    }\r\n\r\n    const html = records.map(record => this.renderRecord(record)).join('');\r\n    \r\n    // 使用淡入动画添加内容\r\n    const tempDiv = document.createElement('div');\r\n    tempDiv.innerHTML = html;\r\n    tempDiv.style.opacity = '0';\r\n    tempDiv.style.transition = 'opacity 0.3s ease-in-out';\r\n    \r\n    contentContainer.appendChild(tempDiv);\r\n    \r\n    // 触发淡入动画\r\n    setTimeout(() => {\r\n      tempDiv.style.opacity = '1';\r\n    }, 10);\r\n    \r\n    // 动画完成后移除包装div\r\n    setTimeout(() => {\r\n      while (tempDiv.firstChild) {\r\n        contentContainer.appendChild(tempDiv.firstChild);\r\n      }\r\n      tempDiv.remove();\r\n    }, 300);\r\n  }\r\n\r\n  /**\r\n   * 渲染单个记录\r\n   */\r\n  private renderRecord(record: DatabaseRecord): string {\r\n    const title = this.extractTitle(record.properties);\r\n    const id = record.id.substring(0, 8);\r\n    \r\n    return `\r\n      <div class=\"notion-database-record\" data-record-id=\"${record.id}\">\r\n        <div class=\"notion-record-title\">${this.escapeHtml(title)}</div>\r\n        <div class=\"notion-record-properties\">\r\n          <div class=\"notion-record-property\">\r\n            <span class=\"notion-property-name\">ID:</span>\r\n            <span class=\"notion-property-value\">${id}...</span>\r\n          </div>\r\n          <div class=\"notion-record-property\">\r\n            <span class=\"notion-property-name\">创建时间:</span>\r\n            <span class=\"notion-property-value\">${this.formatDate(record.created_time)}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    `;\r\n  }\r\n\r\n  /**\r\n   * 提取标题\r\n   */\r\n  private extractTitle(properties: Record<string, any>): string {\r\n    for (const property of Object.values(properties)) {\r\n      if (property.type === 'title' && property.title && property.title.length > 0) {\r\n        return property.title[0].plain_text || '无标题';\r\n      }\r\n    }\r\n    return '无标题';\r\n  }\r\n\r\n  /**\r\n   * 设置加载状态\r\n   */\r\n  private setLoadingState(button: HTMLButtonElement, loading: boolean): void {\r\n    const loadingText = button.querySelector('.notion-loading-text') as HTMLElement;\r\n    const loadingSpinner = button.querySelector('.notion-loading-spinner') as HTMLElement;\r\n    const buttonText = button.querySelector('.notion-button-text') as HTMLElement;\r\n    \r\n    if (loading) {\r\n      button.disabled = true;\r\n      if (buttonText) buttonText.style.display = 'none';\r\n      if (loadingText) loadingText.style.display = 'inline';\r\n      if (loadingSpinner) loadingSpinner.style.display = 'inline';\r\n    } else {\r\n      button.disabled = false;\r\n      if (buttonText) buttonText.style.display = 'inline';\r\n      if (loadingText) loadingText.style.display = 'none';\r\n      if (loadingSpinner) loadingSpinner.style.display = 'none';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 处理加载错误\r\n   */\r\n  private handleLoadError(button: HTMLButtonElement, error: Error): void {\r\n    const loadingText = button.querySelector('.notion-loading-text') as HTMLElement;\r\n    \r\n    if (loadingText) {\r\n      loadingText.textContent = '加载失败，请重试';\r\n      loadingText.style.display = 'inline';\r\n    }\r\n    \r\n    console.error('📄 [渐进式加载] 加载失败:', error);\r\n    \r\n    // 3秒后恢复按钮状态\r\n    setTimeout(() => {\r\n      if (loadingText) {\r\n        loadingText.textContent = '加载中...';\r\n      }\r\n      this.setLoadingState(button, false);\r\n    }, 3000);\r\n  }\r\n\r\n  /**\r\n   * 隐藏加载更多按钮\r\n   */\r\n  private hideLoadMoreButton(button: HTMLButtonElement): void {\r\n    const buttonContainer = button.parentElement;\r\n    if (buttonContainer) {\r\n      buttonContainer.style.transition = 'opacity 0.3s ease-out';\r\n      buttonContainer.style.opacity = '0';\r\n      \r\n      setTimeout(() => {\r\n        buttonContainer.style.display = 'none';\r\n      }, 300);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 生成容器ID\r\n   */\r\n  private generateContainerId(): string {\r\n    return `progressive-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;\r\n  }\r\n\r\n  /**\r\n   * 转义HTML\r\n   */\r\n  private escapeHtml(text: string): string {\r\n    const div = document.createElement('div');\r\n    div.textContent = text;\r\n    return div.innerHTML;\r\n  }\r\n\r\n  /**\r\n   * 格式化日期\r\n   */\r\n  private formatDate(dateString: string): string {\r\n    try {\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit'\r\n      });\r\n    } catch {\r\n      return dateString;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取配置\r\n   */\r\n  getConfig(): ProgressiveLoadConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * 更新配置\r\n   */\r\n  updateConfig(newConfig: Partial<ProgressiveLoadConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n    emit('progressive:config:updated', this.config);\r\n  }\r\n\r\n  /**\r\n   * 获取加载状态\r\n   */\r\n  getLoadingStates(): Map<string, boolean> {\r\n    return new Map(this.loadingStates);\r\n  }\r\n\r\n  /**\r\n   * 清除加载状态\r\n   */\r\n  clearLoadingState(containerId: string): void {\r\n    this.loadingStates.delete(containerId);\r\n    this.loadedData.delete(containerId);\r\n  }\r\n\r\n  /**\r\n   * 销毁渐进式加载器\r\n   */\r\n  destroy(): void {\r\n    document.removeEventListener('click', this.handleLoadMoreClick);\r\n    \r\n    this.loadingStates.clear();\r\n    this.loadedData.clear();\r\n    \r\n    ProgressiveLoader.instance = null;\r\n    emit('progressive:loader:destroyed');\r\n    console.log('📄 [渐进式加载] 已销毁');\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const progressiveLoader = ProgressiveLoader.getInstance();\r\n\r\n// 自动初始化\r\nimport { ready } from '../../shared/utils/dom';\r\nready(() => {\r\n  progressiveLoader;\r\n});\r\n\r\nexport default ProgressiveLoader;\r\n", "/**\r\n * 资源优化器 - 现代化TypeScript版本\r\n * \r\n * 从原有resource-optimizer.js完全迁移，包括：\r\n * - CDN集成和回退\r\n * - 预测性加载\r\n * - 智能缓存策略\r\n * - 性能监控\r\n */\r\n\r\nimport { emit } from '../../shared/core/EventBus';\r\nimport { ready } from '../../shared/utils/dom';\r\n\r\nexport interface OptimizerConfig {\r\n  cdn: {\r\n    enabled: boolean;\r\n    baseUrl: string;\r\n    fallbackEnabled: boolean;\r\n    timeout: number;\r\n  };\r\n  lazyLoading: {\r\n    enhanced: boolean;\r\n    preloadThreshold: number;\r\n    retryAttempts: number;\r\n    retryDelay: number;\r\n  };\r\n  performance: {\r\n    enabled: boolean;\r\n    reportInterval: number;\r\n    metricsEndpoint: string;\r\n  };\r\n  predictiveLoading: {\r\n    enabled: boolean;\r\n    hoverDelay: number;\r\n    scrollThreshold: number;\r\n    maxPredictions: number;\r\n    confidenceThreshold: number;\r\n  };\r\n  smartCache: {\r\n    enabled: boolean;\r\n    maxCacheSize: number;\r\n    ttl: number;\r\n    compressionEnabled: boolean;\r\n    versionCheck: boolean;\r\n  };\r\n}\r\n\r\nexport interface PerformanceMetrics {\r\n  loadTimes: number[];\r\n  errors: string[];\r\n  cacheHits: number;\r\n  totalRequests: number;\r\n  predictiveHits: number;\r\n  predictiveAttempts: number;\r\n  cacheSize: number;\r\n}\r\n\r\nexport interface UserBehavior {\r\n  scrollSpeed: number;\r\n  hoverTargets: string[];\r\n  clickPatterns: string[];\r\n  lastActivity: number;\r\n}\r\n\r\n/**\r\n * 资源优化器类\r\n */\r\nexport class ResourceOptimizer {\r\n  private static instance: ResourceOptimizer | null = null;\r\n\r\n  private config!: OptimizerConfig;\r\n  private metrics!: PerformanceMetrics;\r\n  private userBehavior!: UserBehavior;\r\n  private resourceCache = new Map<string, any>();\r\n  private predictiveQueue = new Set<string>();\r\n  private intersectionObserver: IntersectionObserver | null = null;\r\n  private performanceTimer: NodeJS.Timeout | null = null;\r\n\r\n  constructor(config: Partial<OptimizerConfig> = {}) {\r\n    if (ResourceOptimizer.instance) {\r\n      return ResourceOptimizer.instance;\r\n    }\r\n    \r\n    ResourceOptimizer.instance = this;\r\n    \r\n    this.config = {\r\n      cdn: {\r\n        enabled: false,\r\n        baseUrl: '',\r\n        fallbackEnabled: true,\r\n        timeout: 5000\r\n      },\r\n      lazyLoading: {\r\n        enhanced: true,\r\n        preloadThreshold: 2,\r\n        retryAttempts: 3,\r\n        retryDelay: 1000\r\n      },\r\n      performance: {\r\n        enabled: true,\r\n        reportInterval: 30000,\r\n        metricsEndpoint: ''\r\n      },\r\n      predictiveLoading: {\r\n        enabled: true,\r\n        hoverDelay: 100,\r\n        scrollThreshold: 0.8,\r\n        maxPredictions: 5,\r\n        confidenceThreshold: 0.7\r\n      },\r\n      smartCache: {\r\n        enabled: true,\r\n        maxCacheSize: 50 * 1024 * 1024, // 50MB\r\n        ttl: 24 * 60 * 60 * 1000, // 24小时\r\n        compressionEnabled: true,\r\n        versionCheck: true\r\n      },\r\n      ...config\r\n    };\r\n    \r\n    this.metrics = {\r\n      loadTimes: [],\r\n      errors: [],\r\n      cacheHits: 0,\r\n      totalRequests: 0,\r\n      predictiveHits: 0,\r\n      predictiveAttempts: 0,\r\n      cacheSize: 0\r\n    };\r\n    \r\n    this.userBehavior = {\r\n      scrollSpeed: 0,\r\n      hoverTargets: [],\r\n      clickPatterns: [],\r\n      lastActivity: Date.now()\r\n    };\r\n    \r\n    this.init();\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  static getInstance(config?: Partial<OptimizerConfig>): ResourceOptimizer {\r\n    if (!ResourceOptimizer.instance) {\r\n      ResourceOptimizer.instance = new ResourceOptimizer(config);\r\n    }\r\n    return ResourceOptimizer.instance;\r\n  }\r\n\r\n  /**\r\n   * 初始化资源优化器\r\n   */\r\n  private init(): void {\r\n    this.setupUserBehaviorTracking();\r\n    this.setupPredictiveLoading();\r\n    this.setupPerformanceMonitoring();\r\n    this.setupSmartCache();\r\n    \r\n    console.log('⚡ [资源优化器] 已初始化');\r\n    emit('resource:optimizer:initialized');\r\n  }\r\n\r\n  /**\r\n   * 设置用户行为追踪\r\n   */\r\n  private setupUserBehaviorTracking(): void {\r\n    let lastScrollY = window.scrollY;\r\n    let scrollStartTime = Date.now();\r\n    \r\n    // 滚动行为追踪\r\n    const handleScroll = this.throttle(() => {\r\n      const currentScrollY = window.scrollY;\r\n      const currentTime = Date.now();\r\n      const distance = Math.abs(currentScrollY - lastScrollY);\r\n      const time = currentTime - scrollStartTime;\r\n      \r\n      if (time > 0) {\r\n        this.userBehavior.scrollSpeed = distance / time;\r\n      }\r\n      \r\n      lastScrollY = currentScrollY;\r\n      scrollStartTime = currentTime;\r\n      this.userBehavior.lastActivity = currentTime;\r\n    }, 100);\r\n    \r\n    // 鼠标悬停追踪\r\n    const handleMouseOver = (e: MouseEvent) => {\r\n      const target = e.target as HTMLElement;\r\n      if (target.tagName === 'A') {\r\n        const href = (target as HTMLAnchorElement).href;\r\n        if (href && !this.userBehavior.hoverTargets.includes(href)) {\r\n          this.userBehavior.hoverTargets.push(href);\r\n          this.predictResource(href);\r\n        }\r\n      }\r\n      this.userBehavior.lastActivity = Date.now();\r\n    };\r\n    \r\n    // 点击模式追踪\r\n    const handleClick = (e: MouseEvent) => {\r\n      const target = e.target as HTMLElement;\r\n      if (target.tagName === 'A') {\r\n        const href = (target as HTMLAnchorElement).href;\r\n        if (href) {\r\n          this.userBehavior.clickPatterns.push(href);\r\n          // 保持最近20个点击记录\r\n          if (this.userBehavior.clickPatterns.length > 20) {\r\n            this.userBehavior.clickPatterns.shift();\r\n          }\r\n        }\r\n      }\r\n      this.userBehavior.lastActivity = Date.now();\r\n    };\r\n    \r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    document.addEventListener('mouseover', handleMouseOver, { passive: true });\r\n    document.addEventListener('click', handleClick, { passive: true });\r\n  }\r\n\r\n  /**\r\n   * 设置预测性加载\r\n   */\r\n  private setupPredictiveLoading(): void {\r\n    if (!this.config.predictiveLoading.enabled) return;\r\n    \r\n    // 创建Intersection Observer用于预测性加载\r\n    this.intersectionObserver = new IntersectionObserver((entries) => {\r\n      entries.forEach(entry => {\r\n        if (entry.isIntersecting) {\r\n          const element = entry.target as HTMLElement;\r\n          this.analyzePredictiveOpportunity(element);\r\n        }\r\n      });\r\n    }, {\r\n      rootMargin: '100px',\r\n      threshold: 0.1\r\n    });\r\n    \r\n    // 观察所有链接\r\n    document.querySelectorAll('a[href]').forEach(link => {\r\n      this.intersectionObserver!.observe(link);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 分析预测性加载机会\r\n   */\r\n  private analyzePredictiveOpportunity(element: HTMLElement): void {\r\n    if (element.tagName !== 'A') return;\r\n    \r\n    const href = (element as HTMLAnchorElement).href;\r\n    if (!href || this.predictiveQueue.has(href)) return;\r\n    \r\n    // 计算预测置信度\r\n    const confidence = this.calculatePredictionConfidence(href);\r\n    \r\n    if (confidence >= this.config.predictiveLoading.confidenceThreshold) {\r\n      this.predictResource(href);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 计算预测置信度\r\n   */\r\n  private calculatePredictionConfidence(href: string): number {\r\n    let confidence = 0;\r\n    \r\n    // 基于悬停历史\r\n    if (this.userBehavior.hoverTargets.includes(href)) {\r\n      confidence += 0.3;\r\n    }\r\n    \r\n    // 基于点击模式\r\n    const clickCount = this.userBehavior.clickPatterns.filter(pattern => pattern === href).length;\r\n    confidence += Math.min(clickCount * 0.2, 0.4);\r\n    \r\n    // 基于滚动速度（慢速滚动表示用户在仔细阅读）\r\n    if (this.userBehavior.scrollSpeed < 1) {\r\n      confidence += 0.2;\r\n    }\r\n    \r\n    // 基于活跃度\r\n    const timeSinceLastActivity = Date.now() - this.userBehavior.lastActivity;\r\n    if (timeSinceLastActivity < 5000) { // 5秒内有活动\r\n      confidence += 0.1;\r\n    }\r\n    \r\n    return Math.min(confidence, 1);\r\n  }\r\n\r\n  /**\r\n   * 预测性资源加载\r\n   */\r\n  private predictResource(href: string): void {\r\n    if (this.predictiveQueue.size >= this.config.predictiveLoading.maxPredictions) {\r\n      return;\r\n    }\r\n    \r\n    this.predictiveQueue.add(href);\r\n    this.metrics.predictiveAttempts++;\r\n    \r\n    // 延迟预加载以避免影响当前页面性能\r\n    setTimeout(() => {\r\n      this.preloadResource(href);\r\n    }, this.config.predictiveLoading.hoverDelay);\r\n  }\r\n\r\n  /**\r\n   * 预加载资源\r\n   */\r\n  private async preloadResource(href: string): Promise<void> {\r\n    try {\r\n      const link = document.createElement('link');\r\n      link.rel = 'prefetch';\r\n      link.href = href;\r\n      document.head.appendChild(link);\r\n      \r\n      this.metrics.predictiveHits++;\r\n      emit('resource:predicted', { href, success: true });\r\n      \r\n    } catch (error) {\r\n      console.warn('⚡ [预测性加载] 失败:', href, error);\r\n      emit('resource:predicted', { href, success: false, error });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置性能监控\r\n   */\r\n  private setupPerformanceMonitoring(): void {\r\n    if (!this.config.performance.enabled) return;\r\n    \r\n    // 监控资源加载时间\r\n    if ('PerformanceObserver' in window) {\r\n      const observer = new PerformanceObserver((list) => {\r\n        list.getEntries().forEach(entry => {\r\n          if (entry.entryType === 'resource') {\r\n            this.metrics.loadTimes.push(entry.duration);\r\n            this.metrics.totalRequests++;\r\n          }\r\n        });\r\n      });\r\n      \r\n      observer.observe({ entryTypes: ['resource'] });\r\n    }\r\n    \r\n    // 定期报告性能指标\r\n    if (this.config.performance.reportInterval > 0) {\r\n      this.performanceTimer = setInterval(() => {\r\n        this.reportPerformanceMetrics();\r\n      }, this.config.performance.reportInterval);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 报告性能指标\r\n   */\r\n  private reportPerformanceMetrics(): void {\r\n    const metrics = this.getPerformanceMetrics();\r\n    \r\n    console.log('⚡ [性能指标]', metrics);\r\n    emit('resource:performance:report', metrics);\r\n    \r\n    // 如果配置了端点，发送到服务器\r\n    if (this.config.performance.metricsEndpoint) {\r\n      this.sendMetricsToServer(metrics);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 发送指标到服务器\r\n   */\r\n  private async sendMetricsToServer(metrics: any): Promise<void> {\r\n    try {\r\n      await fetch(this.config.performance.metricsEndpoint, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(metrics)\r\n      });\r\n    } catch (error) {\r\n      console.warn('⚡ [性能指标] 发送失败:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置智能缓存\r\n   */\r\n  private setupSmartCache(): void {\r\n    if (!this.config.smartCache.enabled) return;\r\n    \r\n    // 从localStorage恢复缓存\r\n    this.loadCacheFromStorage();\r\n    \r\n    // 定期清理过期缓存\r\n    setInterval(() => {\r\n      this.cleanExpiredCache();\r\n    }, 60000); // 每分钟检查一次\r\n  }\r\n\r\n  /**\r\n   * 从存储加载缓存\r\n   */\r\n  private loadCacheFromStorage(): void {\r\n    try {\r\n      const cached = localStorage.getItem('notion-resource-cache');\r\n      if (cached) {\r\n        const data = JSON.parse(cached);\r\n        this.resourceCache = new Map(data.entries);\r\n        this.metrics.cacheSize = data.size || 0;\r\n      }\r\n    } catch (error) {\r\n      console.warn('⚡ [智能缓存] 加载失败:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 清理过期缓存\r\n   */\r\n  private cleanExpiredCache(): void {\r\n    const now = Date.now();\r\n    let cleaned = 0;\r\n    \r\n    for (const [key, value] of this.resourceCache.entries()) {\r\n      if (value.expires && value.expires < now) {\r\n        this.resourceCache.delete(key);\r\n        cleaned++;\r\n      }\r\n    }\r\n    \r\n    if (cleaned > 0) {\r\n      this.saveCacheToStorage();\r\n      console.log(`⚡ [智能缓存] 清理过期项: ${cleaned}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 保存缓存到存储\r\n   */\r\n  private saveCacheToStorage(): void {\r\n    try {\r\n      const data = {\r\n        entries: Array.from(this.resourceCache.entries()),\r\n        size: this.metrics.cacheSize,\r\n        timestamp: Date.now()\r\n      };\r\n      \r\n      localStorage.setItem('notion-resource-cache', JSON.stringify(data));\r\n    } catch (error) {\r\n      console.warn('⚡ [智能缓存] 保存失败:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 节流函数\r\n   */\r\n  private throttle<T extends (...args: any[]) => any>(\r\n    func: T,\r\n    limit: number\r\n  ): (...args: Parameters<T>) => void {\r\n    let inThrottle = false;\r\n    \r\n    return (...args: Parameters<T>) => {\r\n      if (!inThrottle) {\r\n        func.apply(this, args);\r\n        inThrottle = true;\r\n        setTimeout(() => {\r\n          inThrottle = false;\r\n        }, limit);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取性能指标\r\n   */\r\n  getPerformanceMetrics(): PerformanceMetrics & { \r\n    averageLoadTime: number;\r\n    cacheHitRate: number;\r\n    predictiveHitRate: number;\r\n  } {\r\n    const averageLoadTime = this.metrics.loadTimes.length > 0 \r\n      ? this.metrics.loadTimes.reduce((a, b) => a + b, 0) / this.metrics.loadTimes.length \r\n      : 0;\r\n    \r\n    const cacheHitRate = this.metrics.totalRequests > 0 \r\n      ? this.metrics.cacheHits / this.metrics.totalRequests \r\n      : 0;\r\n    \r\n    const predictiveHitRate = this.metrics.predictiveAttempts > 0 \r\n      ? this.metrics.predictiveHits / this.metrics.predictiveAttempts \r\n      : 0;\r\n    \r\n    return {\r\n      ...this.metrics,\r\n      averageLoadTime,\r\n      cacheHitRate,\r\n      predictiveHitRate\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取配置\r\n   */\r\n  getConfig(): OptimizerConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * 更新配置\r\n   */\r\n  updateConfig(newConfig: Partial<OptimizerConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n    emit('resource:config:updated', this.config);\r\n  }\r\n\r\n  /**\r\n   * 销毁资源优化器\r\n   */\r\n  destroy(): void {\r\n    if (this.intersectionObserver) {\r\n      this.intersectionObserver.disconnect();\r\n      this.intersectionObserver = null;\r\n    }\r\n    \r\n    if (this.performanceTimer) {\r\n      clearInterval(this.performanceTimer);\r\n      this.performanceTimer = null;\r\n    }\r\n    \r\n    this.saveCacheToStorage();\r\n    this.resourceCache.clear();\r\n    this.predictiveQueue.clear();\r\n    \r\n    ResourceOptimizer.instance = null;\r\n    emit('resource:optimizer:destroyed');\r\n    console.log('⚡ [资源优化器] 已销毁');\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const resourceOptimizer = ResourceOptimizer.getInstance();\r\n\r\n// 自动初始化\r\nready(() => {\r\n  resourceOptimizer;\r\n});\r\n\r\nexport default ResourceOptimizer;\r\n", "/**\r\n * 前端内容渲染系统 - 现代化TypeScript版本\r\n * \r\n * 完全替代原有的前端内容处理JavaScript文件，包括：\r\n * - 所有前端组件的统一初始化和管理\r\n * - 全局事件处理和协调\r\n * - 性能优化和用户体验增强\r\n * - 向后兼容性支持\r\n */\r\n\r\nimport { emit, on } from '../shared/core/EventBus';\r\nimport { ready } from '../shared/utils/dom';\r\nimport { AnchorNavigation, anchorNavigation } from './components/AnchorNavigation';\r\nimport { LazyLoader, lazyLoader } from './components/LazyLoader';\r\nimport { ProgressiveLoader, progressiveLoader } from './components/ProgressiveLoader';\r\nimport { ResourceOptimizer, resourceOptimizer } from './components/ResourceOptimizer';\r\n\r\nexport interface FrontendContentConfig {\r\n  enableAnchorNavigation?: boolean;\r\n  enableLazyLoading?: boolean;\r\n  enableProgressiveLoading?: boolean;\r\n  enableResourceOptimization?: boolean;\r\n  enablePerformanceMonitoring?: boolean;\r\n}\r\n\r\n/**\r\n * 前端内容渲染系统主类\r\n */\r\nexport class FrontendContent {\r\n  private static instance: FrontendContent | null = null;\r\n  private initialized = false;\r\n  private config!: FrontendContentConfig;\r\n\r\n  // 组件实例\r\n  private anchorNavigation!: AnchorNavigation;\r\n  private lazyLoader!: LazyLoader;\r\n  private progressiveLoader!: ProgressiveLoader;\r\n  private resourceOptimizer!: ResourceOptimizer;\r\n\r\n  constructor(config: FrontendContentConfig = {}) {\r\n    if (FrontendContent.instance) {\r\n      return FrontendContent.instance;\r\n    }\r\n    \r\n    FrontendContent.instance = this;\r\n    \r\n    this.config = {\r\n      enableAnchorNavigation: true,\r\n      enableLazyLoading: true,\r\n      enableProgressiveLoading: true,\r\n      enableResourceOptimization: true,\r\n      enablePerformanceMonitoring: true,\r\n      ...config\r\n    };\r\n    \r\n    // 初始化组件实例\r\n    this.anchorNavigation = anchorNavigation;\r\n    this.lazyLoader = lazyLoader;\r\n    this.progressiveLoader = progressiveLoader;\r\n    this.resourceOptimizer = resourceOptimizer;\r\n  }\r\n\r\n  /**\r\n   * 获取单例实例\r\n   */\r\n  static getInstance(config?: FrontendContentConfig): FrontendContent {\r\n    if (!FrontendContent.instance) {\r\n      FrontendContent.instance = new FrontendContent(config);\r\n    }\r\n    return FrontendContent.instance;\r\n  }\r\n\r\n  /**\r\n   * 初始化前端内容渲染系统\r\n   */\r\n  init(): void {\r\n    if (this.initialized) {\r\n      console.warn('⚠️ [前端内容] 已经初始化，跳过重复初始化');\r\n      return;\r\n    }\r\n\r\n    console.log('🚀 [前端内容] 开始初始化...');\r\n\r\n    try {\r\n      this.setupGlobalEventHandlers();\r\n      this.setupComponentCoordination();\r\n      this.setupPerformanceMonitoring();\r\n      this.setupCompatibilityLayer();\r\n      this.applyConfiguration();\r\n      \r\n      this.initialized = true;\r\n      \r\n      emit('frontend:content:initialized');\r\n      console.log('✅ [前端内容] 初始化完成');\r\n      \r\n    } catch (error) {\r\n      console.error('❌ [前端内容] 初始化失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置全局事件处理器\r\n   */\r\n  private setupGlobalEventHandlers(): void {\r\n    // 页面卸载前的清理\r\n    window.addEventListener('beforeunload', () => {\r\n      this.cleanup();\r\n    });\r\n\r\n    // 页面可见性变化处理\r\n    document.addEventListener('visibilitychange', () => {\r\n      if (document.hidden) {\r\n        emit('frontend:page:hidden');\r\n      } else {\r\n        emit('frontend:page:visible');\r\n      }\r\n    });\r\n\r\n    // DOM变化监听（用于动态内容）\r\n    if ('MutationObserver' in window) {\r\n      const observer = new MutationObserver((mutations) => {\r\n        let hasNewContent = false;\r\n        \r\n        mutations.forEach(mutation => {\r\n          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {\r\n            mutation.addedNodes.forEach(node => {\r\n              if (node.nodeType === Node.ELEMENT_NODE) {\r\n                const element = node as HTMLElement;\r\n                if (element.querySelector && (\r\n                  element.querySelector('img[data-src]') ||\r\n                  element.querySelector('[id^=\"notion-block-\"]') ||\r\n                  element.querySelector('.notion-progressive-loading')\r\n                )) {\r\n                  hasNewContent = true;\r\n                }\r\n              }\r\n            });\r\n          }\r\n        });\r\n        \r\n        if (hasNewContent) {\r\n          this.handleDynamicContent();\r\n        }\r\n      });\r\n      \r\n      observer.observe(document.body, {\r\n        childList: true,\r\n        subtree: true\r\n      });\r\n    }\r\n\r\n    console.log('🎯 [全局事件] 已设置');\r\n  }\r\n\r\n  /**\r\n   * 设置组件协调\r\n   */\r\n  private setupComponentCoordination(): void {\r\n    // 懒加载完成后刷新锚点导航\r\n    on('lazy:image:loaded', () => {\r\n      this.anchorNavigation.updateHeaderOffset();\r\n    });\r\n\r\n    // 渐进式加载完成后刷新懒加载\r\n    on('progressive:load:success', () => {\r\n      this.lazyLoader.refresh();\r\n    });\r\n\r\n    // 锚点导航时暂停资源优化\r\n    on('anchor:scrolled', () => {\r\n      // 可以在这里暂停预测性加载等\r\n    });\r\n\r\n    console.log('🔗 [组件协调] 已设置');\r\n  }\r\n\r\n  /**\r\n   * 设置性能监控\r\n   */\r\n  private setupPerformanceMonitoring(): void {\r\n    if (!this.config.enablePerformanceMonitoring) return;\r\n\r\n    // 监控页面加载性能\r\n    window.addEventListener('load', () => {\r\n      setTimeout(() => {\r\n        this.reportPagePerformance();\r\n      }, 1000);\r\n    });\r\n\r\n    // 监控组件性能\r\n    on('anchor:scrolled', (_event, data) => {\r\n      this.trackComponentPerformance('anchor_navigation', data);\r\n    });\r\n\r\n    on('lazy:image:loaded', (_event, data) => {\r\n      this.trackComponentPerformance('lazy_loading', data);\r\n    });\r\n\r\n    on('progressive:load:success', (_event, data) => {\r\n      this.trackComponentPerformance('progressive_loading', data);\r\n    });\r\n\r\n    console.log('📊 [性能监控] 已设置');\r\n  }\r\n\r\n  /**\r\n   * 设置兼容性层\r\n   */\r\n  private setupCompatibilityLayer(): void {\r\n    // 为了向后兼容，在全局对象上暴露一些功能\r\n    const globalNotionWp = (window as any).notionToWp || {};\r\n    \r\n    // 暴露组件实例\r\n    globalNotionWp.frontend = {\r\n      anchorNavigation: this.anchorNavigation,\r\n      lazyLoader: this.lazyLoader,\r\n      progressiveLoader: this.progressiveLoader,\r\n      resourceOptimizer: this.resourceOptimizer\r\n    };\r\n\r\n    // 暴露主实例\r\n    globalNotionWp.frontendContent = this;\r\n\r\n    // 暴露常用方法\r\n    globalNotionWp.scrollToAnchor = (targetId: string) => {\r\n      return this.anchorNavigation.scrollToAnchor(targetId);\r\n    };\r\n\r\n    globalNotionWp.refreshLazyLoading = () => {\r\n      this.lazyLoader.refresh();\r\n    };\r\n\r\n    (window as any).notionToWp = globalNotionWp;\r\n\r\n    console.log('🔄 [兼容性层] 已设置');\r\n  }\r\n\r\n  /**\r\n   * 应用配置\r\n   */\r\n  private applyConfiguration(): void {\r\n    // 根据配置启用/禁用功能\r\n    if (!this.config.enableAnchorNavigation) {\r\n      // 可以在这里禁用锚点导航\r\n    }\r\n\r\n    if (!this.config.enableLazyLoading) {\r\n      // 可以在这里禁用懒加载\r\n    }\r\n\r\n    if (!this.config.enableProgressiveLoading) {\r\n      // 可以在这里禁用渐进式加载\r\n    }\r\n\r\n    if (!this.config.enableResourceOptimization) {\r\n      // 可以在这里禁用资源优化\r\n    }\r\n\r\n    console.log('⚙️ [配置应用] 完成:', this.config);\r\n  }\r\n\r\n  /**\r\n   * 处理动态内容\r\n   */\r\n  private handleDynamicContent(): void {\r\n    console.log('🔄 [动态内容] 检测到新内容，刷新组件...');\r\n    \r\n    // 刷新懒加载\r\n    this.lazyLoader.refresh();\r\n    \r\n    // 更新锚点导航\r\n    this.anchorNavigation.updateHeaderOffset();\r\n    \r\n    emit('frontend:dynamic:content:detected');\r\n  }\r\n\r\n  /**\r\n   * 报告页面性能\r\n   */\r\n  private reportPagePerformance(): void {\r\n    if (!('performance' in window)) return;\r\n\r\n    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\r\n    if (!navigation) return;\r\n\r\n    const metrics = {\r\n      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,\r\n      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,\r\n      firstPaint: 0,\r\n      firstContentfulPaint: 0\r\n    };\r\n\r\n    // 获取绘制指标\r\n    const paintEntries = performance.getEntriesByType('paint');\r\n    paintEntries.forEach(entry => {\r\n      if (entry.name === 'first-paint') {\r\n        metrics.firstPaint = entry.startTime;\r\n      } else if (entry.name === 'first-contentful-paint') {\r\n        metrics.firstContentfulPaint = entry.startTime;\r\n      }\r\n    });\r\n\r\n    console.log('📊 [页面性能]', metrics);\r\n    emit('frontend:performance:report', metrics);\r\n  }\r\n\r\n  /**\r\n   * 追踪组件性能\r\n   */\r\n  private trackComponentPerformance(component: string, data: any): void {\r\n    const timestamp = Date.now();\r\n    \r\n    console.log(`📊 [组件性能] ${component}:`, data);\r\n    emit('frontend:component:performance', { component, data, timestamp });\r\n  }\r\n\r\n  /**\r\n   * 获取组件实例\r\n   */\r\n  getAnchorNavigation(): AnchorNavigation {\r\n    return this.anchorNavigation;\r\n  }\r\n\r\n  getLazyLoader(): LazyLoader {\r\n    return this.lazyLoader;\r\n  }\r\n\r\n  getProgressiveLoader(): ProgressiveLoader {\r\n    return this.progressiveLoader;\r\n  }\r\n\r\n  getResourceOptimizer(): ResourceOptimizer {\r\n    return this.resourceOptimizer;\r\n  }\r\n\r\n  /**\r\n   * 获取配置\r\n   */\r\n  getConfig(): FrontendContentConfig {\r\n    return { ...this.config };\r\n  }\r\n\r\n  /**\r\n   * 更新配置\r\n   */\r\n  updateConfig(newConfig: Partial<FrontendContentConfig>): void {\r\n    this.config = { ...this.config, ...newConfig };\r\n    this.applyConfiguration();\r\n    emit('frontend:config:updated', this.config);\r\n  }\r\n\r\n  /**\r\n   * 检查是否已初始化\r\n   */\r\n  isInitialized(): boolean {\r\n    return this.initialized;\r\n  }\r\n\r\n  /**\r\n   * 获取系统状态\r\n   */\r\n  getSystemStatus(): {\r\n    initialized: boolean;\r\n    componentsActive: {\r\n      anchorNavigation: boolean;\r\n      lazyLoader: boolean;\r\n      progressiveLoader: boolean;\r\n      resourceOptimizer: boolean;\r\n    };\r\n    config: FrontendContentConfig;\r\n  } {\r\n    return {\r\n      initialized: this.initialized,\r\n      componentsActive: {\r\n        anchorNavigation: true, // AnchorNavigation 总是活跃的\r\n        lazyLoader: this.lazyLoader.isObserverSupported(),\r\n        progressiveLoader: true, // ProgressiveLoader 总是活跃的\r\n        resourceOptimizer: true // ResourceOptimizer 总是活跃的\r\n      },\r\n      config: this.config\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 清理资源\r\n   */\r\n  cleanup(): void {\r\n    if (!this.initialized) return;\r\n\r\n    console.log('🧹 [前端内容] 开始清理...');\r\n\r\n    try {\r\n      this.anchorNavigation.destroy();\r\n      this.lazyLoader.destroy();\r\n      this.progressiveLoader.destroy();\r\n      this.resourceOptimizer.destroy();\r\n      \r\n      this.initialized = false;\r\n      FrontendContent.instance = null;\r\n      \r\n      emit('frontend:content:destroyed');\r\n      console.log('✅ [前端内容] 清理完成');\r\n    } catch (error) {\r\n      console.error('❌ [前端内容] 清理失败:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 销毁实例\r\n   */\r\n  destroy(): void {\r\n    this.cleanup();\r\n  }\r\n}\r\n\r\n// 导出单例实例\r\nexport const frontendContent = FrontendContent.getInstance();\r\n\r\n// 自动初始化\r\nready(() => {\r\n  frontendContent.init();\r\n});\r\n\r\nexport default FrontendContent;\r\n", "/**\r\n * 前端入口文件\r\n */\r\n\r\nimport { ready } from '../shared/utils/dom';\r\nimport { eventBus, emit } from '../shared/core/EventBus';\r\nimport { frontendContent } from './FrontendContent';\r\n\r\n// 导入样式\r\nimport '../styles/frontend/frontend.scss';\r\n\r\n/**\r\n * 前端应用主类\r\n */\r\nclass FrontendApp {\r\n  private initialized = false;\r\n\r\n  /**\r\n   * 初始化应用\r\n   */\r\n  init(): void {\r\n    if (this.initialized) {\r\n      return;\r\n    }\r\n\r\n    console.log('🚀 Notion to WordPress Frontend App initializing...');\r\n\r\n    // 初始化组件\r\n    this.initializeComponents();\r\n\r\n    // 绑定事件\r\n    this.bindEvents();\r\n\r\n    this.initialized = true;\r\n    emit('frontend:initialized');\r\n\r\n    console.log('✅ Notion to WordPress Frontend App initialized');\r\n  }\r\n\r\n  /**\r\n   * 初始化组件\r\n   */\r\n  private initializeComponents(): void {\r\n    // 初始化现代化前端内容渲染系统\r\n    this.initializeFrontendContent();\r\n\r\n    // 初始化Notion块渲染器\r\n    this.initNotionBlocks();\r\n\r\n    // 初始化懒加载\r\n    this.initLazyLoading();\r\n\r\n    // 初始化数学公式渲染\r\n    this.initMathRendering();\r\n\r\n    emit('frontend:components:init');\r\n  }\r\n\r\n  /**\r\n   * 初始化Notion块\r\n   */\r\n  private initNotionBlocks(): void {\r\n    const notionBlocks = document.querySelectorAll('.notion-block');\r\n    console.log(`Found ${notionBlocks.length} Notion blocks`);\r\n\r\n    notionBlocks.forEach(block => {\r\n      // 这里将处理各种Notion块类型\r\n      const blockType = block.getAttribute('data-block-type');\r\n      emit('frontend:block:init', { block, blockType });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 初始化懒加载\r\n   */\r\n  private initLazyLoading(): void {\r\n    if ('IntersectionObserver' in window) {\r\n      const lazyImages = document.querySelectorAll('img[data-src]');\r\n      \r\n      if (lazyImages.length > 0) {\r\n        const imageObserver = new IntersectionObserver((entries) => {\r\n          entries.forEach(entry => {\r\n            if (entry.isIntersecting) {\r\n              const img = entry.target as HTMLImageElement;\r\n              const src = img.getAttribute('data-src');\r\n              \r\n              if (src) {\r\n                img.src = src;\r\n                img.removeAttribute('data-src');\r\n                imageObserver.unobserve(img);\r\n                \r\n                emit('frontend:image:loaded', { img, src });\r\n              }\r\n            }\r\n          });\r\n        });\r\n\r\n        lazyImages.forEach(img => imageObserver.observe(img));\r\n        console.log(`Initialized lazy loading for ${lazyImages.length} images`);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 初始化数学公式渲染\r\n   */\r\n  private initMathRendering(): void {\r\n    const mathElements = document.querySelectorAll('.notion-equation');\r\n    \r\n    if (mathElements.length > 0) {\r\n      console.log(`Found ${mathElements.length} math equations`);\r\n      \r\n      // 动态加载KaTeX（如果需要）\r\n      this.loadMathRenderer().then(() => {\r\n        mathElements.forEach(element => {\r\n          emit('frontend:math:render', { element });\r\n        });\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 动态加载数学渲染器\r\n   */\r\n  private async loadMathRenderer(): Promise<void> {\r\n    // 这里将动态加载KaTeX或其他数学渲染库\r\n    return new Promise((resolve) => {\r\n      // 模拟异步加载\r\n      setTimeout(resolve, 100);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 初始化前端内容渲染系统\r\n   */\r\n  private initializeFrontendContent(): void {\r\n    // 初始化现代化的前端内容渲染系统\r\n    frontendContent.init();\r\n\r\n    // 监听前端内容系统事件\r\n    eventBus.on('frontend:content:initialized', () => {\r\n      console.log('✅ 前端内容渲染系统已初始化');\r\n    });\r\n\r\n    eventBus.on('frontend:content:destroyed', () => {\r\n      console.log('🔥 前端内容渲染系统已销毁');\r\n    });\r\n\r\n    // 监听性能报告事件\r\n    eventBus.on('frontend:performance:report', (_event, metrics) => {\r\n      console.log('📊 前端性能报告:', metrics);\r\n    });\r\n\r\n    console.log('✅ 前端内容渲染系统已初始化');\r\n  }\r\n\r\n  /**\r\n   * 绑定事件\r\n   */\r\n  private bindEvents(): void {\r\n    // 监听页面滚动\r\n    let scrollTimeout: NodeJS.Timeout;\r\n    window.addEventListener('scroll', () => {\r\n      clearTimeout(scrollTimeout);\r\n      scrollTimeout = setTimeout(() => {\r\n        emit('frontend:scroll', {\r\n          scrollY: window.scrollY,\r\n          scrollX: window.scrollX\r\n        });\r\n      }, 100);\r\n    });\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', () => {\r\n      emit('frontend:resize', {\r\n        width: window.innerWidth,\r\n        height: window.innerHeight\r\n      });\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 销毁应用\r\n   */\r\n  destroy(): void {\r\n    if (!this.initialized) {\r\n      return;\r\n    }\r\n\r\n    // 清理前端内容渲染系统\r\n    frontendContent.destroy();\r\n\r\n    emit('frontend:destroy');\r\n    eventBus.removeAllListeners();\r\n    this.initialized = false;\r\n\r\n    console.log('🔥 Notion to WordPress Frontend App destroyed');\r\n  }\r\n}\r\n\r\n/**\r\n * 创建全局应用实例\r\n */\r\nconst frontendApp = new FrontendApp();\r\n\r\n/**\r\n * 导出到全局作用域\r\n */\r\ndeclare global {\r\n  interface Window {\r\n    NotionWpFrontend: FrontendApp;\r\n  }\r\n}\r\n\r\nwindow.NotionWpFrontend = frontendApp;\r\n\r\n/**\r\n * DOM准备就绪后初始化\r\n */\r\nready(() => {\r\n  frontendApp.init();\r\n});\r\n\r\n/**\r\n * 导出主要功能\r\n */\r\nexport { frontendApp };\r\nexport default frontendApp;\r\n"], "names": ["AnchorNavigation", "config", "arguments", "length", "undefined", "_classCallCheck", "_defineProperty", "instance", "this", "_objectSpread", "headerSelectors", "smoothScrollSupported", "document", "documentElement", "style", "highlightDuration", "scrollOffset", "supportsSmoothScroll", "init", "key", "value", "updateHeaderOffset", "setupEventListeners", "handleInitialHash", "emit", "addEventListener", "handleAnchorClick", "bind", "window", "debounce", "handleHashChange", "setupHeaderObserver", "_this", "resizeObserver", "ResizeObserver", "for<PERSON>ach", "selector", "querySelectorAll", "element", "HTMLElement", "observe", "maxHeight", "querySelector", "getComputedStyle", "position", "Math", "max", "offsetHeight", "newOffset", "calculateHeaderOffset", "headerOffset", "setProperty", "concat", "offset", "targetId", "startsWith", "cleanId", "replace", "target", "getElementById", "scrollTarget", "id", "rect", "getBoundingClientRect", "performScroll", "highlightBlock", "updateURL", "_this2", "scrollOptions", "block", "behavior", "scrollIntoView", "setTimeout", "top", "scrollBy", "classList", "remove", "offsetWidth", "add", "removeHighlight", "removeEventListener", "once", "history", "replaceState", "hash", "error", "event", "link", "closest", "preventDefault", "href", "getAttribute", "scrollToAnchor", "location", "_this3", "func", "wait", "timeout", "_this4", "_len", "args", "Array", "_key", "clearTimeout", "apply", "newConfig", "anchors", "push", "disconnect", "removeProperty", "anchorNavigation", "getInstance", "ready", "e", "t", "r", "Symbol", "n", "iterator", "o", "toStringTag", "i", "c", "prototype", "Generator", "u", "Object", "create", "_regeneratorDefine2", "f", "p", "y", "G", "v", "a", "d", "l", "TypeError", "call", "done", "return", "GeneratorFunction", "GeneratorFunctionPrototype", "getPrototypeOf", "setPrototypeOf", "__proto__", "displayName", "_regenerator", "w", "m", "defineProperty", "_invoke", "enumerable", "configurable", "writable", "asyncGeneratorStep", "Promise", "resolve", "then", "_asyncToGenerator", "_next", "_throw", "ownKeys", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "getOwnPropertyDescriptors", "defineProperties", "_defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_typeof", "toPrimitive", "String", "Number", "_toPrimitive", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "Map", "rootMargin", "threshold", "loadingClass", "loadedClass", "errorClass", "observedClass", "retryAttempts", "retry<PERSON><PERSON><PERSON>", "supportsIntersectionObserver", "createObserver", "observeImages", "fallbackLoad", "observerSupported", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "img", "loadImage", "unobserve", "lazyImages", "HTMLImageElement", "_loadImage", "_callee", "src", "_t", "_context", "dataset", "_lazyOriginalSrc", "preloadImage", "loadedImages", "dispatchEvent", "CustomEvent", "detail", "handleImageError", "_x", "reject", "imageLoader", "Image", "onload", "onerror", "Error", "_handleImageError", "_callee2", "retryCount", "_context2", "_lazyRetryCount", "errorImages", "setErrorPlaceholder", "_x2", "_x3", "_preloadImages", "_callee4", "urls", "promises", "_this5", "_context4", "isArray", "map", "_ref", "_callee3", "url", "_context3", "_x5", "allSettled", "count", "_x4", "totalImages", "retryQueue", "size", "clear", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loadingDelay", "batchSize", "handleLoadMoreClick", "button", "loadMore", "_loadMore", "container", "containerId", "data", "generateContainerId", "loadingStates", "get", "setLoadingState", "set", "fetchMoreData", "records", "renderRecords", "hasMore", "hideLoadMoreButton", "refresh", "recordCount", "handleLoadError", "_fetchMoreData", "recordsData", "endpoint", "params", "response", "JSON", "parse", "atob", "getLoadParams", "post", "success", "message", "batch_size", "paramName", "toLowerCase", "_renderRecords", "contentContainer", "html", "tempDiv", "record", "renderRecord", "join", "createElement", "innerHTML", "opacity", "transition", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "title", "extractTitle", "properties", "substring", "escapeHtml", "formatDate", "created_time", "_i", "_Object$values", "values", "property", "type", "plain_text", "loading", "loadingText", "loadingSpinner", "buttonText", "disabled", "display", "textContent", "buttonContainer", "parentElement", "Date", "now", "random", "toString", "text", "div", "dateString", "toLocaleDateString", "year", "month", "day", "_unused", "delete", "loadedData", "progressive<PERSON><PERSON>der", "ResourceOptimizer", "cdn", "enabled", "baseUrl", "fallback<PERSON><PERSON>bled", "lazyLoading", "enhanced", "preloadThreshold", "performance", "reportInterval", "metricsEndpoint", "predictiveLoading", "hoverDelay", "scrollThreshold", "maxPredictions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smartCache", "maxCacheSize", "ttl", "compressionEnabled", "versionCheck", "metrics", "loadTimes", "errors", "cacheHits", "totalRequests", "predictiveHits", "predictiveAttempts", "cacheSize", "userBeh<PERSON>or", "scrollSpeed", "hoverTargets", "clickPatterns", "lastActivity", "setupUserBehaviorTracking", "setupPredictiveLoading", "setupPerformanceMonitoring", "setupSmartCache", "lastScrollY", "scrollY", "scrollStartTime", "handleScroll", "throttle", "currentScrollY", "currentTime", "distance", "abs", "time", "passive", "tagName", "includes", "predictResource", "shift", "intersectionObserver", "analyzePredictiveOpportunity", "predictiveQueue", "has", "calculatePredictionConfidence", "confidence", "clickCount", "pattern", "min", "preloadResource", "_preloadResource", "rel", "head", "PerformanceObserver", "list", "getEntries", "entryType", "duration", "entryTypes", "performanceTimer", "setInterval", "reportPerformanceMetrics", "getPerformanceMetrics", "sendMetricsToServer", "_sendMetricsToServer", "fetch", "method", "headers", "body", "stringify", "loadCacheFromStorage", "cleanExpiredCache", "cached", "localStorage", "getItem", "resourceCache", "_step", "cleaned", "_iterator", "_createForOfIteratorHelper", "s", "_step$value", "_slicedToArray", "expires", "err", "saveCacheToStorage", "from", "timestamp", "setItem", "limit", "_this6", "inThrottle", "averageLoadTime", "reduce", "b", "cacheHitRate", "predictiveHitRate", "clearInterval", "resourceOptimizer", "FrontendContent", "enableAnchorNavigation", "enableLazyLoading", "enableProgressiveLoading", "enableResourceOptimization", "enablePerformanceMonitoring", "initialized", "setupGlobalEventHandlers", "setupComponentCoordination", "setupCompatibilityLayer", "applyConfiguration", "cleanup", "hidden", "MutationObserver", "mutations", "<PERSON><PERSON><PERSON><PERSON><PERSON>nt", "mutation", "addedNodes", "node", "nodeType", "Node", "ELEMENT_NODE", "handleDynamicContent", "childList", "subtree", "on", "reportPagePerformance", "_event", "trackComponentPerformance", "globalNotionWp", "notionToWp", "frontend", "frontend<PERSON><PERSON><PERSON>", "refreshLazyLoading", "navigation", "getEntriesByType", "domContentLoaded", "domContentLoadedEventEnd", "domContentLoadedEventStart", "loadComplete", "loadEventEnd", "loadEventStart", "<PERSON><PERSON><PERSON><PERSON>", "firstContentful<PERSON><PERSON>t", "name", "startTime", "component", "componentsActive", "isObserverSupported", "destroy", "frontendApp", "FrontendApp", "initializeComponents", "bindEvents", "initializeFrontendContent", "initNotionBlocks", "initLazyLoading", "initMath<PERSON><PERSON>ing", "blockType", "imageObserver", "removeAttribute", "mathElements", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_load<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventBus", "scrollTimeout", "scrollX", "width", "innerWidth", "height", "innerHeight", "removeAllListeners", "NotionWpFrontend"], "sourceRoot": ""}