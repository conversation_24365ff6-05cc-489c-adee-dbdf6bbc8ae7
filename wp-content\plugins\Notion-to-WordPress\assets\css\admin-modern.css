/**
 * 插件后台现代化样式
 *
 * 为 Notion to WordPress 插件后台提供现代化、响应式的用户界面样式。
 *
 * 特性：
 * - WCAG 2.1 AA 级别的颜色对比度
 * - 三层响应式断点设计 (1024px, 768px, 480px)
 * - 硬件加速的动画效果
 * - 统一的设计系统和CSS变量
 * - 触摸友好的交互元素
 *
 * @since      2.0.0
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON><PERSON>/Notion-to-WordPress
 */

:root {
  /* 主色调 - 优化对比度 */
  --notion-primary: #1e40af;
  --notion-primary-dark: #1e3a8a;
  --notion-primary-light: #eff6ff;
  --notion-secondary: #1f2937;
  --notion-accent: #047857;
  --notion-accent-light: #ecfdf5;

  /* 状态颜色 - 提升对比度 */
  --notion-danger: #b91c1c;
  --notion-danger-light: #fef2f2;
  --notion-warning: #b45309;
  --notion-warning-light: #fffbeb;
  --notion-info: #0369a1;
  --notion-info-light: #eff6ff;
  --notion-success: #047857;
  --notion-success-light: #ecfdf5;

  /* 灰度系统 - 优化可读性 */
  --notion-gray-50: #f9fafb;
  --notion-gray-100: #f3f4f6;
  --notion-gray-200: #e5e7eb;
  --notion-gray-300: #d1d5db;
  --notion-gray-400: #9ca3af;
  --notion-gray-500: #6b7280;
  --notion-gray-600: #4b5563;
  --notion-gray-700: #374151;
  --notion-gray-800: #1f2937;
  --notion-gray-900: #111827;

  /* 设计系统 */
  --notion-border-radius: 8px;
  --notion-border-radius-sm: 4px;
  --notion-border-radius-lg: 12px;
  --notion-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --notion-box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --notion-transition: all 0.15s ease-in-out;
  --notion-transition-fast: all 0.1s ease-in-out;

  /* 布局 */
  --notion-sidebar-width: 240px;
  --notion-content-width: calc(100% - var(--notion-sidebar-width) - 20px);

  /* 字体 */
  --notion-font-size-xs: 0.75rem;
  --notion-font-size-sm: 0.875rem;
  --notion-font-size-base: 1rem;
  --notion-font-size-lg: 1.125rem;
  --notion-font-size-xl: 1.25rem;
}

/* 字体与基本布局 */
body.wp-admin {
    font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* 表格 */
.wp-list-table.widetable {
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,.05);
}
.wp-list-table.widetable th {
    background: #f1f2f4;
    color: #333;
    font-weight: 600;
}
.wp-list-table.widetable tr:nth-child(even) {
    background: #fafafa;
}
.wp-list-table.widetable tr:hover {
    background: #f5f7fa;
}

/* 按钮微调 */
#notion-to-wordpress-plugin-admin .button {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}
#notion-to-wordpress-plugin-admin .button .dashicons {
    line-height: 20px;
    font-size: 18px;
}

/* Loading Overlay */
#loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-message {
    background: white;
    color: var(--notion-gray-800);
    font-size: 16px;
    padding: 20px 25px;
    border-radius: var(--notion-border-radius);
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.loading-message .spinner {
    float: none !important;
    margin: 0 !important;
}

/* Toast Notice */
.notion-toast-notice {
    position: fixed;
    top: 80px;
    right: 24px;
    background: #007cba;
    color: #fff;
    padding: 10px 16px;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,.15);
    z-index: 10000;
    opacity: 0;
    transition: opacity .3s ease;
}
.notion-toast-notice.show {
    opacity: 1;
}

/* 统计卡片 */
.notion-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin: 20px 0 30px;
}
.stat-card {
    background: white;
    border-radius: var(--notion-border-radius);
    padding: 20px;
    box-shadow: var(--notion-box-shadow);
    text-align: center;
    border-top: 4px solid var(--notion-primary);
    transition: var(--notion-transition);
}
.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.stat-card h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--notion-primary);
}
.stat-card span {
    color: var(--notion-gray-700);
    font-size: 14px;
    font-weight: 500;
}

.stat-card .stat-imported-count,
.stat-card .stat-published-count {
    font-size: 32px;
    min-height: 48px; /* 保持与日期卡片一致的高度，底部标签对齐 */
    display:flex;
    align-items:center;
    justify-content:center;
}

.stat-card .stat-last-update,
.stat-card .stat-next-run {
    font-size: 20px !important; /* 日期/文字稍小，避免撑高卡片 */
    line-height: 1.3;
    word-wrap: break-word;
    overflow-wrap: break-word;
    min-height: 48px; /* 保持卡片高度一致 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式：小屏表格横向滚动 */
@media (max-width: 782px) {
    .wp-list-table.widetable {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    .notion-stats-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* 侧边栏样式 - 统一使用CSS变量 */
.ncf-admin-aside {
    background: var(--notion-gray-50);
    color: var(--notion-gray-800);
    border-right: 1px solid var(--notion-gray-200);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
}

/* 修复可能的黑色小块问题 */
.notion-to-wordpress-admin *::before,
.notion-to-wordpress-admin *::after {
    background: transparent !important;
}

/* ================ 性能监控页面样式 ================ */
.notion-wp-performance-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

.notion-wp-performance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.notion-wp-performance-card {
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: var(--notion-border-radius);
    padding: 20px;
    box-shadow: var(--notion-box-shadow);
    transition: var(--notion-transition);
}

.notion-wp-performance-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.notion-wp-performance-card h3 {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 600;
    color: var(--notion-gray-900);
    border-bottom: 2px solid var(--notion-primary);
    padding-bottom: 8px;
}

.notion-wp-performance-config,
.notion-wp-system-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.config-item:last-child {
    border-bottom: none;
}

.config-label {
    font-weight: 500;
    color: var(--notion-gray-700);
}

.config-value {
    font-weight: 600;
    color: var(--notion-gray-900);
    background: var(--notion-gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}

.config-value.enabled {
    background: var(--notion-accent-light);
    color: var(--notion-accent);
}

.config-value.disabled {
    background: var(--notion-danger-light);
    color: var(--notion-danger);
}

.notion-wp-performance-actions {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    justify-content: center;
}

.notion-wp-performance-tips {
    background: var(--notion-primary-light);
    border: 1px solid var(--notion-primary);
    border-radius: var(--notion-border-radius);
    padding: 20px;
    margin-top: 20px;
}

.notion-wp-performance-tips h3 {
    margin: 0 0 15px 0;
    color: var(--notion-primary);
}

.notion-wp-performance-tips ul {
    margin: 0;
    padding-left: 20px;
}

.notion-wp-performance-tips li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.notion-wp-async-status,
.notion-wp-index-status {
    background: var(--notion-gray-100);
    border-radius: var(--notion-border-radius);
    padding: 15px;
    margin: 15px 0;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--notion-gray-600);
}

.loading-placeholder .spinner {
    width: 20px;
    height: 20px;
}

.notion-wp-async-actions,
.notion-wp-index-actions {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.notion-wp-async-info,
.notion-wp-index-info {
    background: var(--notion-info);
    color: white;
    padding: 15px;
    border-radius: var(--notion-border-radius);
    margin-top: 15px;
}

.notion-wp-async-info h4,
.notion-wp-index-info h4 {
    margin: 0 0 10px 0;
    color: white;
}

.notion-wp-async-info ul,
.notion-wp-index-info ul {
    margin: 0;
    padding-left: 20px;
}

.notion-wp-async-info li,
.notion-wp-index-info li {
    margin-bottom: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .notion-wp-performance-cards {
        grid-template-columns: 1fr;
    }

    .notion-wp-performance-actions,
    .notion-wp-async-actions,
    .notion-wp-index-actions {
        flex-direction: column;
    }

    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 确保没有意外的黑色背景 */
.notion-to-wordpress-admin .black-block,
.notion-to-wordpress-admin .dark-block {
    background: transparent !important;
    color: inherit !important;
}

/* 修复可能的图标显示问题 */
.notion-to-wordpress-admin .dashicons {
    color: inherit;
    background: transparent;
}

/* 自定义品牌图标样式 */
.notion-wp-logo {
    width: 20px !important;
    height: 20px !important;
    background-image: url('../icon.svg') !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    display: inline-block !important;
    vertical-align: middle !important;
    margin-right: 8px !important;
}

/* WordPress 菜单图标样式 - 放大一倍 */
#adminmenu .wp-menu-image img,
#adminmenu .wp-menu-image svg {
    width: 40px !important;
    height: 40px !important;
}

/* 针对我们插件的菜单项 */
#adminmenu #toplevel_page_notion-to-wordpress .wp-menu-image {
    background-size: 40px 40px !important;
}

.notion-wp-logo:before {
    content: '' !important;
    display: none !important;
}

/* 头部标题中的图标样式 - 放大两倍 */
.wp-heading-inline .notion-wp-logo {
    width: 80px !important;
    height: 80px !important;
    margin-right: 12px !important;
    vertical-align: text-bottom !important;
}

/* 按钮中的图标样式 */
.button .notion-wp-logo {
    width: 16px !important;
    height: 16px !important;
    margin-right: 6px !important;
    vertical-align: middle !important;
    display: inline-block !important;
}

/* 菜单项样式优化 */
.notion-wp-menu-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    border: none;
    background: transparent;
    color: var(--notion-gray-700);
    border-radius: 8px;
    margin-bottom: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    width: 100%;
}

.notion-wp-menu-item:hover {
    background: var(--notion-gray-100);
    color: var(--notion-gray-900);
}

.notion-wp-menu-item.active {
    background: var(--notion-primary);
    color: white;
    font-weight: 600;
}

.notion-wp-menu-item.active:hover {
    background: var(--notion-primary-dark);
}

/* WordPress 暗模式兼容*/
body.admin-color-dark .ncf-admin-aside {
    background: #f9f9f9;
    color: #333;
}

/* 管理页面样式 */
:root {
  --ncf-primary: #2271b1;
  --ncf-primary-hover: #135e96;
  --ncf-accent: #2fb344;
}

/* 基本卡片和布局样式 */
.notion-to-wordpress-header {
  background: #fff;
  border-bottom: 1px solid #e2e4e7;
  padding: 12px 20px;
  margin-left: -20px;
  display: flex;
  align-items: center;
}

.notion-to-wordpress-header-inner {
  width: 100%;
  display: flex;
  align-items: center;
}

.notion-to-wordpress-logo {
  font-size: 36px;
  height: 36px;
  width: 36px;
  color: var(--ncf-primary);
  margin-right: 10px;
  vertical-align: middle;
}

.notion-to-wordpress-title {
  font-size: 1.3em;
  font-weight: 600;
  margin: 0;
  padding: 0;
  flex-grow: 1;
}

.notion-to-wordpress-nav {
  display: flex;
  gap: 15px;
}

.notion-to-wordpress-nav a {
  color: #1d2327;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 4px;
}

.notion-to-wordpress-nav a:hover {
  background: rgba(0,0,0,0.04);
}

.notion-to-wordpress-nav a.active {
  color: var(--ncf-primary);
  background: rgba(34, 113, 177, 0.1);
  font-weight: 600;
}

/**
 * 响应式管理界面样式
 *
 * @since      1.0.5
 * @package    Notion_To_WordPress
 */

/* 基础样式重置 */
.notion-wp-admin {
  max-width: 100%;
  margin: 20px 20px 20px 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  color: var(--notion-gray-900);
}

.notion-wp-admin * {
  box-sizing: border-box;
}

/* 页面标题区域 */
.notion-wp-header {
  background: white;
  padding: 15px 20px;
  border-radius: var(--notion-border-radius);
  box-shadow: var(--notion-box-shadow);
  margin-bottom: 20px;
}

.notion-wp-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notion-wp-admin h1.wp-heading-inline {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  padding: 0;
  color: var(--notion-secondary);
}

.notion-wp-logo {
  font-size: 32px;
  height: 32px;
  width: 32px;
  color: var(--notion-primary);
  margin-right: 12px;
}

.notion-wp-version {
  font-size: 14px;
  color: var(--notion-gray-600);
  font-weight: 500;
  background: var(--notion-gray-100);
  padding: 4px 10px;
  border-radius: 20px;
}

/* 分栏布局 */
.notion-wp-layout {
  display: flex;
  gap: 20px;
}

.notion-wp-sidebar {
  width: var(--notion-sidebar-width);
  flex-shrink: 0;
}

.notion-wp-content {
  width: var(--notion-content-width);
  flex-grow: 1;
}

/* 侧边栏菜单 */
.notion-wp-menu {
  background: white;
  border-radius: var(--notion-border-radius);
  box-shadow: var(--notion-box-shadow);
  overflow: hidden;
  margin-bottom: 20px;
}

.notion-wp-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 15px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  color: var(--notion-gray-700);
  border-left: 3px solid transparent;
  transition: var(--notion-transition);
}

.notion-wp-menu-item:hover {
  background: var(--notion-gray-100);
  color: var(--notion-gray-900);
}

.notion-wp-menu-item.active {
  background: var(--notion-primary);
  color: #fff;
  border-left-color: var(--notion-primary-dark);
  font-weight: 600;
}

.notion-wp-menu-item .dashicons {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* 作者卡片 */
.notion-wp-author-card {
  background: white;
  border-radius: var(--notion-border-radius);
  box-shadow: var(--notion-box-shadow);
  padding: 20px;
  text-align: center;
}

.notion-wp-author-avatar {
  margin-bottom: 15px;
}

.notion-wp-author-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--notion-primary);
}

.notion-wp-author-info h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  color: var(--notion-gray-900);
}

.notion-wp-author-info p {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: var(--notion-gray-600);
}

.notion-wp-author-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.notion-wp-author-links a {
  color: var(--notion-gray-600);
  text-decoration: none;
  transition: var(--notion-transition);
}

.notion-wp-author-links a:hover {
  color: var(--notion-primary);
}

.notion-wp-author-links .dashicons {
  font-size: 22px;
  width: 22px;
  height: 22px;
}

/* 内容区域 */
.notion-wp-tab-content {
  display: none !important;
  background: white;
  border-radius: var(--notion-border-radius);
  padding: 25px;
  box-shadow: var(--notion-box-shadow);
  margin-bottom: 25px;
}

.notion-wp-tab-content.active {
  display: block !important;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.notion-wp-settings-section h2 {
  font-size: 20px;
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--notion-secondary);
  padding-bottom: 12px;
  border-bottom: 1px solid var(--notion-gray-200);
}

/* Toast提示样式 */
.notion-wp-toast {
  position: fixed;
  right: 24px;
  bottom: 24px;
  min-width: 250px;
  max-width: 350px;
  background: var(--notion-gray-900);
  color: #fff;
  padding: 12px 16px;
  border-radius: var(--notion-border-radius);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  z-index: 9999;
  display: none; /* 默认隐藏 */
  align-items: center;
  gap: 10px;
  opacity: 0;
  transform: translateX(100%);
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
  border-left: 4px solid var(--notion-primary);
}

.notion-wp-toast.show {
  display: flex;
  opacity: 0.95;
  transform: translateX(0);
}

.notion-wp-toast.success {
  background: var(--notion-accent);
  border-left-color: #047857;
}

.notion-wp-toast.error {
  background: var(--notion-danger);
  border-left-color: #b91c1c;
}

.notion-wp-toast.info {
  background: var(--notion-info);
  border-left-color: var(--notion-primary-dark);
}

.notion-wp-toast-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notion-wp-toast.success .notion-wp-toast-icon .dashicons {
  color: var(--notion-accent);
}

.notion-wp-toast.error .notion-wp-toast-icon .dashicons {
  color: var(--notion-danger);
}

.notion-wp-toast.info .notion-wp-toast-icon .dashicons {
  color: var(--notion-primary);
}

.notion-wp-toast-content {
  flex-grow: 1;
  font-size: 14px;
  line-height: 1.5;
}

.notion-wp-toast-close {
  background: transparent;
  border: none;
  color: var(--notion-gray-500);
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  transition: var(--notion-transition);
}

.notion-wp-toast-close:hover {
  color: var(--notion-gray-900);
}

/* 表单样式 - 优化选择器 */
.notion-wp-admin .form-table {
  border-collapse: collapse;
  margin-top: 20px;
  width: 100%;
}

.notion-wp-admin .form-table th {
  width: 200px;
  text-align: left;
  padding: 20px 10px 20px 0;
  vertical-align: top;
  font-weight: 600;
  color: var(--notion-gray-800);
}

.notion-wp-admin .form-table td {
  padding: 15px 10px;
  vertical-align: middle;
}

.notion-wp-admin input[type="text"],
.notion-wp-admin input[type="password"],
.notion-wp-admin select {
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--notion-gray-300);
  border-radius: var(--notion-border-radius);
  padding: 10px 15px;
  font-size: 14px;
  transition: var(--notion-transition);
}

.notion-wp-admin input[type="text"]:focus,
.notion-wp-admin input[type="password"]:focus,
.notion-wp-admin select:focus {
  border-color: var(--notion-primary);
  box-shadow: 0 0 0 1px var(--notion-primary);
  outline: none;
}

.notion-wp-admin input[type="checkbox"] {
  border: 1px solid var(--notion-gray-400);
  border-radius: 3px;
  width: 18px;
  height: 18px;
}

.notion-wp-admin input[type="checkbox"]:checked {
  background-color: var(--notion-primary);
  border-color: var(--notion-primary);
}

.notion-wp-admin .description {
  font-size: 13px;
  color: var(--notion-gray-600);
  margin-top: 8px;
  display: block;
}

/* 输入框与按钮组合样式 */
.input-with-button {
  display: flex;
  max-width: 400px;
}

.input-with-button input {
  flex-grow: 1;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.input-with-button .button {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  margin-left: -1px !important;
}

/* 按钮行样式 */
.notion-wp-button-row {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

/* 复选框标签样式 */
.checkbox-with-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox-with-label input[type="checkbox"] {
  margin: 0;
}

.checkbox-with-label span {
  font-weight: normal;
}

/* 密码显示/隐藏按钮 */
.show-hide-password {
  background: var(--notion-gray-100) !important;
  border: 1px solid var(--notion-gray-300) !important;
  border-radius: var(--notion-border-radius) !important;
  padding: 8px 12px !important;
  cursor: pointer;
  transition: var(--notion-transition);
  margin-left: 10px !important;
}

.show-hide-password:hover {
  background: var(--notion-gray-200) !important;
}

/* 警告样式 */
.notion-wp-warning {
  color: var(--notion-danger);
  font-weight: 500;
}

/* 帮助部分样式增强 */
.notion-wp-help-section {
  margin-top: 20px;
}

.notion-wp-help-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-top: 25px;
  margin-bottom: 15px;
  color: var(--notion-secondary);
}

.notion-wp-help-section p {
  margin: 0.5em 0 1em;
  line-height: 1.6;
}

.notion-wp-help-section ul,
.notion-wp-help-section ol {
  margin: 0.5em 0 1.5em 1.5em;
}

.notion-wp-help-section li {
  margin-bottom: 10px;
}

.notion-wp-help-section a {
  color: var(--notion-primary);
  text-decoration: none;
  transition: var(--notion-transition);
}

.notion-wp-help-section a:hover {
  color: var(--notion-primary-dark);
  text-decoration: underline;
}

/* 操作按钮栏样式 */
.notion-wp-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
  border-top: 1px solid var(--notion-gray-200);
  margin-top: 30px;
}

.notion-wp-actions-bar .left-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.notion-wp-actions-bar .left-actions .button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.notion-wp-actions-bar .left-actions .button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.notion-wp-actions-bar .button-primary {
  background: var(--notion-primary);
  border-color: var(--notion-primary);
  color: white;
  font-weight: 500;
  padding: 10px 24px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.notion-wp-actions-bar .button-primary:hover {
  background: var(--notion-primary-dark);
  border-color: var(--notion-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* 动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.stat-card:hover h3 {
  animation: pulse 1s ease-in-out;
}

/* 通用状态样式 */
.error {
  border-color: var(--notion-error) !important;
  box-shadow: 0 0 0 1px var(--notion-error) !important;
}

.highlight { 
  animation: highlight 1.5s ease-in-out; 
}

@keyframes highlight {
  0% { color: var(--notion-primary); }
  50% { color: var(--notion-accent); }
  100% { color: var(--notion-primary); }
}

.spin { 
  animation: spin 1.5s infinite linear; 
  display: inline-block; 
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通用按钮样式增强 */
.button {
    transition: var(--notion-transition);
    position: relative;
    overflow: hidden;
    transform: translateZ(0); /* 启用硬件加速 */
}

.button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease-out;
}

.button:hover:before {
    left: 100%;
}

/* 按钮焦点状态 - 提升可访问性 */
.button:focus {
    outline: 2px solid var(--notion-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

/* 按钮禁用状态 */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* 按钮加载状态 */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* 改进的spinner样式 */
.spinner.is-active {
    visibility: visible !important;
    opacity: 1 !important;
    width: 16px !important;
    height: 16px !important;
    margin: 0 !important;
    float: none !important;
    display: inline-block !important;
}

/* 复制按钮成功状态 */
.button.copied {
    background-color: var(--notion-success) !important;
    border-color: var(--notion-success) !important;
    color: white !important;
    transform: scale(1.05);
}

.button.copied:before {
    content: '✓ ';
}

/* 复制按钮动画 */
@keyframes copySuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.button.copied {
    animation: copySuccess 0.3s ease-in-out;
}

/* 同步进度指示器 */
.sync-progress {
    margin: 20px 0;
    padding: 15px;
    background: var(--notion-gray-50);
    border: 1px solid var(--notion-gray-200);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--notion-gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--notion-primary), var(--notion-secondary));
    border-radius: 4px;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: var(--notion-gray-700);
}

.current-step {
    font-weight: 500;
}

.progress-percentage {
    font-weight: 600;
    color: var(--notion-primary);
}

/* 同步按钮增强 */
.notion-wp-sync-btn {
    position: relative;
    overflow: hidden;
}

.notion-wp-sync-btn .button-text {
    transition: opacity 0.2s ease;
}

.notion-wp-sync-btn.loading .button-text {
    opacity: 0;
}

.notion-wp-sync-btn.success {
    background-color: var(--notion-success) !important;
    border-color: var(--notion-success) !important;
    color: white !important;
}

/* ==================== 简洁光泽进度条样式 ==================== */

/* 简洁进度容器 */
.notion-sync-progress-container {
    margin: 16px 0;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
    font-size: 14px;
}

/* 旧版本UI组件已完全移除 */

/* 简洁光泽动画进度条 */
.sync-main-progress {
    margin-bottom: 8px;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.progress-bar {
    flex: 1;
    height: 16px;
    background: #f3f4f6;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: #3b82f6;
    border-radius: 8px;
    width: 0%;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

/* 光泽动画效果 */
.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    100% {
        left: 150%;
    }
}

.progress-percentage {
    font-size: 14px;
    font-weight: 600;
    color: #3b82f6;
    min-width: 40px;
    text-align: right;
}

.progress-status {
    font-size: 14px;
    color: #6b7280;
    text-align: left;
}

.current-step-text {
    font-weight: 500;
    color: #374151;
}

/* 简洁进度条响应式设计 */
@media (max-width: 768px) {
    .progress-bar-container {
        flex-direction: column;
        gap: 8px;
    }

    .progress-percentage {
        text-align: center;
        min-width: auto;
    }

    .progress-status {
        text-align: center;
    }

}

@media (max-width: 480px) {
    .notion-sync-progress-container {
        margin: 8px 0;
    }
}

/* 简洁进度条动画优化 */
@media (prefers-reduced-motion: reduce) {
    .progress-fill::after {
        animation: none;
    }

    .progress-fill {
        transition-duration: 0.01ms !important;
    }
}

/* 简洁进度条高对比度模式 */
@media (prefers-contrast: high) {
    .progress-bar {
        border: 1px solid #6b7280;
    }
}

.notion-wp-sync-btn.error {
    background-color: var(--notion-error) !important;
    border-color: var(--notion-error) !important;
    color: white !important;
}

/* 表单验证反馈 */
.input-with-validation {
    position: relative;
}

.validation-feedback {
    margin-top: 5px;
    font-size: 13px;
    line-height: 1.4;
    padding: 8px 12px;
    border-radius: 4px;
    display: none;
    transition: all 0.3s ease;
}

.validation-feedback.success {
    display: block;
    background-color: rgba(46, 160, 67, 0.1);
    border: 1px solid var(--notion-success);
    color: var(--notion-success);
}

.validation-feedback.error {
    display: block;
    background-color: rgba(220, 38, 38, 0.1);
    border: 1px solid var(--notion-error);
    color: var(--notion-error);
}

.validation-feedback.warning {
    display: block;
    background-color: rgba(245, 158, 11, 0.1);
    border: 1px solid var(--notion-warning);
    color: var(--notion-warning);
}

.validation-feedback:before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    vertical-align: text-top;
    background-size: contain;
}

.validation-feedback.success:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232ea043' viewBox='0 0 16 16'%3E%3Cpath d='M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z'/%3E%3C/svg%3E");
}

.validation-feedback.error:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc2626' viewBox='0 0 16 16'%3E%3Cpath d='M8 0a8 8 0 100 16A8 8 0 008 0zM3.97 3.97a.75.75 0 011.06 0L8 6.94l2.97-2.97a.75.75 0 111.06 1.06L9.06 8l2.97 2.97a.75.75 0 11-1.06 1.06L8 9.06l-2.97 2.97a.75.75 0 01-1.06-1.06L6.94 8 3.97 5.03a.75.75 0 010-1.06z'/%3E%3C/svg%3E");
}

.validation-feedback.warning:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23f59e0b' viewBox='0 0 16 16'%3E%3Cpath d='M8.982 1.566a1.13 1.13 0 00-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 018 5zm0 8a1 1 0 100-2 1 1 0 000 2z'/%3E%3C/svg%3E");
}

/* 输入框验证状态 */
.notion-wp-validated-input.valid {
    border-color: var(--notion-success);
    box-shadow: 0 0 0 1px rgba(46, 160, 67, 0.2);
}

.notion-wp-validated-input.invalid {
    border-color: var(--notion-error);
    box-shadow: 0 0 0 1px rgba(220, 38, 38, 0.2);
}

.notion-wp-validated-input.warning {
    border-color: var(--notion-warning);
    box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.2);
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .notion-wp-layout {
    flex-direction: column;
  }
  
  .notion-wp-sidebar {
    width: 100%;
  }
  
  .notion-wp-content {
    width: 100%;
  }
  
  .notion-wp-menu {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
  }
  
  .notion-wp-menu-item {
    width: auto;
    border-left: none;
    border-bottom: 3px solid transparent;
    padding: 10px 15px;
  }
  
  .notion-wp-menu-item.active {
    border-left-color: transparent;
    border-bottom-color: var(--notion-primary);
  }
  
  .notion-wp-author-card {
    display: flex;
    text-align: left;
    align-items: center;
    gap: 20px;
  }
  
  .notion-wp-author-avatar {
    margin-bottom: 0;
  }
}

@media screen and (max-width: 782px) {
  .notion-wp-admin h1.wp-heading-inline {
    font-size: 22px;
  }
  
  .notion-wp-logo {
    font-size: 28px;
    height: 28px;
    width: 28px;
  }
  
  .notion-wp-admin .form-table th {
    width: 100%;
    display: block;
    padding-bottom: 0;
  }
  
  .notion-wp-admin .form-table td {
    width: 100%;
    display: block;
    padding-top: 8px;
  }
  
  .notion-wp-actions-bar {
    flex-direction: column;
    gap: 15px;
  }
  
  .notion-wp-actions-bar .left-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .notion-wp-actions-bar .p-submit {
    width: 100%;
  }
  
  .notion-wp-actions-bar .p-submit .button {
    width: 100%;
    justify-content: center;
  }
  
  .notion-wp-menu {
    flex-direction: column;
  }
  
  .notion-wp-menu-item {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .notion-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .notion-wp-author-card {
    flex-direction: column;
    text-align: center;
  }
}

/* 保姆级指南样式 */
.notion-wp-description {
    background-color: #f0f6fc;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border-left: 4px solid #2271b1;
}

.notion-wp-steps {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dcdcde;
}

.notion-wp-steps h4 {
    color: #2271b1;
    font-size: 16px;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f1;
}

.notion-wp-steps ol {
    margin-left: 20px;
}

.notion-wp-steps li {
    margin-bottom: 12px;
    line-height: 1.6;
}

.notion-wp-tip {
    background-color: #f6f7f7;
    border-left: 4px solid #72aee6;
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.notion-wp-troubleshoot {
    background-color: #fcf9e8;
    border-left: 4px solid #dba617;
    padding: 15px;
    margin-top: 25px;
    border-radius: 3px;
}

.notion-wp-troubleshoot h4 {
    color: #996600;
    margin-top: 0;
    margin-bottom: 10px;
}

.notion-wp-troubleshoot ul {
    margin-left: 20px;
}

.notion-wp-troubleshoot li {
    margin-bottom: 10px;
}

code {
    background: #f6f7f7;
    padding: 3px 5px;
    border-radius: 3px;
    font-family: monospace;
    color: #3c434a;
}

/* 日志文件列表 */
.notion-wp-log-files ul {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.notion-wp-log-files li {
    display: flex;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f9f9f9;
    align-items: center;
}

.notion-wp-log-files li:last-child {
    border-bottom: none;
}

.notion-wp-log-files li:nth-child(odd) {
    background-color: #fff;
}

.notion-wp-log-files .log-file-name {
    flex: 2;
    font-family: monospace;
    color: #333;
}

.notion-wp-log-files .log-file-size {
    flex: 1;
    text-align: center;
    color: #666;
}

.notion-wp-log-files .log-file-date {
    flex: 1;
    text-align: right;
    color: #666;
}

/* 添加 Webhook 和高级设置样式 */
.notion-wp-subsetting {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border-left: 4px solid #2271b1;
    border-radius: 4px;
}

.notion-wp-field {
    margin-bottom: 15px;
}

.notion-wp-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.input-with-button {
    display: flex;
    align-items: center;
}

.input-with-button input {
    flex-grow: 1;
    margin-right: 5px;
}

.copy-to-clipboard {
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkbox-with-label {
    display: flex;
    align-items: center;
}

.checkbox-with-label input[type="checkbox"] {
    margin-right: 8px;
}

/* 提示样式 */
.notion-wp-warning {
    color: #d63638;
    font-weight: 500;
}

@keyframes toastSlideIn {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 0.95; }
}

/* =============== About Author =============== */
.author-info{display:flex;gap:24px;margin-bottom:32px;padding:24px;background:var(--notion-gray-100);border-radius:var(--notion-border-radius);border:1px solid var(--notion-gray-200);} 
.author-avatar img{width:80px;height:80px;border-radius:50%;border:3px solid var(--notion-primary);object-fit:cover;} 
.author-details h3{margin:0 0 8px 0;font-size:20px;font-weight:600;color:var(--notion-gray-900);} 
.author-title{margin:0 0 12px 0;color:var(--notion-primary);font-weight:500;font-size:14px;} 
.author-description{margin:0 0 16px 0;color:var(--notion-gray-600);font-size:14px;line-height:1.6;} 
.author-links{display:flex;gap:16px;flex-wrap:wrap;} 
.author-link{display:flex;align-items:center;gap:6px;padding:8px 12px;background:#fff;border:1px solid var(--notion-gray-200);border-radius:var(--notion-border-radius);color:var(--notion-gray-700);text-decoration:none;font-size:13px;font-weight:500;transition:var(--notion-transition);} 
.author-link:hover{background:var(--notion-primary);color:#fff;border-color:var(--notion-primary);transform:translateY(-1px);} 
.link-icon{font-size:14px;} 
.plugin-info{margin-bottom:32px;padding:24px;background:var(--notion-gray-100);border-radius:var(--notion-border-radius);border:1px solid var(--notion-gray-200);} 
.plugin-info h4{margin:0 0 16px 0;font-size:16px;font-weight:600;color:var(--notion-gray-900);} 
.info-grid{display:grid;grid-template-columns:1fr 1fr;gap:12px;} 
.info-item{display:flex;gap:6px;font-size:13px;color:var(--notion-gray-700);}
.info-label{font-weight:600;color:var(--notion-gray-800);}

/* =============== Acknowledgments & References =============== */
.acknowledgments{margin-bottom:32px;padding:24px;background:var(--notion-gray-100);border-radius:var(--notion-border-radius);border:1px solid var(--notion-gray-200);}
.acknowledgments h4{margin:0 0 16px 0;font-size:16px;font-weight:600;color:var(--notion-gray-900);}
.acknowledgments p{margin:0 0 16px 0;color:var(--notion-gray-600);font-size:14px;line-height:1.6;}
.reference-projects{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:16px;margin:16px 0;}
.reference-item{padding:16px;background:#fff;border:1px solid var(--notion-gray-200);border-radius:var(--notion-border-radius);transition:var(--notion-transition);}
.reference-item:hover{border-color:var(--notion-primary);transform:translateY(-2px);box-shadow:0 4px 12px rgba(37,99,235,0.1);}
.reference-item a{display:block;font-size:15px;font-weight:600;color:var(--notion-primary);text-decoration:none;margin-bottom:8px;transition:var(--notion-transition);}
.reference-item a:hover{color:var(--notion-primary-dark);}
.reference-item p{margin:0;color:var(--notion-gray-600);font-size:13px;line-height:1.5;}
.acknowledgments-footer{margin:16px 0 0 0;padding:16px 0 0 0;border-top:1px solid var(--notion-gray-200);color:var(--notion-gray-500);font-size:13px;font-style:italic;text-align:center;}

/* =============== 同步选项样式 =============== */
.sync-options-group {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.sync-options-info {
    margin-top: 5px;
}

.sync-options-info .description {
    color: #666;
    line-height: 1.4;
    font-size: 12px;
}

.sync-options-group .button {
    position: relative;
}

.sync-options-group .button:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* =============== 平板设备适配 (481px-768px) =============== */
@media (max-width: 768px) and (min-width: 481px) {
    /* 同步按钮组优化 - 平板设备 */
    .sync-options-group {
        flex-direction: column;
        gap: 8px;
        margin-bottom: 8px;
    }

    .sync-options-group .button {
        width: 100%;
        justify-content: center;
        min-height: 44px; /* 符合触摸标准 */
        padding: 10px 16px;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.2;
        border-radius: 6px;
    }

    /* 按钮图标优化 - 平板设备 */
    .sync-options-group .button .dashicons {
        font-size: 16px;
        line-height: 16px;
        margin-right: 6px;
    }

    /* 说明文字优化 - 平板设备 */
    .sync-options-info {
        margin-top: 6px;
        padding: 0 2px;
    }

    .sync-options-info .description {
        font-size: 12px;
        line-height: 1.4;
        color: #666;
    }

    /* 操作按钮栏优化 - 平板设备 */
    .notion-wp-actions-bar {
        flex-direction: column;
        gap: 15px;
        padding: 18px 0;
    }

    .notion-wp-actions-bar .left-actions {
        width: 100%;
        flex-direction: column;
        gap: 0;
    }

    /* 保存按钮优化 - 平板设备 */
    .notion-wp-actions-bar .button-primary {
        width: 100%;
        min-height: 44px;
        padding: 10px 24px;
        font-size: 14px;
        justify-content: center;
    }

    /* 致谢与参考部分响应式调整 */
    .reference-projects {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .reference-item {
        padding: 12px;
    }
}

/* =============== 中小屏设备通用适配 (≤768px) =============== */
@media (max-width: 768px) {
    /* 同步按钮组基础适配 */
    .sync-options-group {
        flex-direction: column;
    }

    .sync-options-group .button {
        width: 100%;
        justify-content: center;
    }

    /* 致谢与参考部分响应式调整 */
    .reference-projects {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .reference-item {
        padding: 12px;
    }
}

/* =============== 小屏手机适配 (320px-480px) =============== */
@media (max-width: 480px) {
    /* 同步按钮组优化 - 小屏手机 */
    .sync-options-group {
        flex-direction: column;
        gap: 10px;
        margin-bottom: 10px;
    }

    .sync-options-group .button {
        width: 100%;
        justify-content: center;
        min-height: 48px; /* 超过iOS 44px标准，确保触摸友好 */
        padding: 12px 16px;
        font-size: 15px;
        font-weight: 500;
        line-height: 1.2;
        border-radius: 8px;
    }

    /* 按钮图标优化 */
    .sync-options-group .button .dashicons {
        font-size: 16px;
        line-height: 16px;
        margin-right: 6px;
    }

    /* 说明文字优化 - 小屏手机 */
    .sync-options-info {
        margin-top: 8px;
        padding: 0 4px; /* 避免文字贴边 */
    }

    .sync-options-info .description {
        font-size: 13px;
        line-height: 1.5;
        color: #666;
    }

    /* 操作按钮栏整体优化 - 小屏手机 */
    .notion-wp-actions-bar {
        flex-direction: column;
        gap: 12px;
        padding: 16px 0;
    }

    .notion-wp-actions-bar .left-actions {
        width: 100%;
        flex-direction: column;
        gap: 0;
    }

    /* 保存按钮优化 - 小屏手机 */
    .notion-wp-actions-bar .button-primary {
        width: 100%;
        min-height: 48px;
        padding: 12px 24px;
        font-size: 15px;
        justify-content: center;
    }
}

/* ==================== 数据库索引管理样式 ==================== */

/* 索引状态容器 */
.notion-wp-index-status {
    background: #fff;
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    padding: 20px;
    margin: 16px 0;
}

/* 索引状态网格 */
.index-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.index-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--notion-gray-100);
    border-radius: 6px;
    border-left: 4px solid var(--notion-primary);
}

.index-label {
    font-weight: 600;
    color: var(--notion-gray-700);
    font-size: 14px;
}

.index-status {
    font-weight: 500;
    font-size: 13px;
    padding: 4px 8px;
    border-radius: 4px;
}

.index-status.status-active {
    background: var(--notion-accent-light);
    color: var(--notion-accent);
}

.index-status.status-inactive {
    background: var(--notion-danger-light);
    color: var(--notion-danger);
}

.index-value {
    font-weight: 600;
    color: var(--notion-primary);
    font-size: 14px;
}

/* 索引建议 */
.index-suggestions {
    background: var(--notion-primary-light);
    border: 1px solid var(--notion-primary);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.index-suggestions h4 {
    margin: 0 0 12px 0;
    color: var(--notion-primary);
    font-size: 14px;
    font-weight: 600;
}

.index-suggestions ul {
    margin: 0;
    padding-left: 20px;
}

.index-suggestions li {
    color: var(--notion-gray-700);
    font-size: 13px;
    line-height: 1.5;
    margin-bottom: 6px;
}

/* 索引操作按钮 */
.notion-wp-index-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.notion-wp-index-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: var(--notion-transition);
}

.notion-wp-index-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.notion-wp-index-actions .button-primary {
    background: var(--notion-primary);
    border-color: var(--notion-primary);
}

.notion-wp-index-actions .button-primary:hover {
    background: var(--notion-primary-dark);
    border-color: var(--notion-primary-dark);
}

.notion-wp-index-actions .button-link-delete {
    color: var(--notion-danger);
    text-decoration: none;
}

.notion-wp-index-actions .button-link-delete:hover {
    color: #b91c1c;
}

/* 一键优化按钮特殊样式 - 统一尺寸 */
.notion-wp-optimize-all-btn {
    background: linear-gradient(135deg, var(--notion-primary) 0%, var(--notion-primary-dark) 100%) !important;
    border-color: var(--notion-primary-dark) !important;
    font-size: 13px !important;
    height: 32px !important;
    line-height: 30px !important;
    padding: 0 12px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(0, 115, 170, 0.2) !important;
    transition: all 0.3s ease !important;
    min-height: 32px !important;
}

.notion-wp-optimize-all-btn:hover {
    background: linear-gradient(135deg, var(--notion-primary-dark) 0%, #004a66 100%) !important;
    border-color: #004a66 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 115, 170, 0.3) !important;
}

/* 确保所有按钮统一尺寸 */
.notion-wp-index-actions .button {
    height: 32px !important;
    line-height: 30px !important;
    padding: 0 12px !important;
    font-size: 13px !important;
    min-height: 32px !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.notion-wp-optimize-all-btn .dashicons {
    margin-right: 8px !important;
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
}

/* 索引信息说明 */
.notion-wp-index-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
    margin-top: 20px;
}

.notion-wp-index-info h4 {
    margin: 0 0 12px 0;
    color: var(--notion-gray-800);
    font-size: 14px;
    font-weight: 600;
}

.notion-wp-index-info ul {
    margin: 0;
    padding-left: 20px;
}

.notion-wp-index-info li {
    color: var(--notion-gray-700);
    font-size: 13px;
    line-height: 1.6;
    margin-bottom: 8px;
}

.notion-wp-index-info li strong {
    color: var(--notion-gray-800);
    font-weight: 600;
}

/* 加载状态 */
.loading-placeholder {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 20px;
    color: var(--notion-gray-600);
    font-size: 14px;
}

.loading-placeholder .spinner {
    float: none;
    margin: 0;
}

/* 错误消息 */
.error-message {
    background: var(--notion-danger-light);
    color: var(--notion-danger);
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid var(--notion-danger);
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .index-status-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .notion-wp-index-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .notion-wp-index-actions .button {
        justify-content: center;
        width: 100%;
    }

    .index-status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* ==================== 异步处理管理样式 ==================== */

/* 异步状态容器 */
.notion-wp-async-status,
.notion-wp-queue-status {
    background: #fff;
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    padding: 20px;
    margin: 16px 0;
    min-height: 80px; /* 确保有最小高度 */
    display: block !important; /* 强制显示 */
    visibility: visible !important; /* 强制可见 */
}

/* 确保异步状态显示容器可见 */
.async-status-display,
.queue-status-display {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 错误消息样式 */
.error-message {
    color: #d63638;
    padding: 10px;
    background: #fef7f7;
    border: 1px solid #d63638;
    border-radius: 4px;
    margin: 10px 0;
}

/* 异步状态网格 */
.async-status-grid,
.queue-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
}

.async-status-item,
.queue-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background: var(--notion-gray-100);
    border-radius: 6px;
    border-left: 4px solid var(--notion-primary);
}

.status-label {
    font-weight: 600;
    color: var(--notion-gray-700);
    font-size: 13px;
}

.status-value {
    font-weight: 500;
    color: var(--notion-gray-800);
    font-size: 14px;
}

/* 状态颜色 */
.status-value.status-idle {
    color: var(--notion-gray-600);
}

.status-value.status-running {
    color: var(--notion-primary);
    animation: pulse 2s infinite;
}

.status-value.status-paused {
    color: #f59e0b;
}

.status-value.status-error {
    color: var(--notion-danger);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 异步操作按钮 */
.notion-wp-async-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin: 20px 0;
    flex-wrap: wrap;
}

.notion-wp-async-actions .button {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: var(--notion-transition);
    font-size: 13px;
    padding: 6px 12px;
}

.notion-wp-async-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.notion-wp-async-actions .button-secondary {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: var(--notion-gray-700);
}

.notion-wp-async-actions .button-secondary:hover {
    background: #e2e8f0;
    border-color: #94a3b8;
}

.notion-wp-async-actions .button-link-delete {
    color: var(--notion-danger);
    text-decoration: none;
    background: transparent;
    border: 1px solid var(--notion-danger);
}

.notion-wp-async-actions .button-link-delete:hover {
    color: #fff;
    background: var(--notion-danger);
}

/* 异步信息说明 */
.notion-wp-async-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
    margin-top: 20px;
}

.notion-wp-async-info h4 {
    margin: 0 0 12px 0;
    color: var(--notion-gray-800);
    font-size: 14px;
    font-weight: 600;
}

.notion-wp-async-info ul {
    margin: 0;
    padding-left: 20px;
}

.notion-wp-async-info li {
    color: var(--notion-gray-700);
    font-size: 13px;
    line-height: 1.6;
    margin-bottom: 8px;
}

.notion-wp-async-info li strong {
    color: var(--notion-gray-800);
    font-weight: 600;
}

/* 通用隐藏类 */
.notion-wp-hidden {
    display: none !important;
}

/* 同步操作区域样式 */
.notion-wp-sync-actions {
    margin-top: 20px;
}

.sync-buttons {
    margin-bottom: 15px;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* 智能推荐区域样式 */
.notion-wp-smart-recommendations {
    margin-top: 20px;
}

#recommendations-result {
    margin-top: 10px;
}

.notion-wp-apply-recommendations {
    margin-top: 10px;
}

/* 日志查看器样式 */
.notion-wp-log-viewer {
    width: 100% !important;
    max-height: 480px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    white-space: pre;
    overflow: auto;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    line-height: 1.4;
    font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .notion-wp-container {
        padding: 15px;
    }

    .notion-wp-menu-item {
        font-size: var(--notion-font-size-sm);
        padding: 10px 15px;
    }
}

@media (max-width: 768px) {
    .async-status-grid,
    .queue-status-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .notion-wp-async-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .notion-wp-async-actions .button {
        justify-content: center;
        width: 100%;
        min-height: 44px; /* 触摸友好的最小高度 */
    }

    .async-status-item,
    .queue-status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }

    .sync-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .sync-buttons .button {
        width: 100%;
        justify-content: center;
        min-height: 44px;
    }

    /* 表单优化 */
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }

    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }

    .form-table td {
        border-top: none;
        padding-top: 5px;
    }

    /* 输入框优化 */
    .regular-text,
    .large-text {
        width: 100% !important;
        max-width: none;
        font-size: 16px; /* 防止iOS缩放 */
    }

    /* 菜单优化 */
    .notion-wp-menu {
        flex-direction: column;
        gap: 5px;
    }

    .notion-wp-menu-item {
        width: 100%;
        text-align: left;
        justify-content: flex-start;
    }
}

@media (max-width: 480px) {
    .notion-wp-container {
        padding: 10px;
        margin: 10px 0;
    }

    .notion-wp-settings-section h2 {
        font-size: var(--notion-font-size-lg);
        margin-bottom: 15px;
    }

    .button {
        padding: 12px 16px;
        font-size: var(--notion-font-size-sm);
    }

    /* 卡片优化 */
    .stat-card {
        padding: 15px;
        margin-bottom: 15px;
    }

    .stat-card h3 {
        font-size: var(--notion-font-size-base);
    }

    .stat-card .stat-value {
        font-size: 1.5rem;
    }
}
