/*! For license information please see admin.d1d468e7.js.LICENSE.txt */
"use strict";(self.webpackChunknotion_to_wordpress=self.webpackChunknotion_to_wordpress||[]).push([[884],{2852:(e,t,n)=>{n.d(t,{s:()=>O});n(2675),n(9463),n(2259),n(5700),n(2008),n(3418),n(3792),n(4782),n(9572),n(2010),n(6033),n(2892),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(3362),n(825),n(7495),n(8781),n(7764),n(2762),n(3500),n(2953);var r=n(9223);n(8706),n(4423),n(2062),n(5506),n(1699),n(3296),n(7208),n(8408);function o(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function i(e){try{return new URL(e),!0}catch(e){return!1}}var a=n(404),s=n(6919);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return l(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function f(){}t=Object.getPrototypeOf;var h=[][r]?t(t([][r]())):(l(t={},r,function(){return this}),t),p=f.prototype=s.prototype=Object.create(h);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,l(e,o,"GeneratorFunction")),e.prototype=Object.create(p),e}return u.prototype=f,l(p,"constructor",f),l(f,"constructor",u),u.displayName="GeneratorFunction",l(f,o,"GeneratorFunction"),l(p),l(p,o,"Generator"),l(p,r,function(){return this}),l(p,"toString",function(){return"[object Generator]"}),(c=function(){return{w:i,m:d}})()}function l(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}l=function(e,t,n,r){function i(t,n){l(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},l(e,t,n,r)}function f(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function h(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){f(i,r,o,a,s,"next",e)}function s(e){f(i,r,o,a,s,"throw",e)}a(void 0)})}}function p(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return d(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,S(r.key),r)}}function m(e,t,n){return t=g(t),function(e,t){if(t&&("object"==u(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,b()?Reflect.construct(t,n||[],g(e).constructor):t.apply(e,n))}function b(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(b=function(){return!!e})()}function g(e){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},g(e)}function w(e,t){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},w(e,t)}function k(e,t,n){return(t=S(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}var O=function(e){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),k(n=m(this,t,[e]),"formOptions",void 0),k(n,"fields",new Map),k(n,"isSubmitting",!1),k(n,"autoSaveTimer",null),k(n,"throttledValidate",void 0),n.formOptions=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach(function(t){k(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({validateOnInput:!0,validateOnBlur:!0,submitOnEnter:!1,autoSave:!1,autoSaveDelay:2e3},e),n.throttledValidate=(0,a.nF)(n.validateField.bind(n),300),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&w(e,t)}(t,e),n=t,r=[{key:"onInit",value:function(){this.element&&((0,a.iQ)(this.element,"form-component"),this.discoverFields())}},{key:"onMount",value:function(){this.setupValidation()}},{key:"onUnmount",value:function(){this.clearAutoSaveTimer()}},{key:"onDestroy",value:function(){this.clearAutoSaveTimer(),this.fields.clear()}},{key:"onRender",value:function(){this.updateFieldStates()}},{key:"bindEvents",value:function(){var e=this;this.element&&(this.addEventListener(this.element,"submit",this.handleSubmit.bind(this)),this.fields.forEach(function(t,n){e.formOptions.validateOnInput&&e.addEventListener(t.element,"input",function(){return e.throttledValidate(t)}),e.formOptions.validateOnBlur&&e.addEventListener(t.element,"blur",function(){return e.validateField(t)}),e.formOptions.submitOnEnter&&e.addEventListener(t.element,"keydown",function(t){var n=t;"Enter"!==n.key||n.shiftKey||(n.preventDefault(),e.submit())}),e.formOptions.autoSave&&e.addEventListener(t.element,"input",function(){return e.scheduleAutoSave()})}))}},{key:"onStateChange",value:function(e,t,n){}},{key:"discoverFields",value:function(){var e=this;this.element&&this.element.querySelectorAll("input, select, textarea").forEach(function(t){var n=t,r=n.name||n.id;if(r){var o=e.parseValidationRules(n);e.fields.set(r,{name:r,element:n,rules:o,isValid:!0,errorMessage:""})}})}},{key:"parseValidationRules",value:function(e){var t=[],n=e.getAttribute("data-validation");return n&&n.split("|").forEach(function(e){switch(e){case"required":t.push({type:"required",message:"此字段为必填项"});break;case"email":t.push({type:"email",message:"请输入有效的邮箱地址"});break;case"url":t.push({type:"url",message:"请输入有效的URL地址"})}}),e.hasAttribute("required")&&t.push({type:"required",message:"此字段为必填项"}),"email"===e.getAttribute("type")&&t.push({type:"email",message:"请输入有效的邮箱地址"}),"url"===e.getAttribute("type")&&t.push({type:"url",message:"请输入有效的URL地址"}),t}},{key:"setupValidation",value:function(){var e=this;this.fields.forEach(function(t){e.createValidationFeedback(t)})}},{key:"createValidationFeedback",value:function(e){var t=e.element.closest(".form-field")||e.element.parentElement;if(t){var n=t.querySelector(".validation-feedback");n||((n=document.createElement("div")).className="validation-feedback",t.appendChild(n)),e.element.setAttribute("data-feedback-id",n.id||"feedback-".concat(e.name))}}},{key:"validateField",value:function(e){var t,n=e.element.value.trim(),r=!0,o="",i=p(e.rules);try{for(i.s();!(t=i.n()).done;){var a=t.value,s=this.applyValidationRule(n,a);if(!0!==s){r=!1,o="string"==typeof s?s:a.message||"验证失败";break}}}catch(e){i.e(e)}finally{i.f()}return e.isValid=r,e.errorMessage=o,this.updateFieldValidationState(e),r}},{key:"applyValidationRule",value:function(e,t){switch(t.type){case"required":return e.trim().length>0||t.message||"此字段为必填项";case"email":return!e||o(e)||t.message||"请输入有效的邮箱地址";case"url":return!e||i(e)||t.message||"请输入有效的URL地址";case"custom":return!t.validator||t.validator(e);default:return!0}}},{key:"updateFieldValidationState",value:function(e){var t=e.element,n=e.isValid,r=e.errorMessage;(0,a.vy)(t,"valid","invalid"),(0,a.iQ)(t,n?"valid":"invalid");var o=t.closest(".form-field")||t.parentElement,i=null==o?void 0:o.querySelector(".validation-feedback");i&&(i.textContent=r,(0,a.vy)(i,"success","error"),(0,a.iQ)(i,n?"success":"error"))}},{key:"updateFieldStates",value:function(){var e=this;this.fields.forEach(function(t){e.updateFieldValidationState(t)})}},{key:"validateForm",value:function(){var e=this,t=!0;return this.fields.forEach(function(n){e.validateField(n)||(t=!1)}),t}},{key:"handleSubmit",value:(b=h(c().m(function e(t){var n;return c().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t.preventDefault(),!this.isSubmitting){e.n=1;break}return e.a(2);case 1:if(this.validateForm()){e.n=2;break}return(0,s.Qg)("请修正表单中的错误"),e.a(2);case 2:return this.isSubmitting=!0,this.setSubmitButtonState(!0),e.p=3,e.n=4,this.submit();case 4:e.n=6;break;case 5:e.p=5,n=e.v,(0,s.Qg)("提交失败: ".concat(n.message));case 6:return e.p=6,this.isSubmitting=!1,this.setSubmitButtonState(!1),e.f(6);case 7:return e.a(2)}},e,this,[[3,5,6,7]])})),function(e){return b.apply(this,arguments)})},{key:"submit",value:(d=h(c().m(function e(){var t;return c().w(function(e){for(;;)switch(e.n){case 0:if(t=this.getFormData(),this.emit("form:submit",{formData:t,component:this}),!(this.element instanceof HTMLFormElement)){e.n=1;break}return e.n=1,this.submitViaAjax(t);case 1:return e.a(2)}},e,this)})),function(){return d.apply(this,arguments)})},{key:"submitViaAjax",value:(f=h(c().m(function e(t){var n,r,o,i,a,u,l;return c().w(function(e){for(;;)switch(e.n){case 0:return n=this.element,r=n.action||window.location.href,o=n.method||"POST",e.n=1,fetch(r,{method:o,body:t});case 1:return i=e.v,e.n=2,i.json();case 2:if(!(a=e.v).success){e.n=3;break}(0,s.Te)((null===(u=a.data)||void 0===u?void 0:u.message)||"保存成功"),this.emit("form:success",{result:a,component:this}),e.n=4;break;case 3:throw new Error((null===(l=a.data)||void 0===l?void 0:l.message)||"提交失败");case 4:return e.a(2)}},e,this)})),function(e){return f.apply(this,arguments)})},{key:"getFormData",value:function(){var e=new FormData;return this.fields.forEach(function(t){var n=t.name,r=t.element;r instanceof HTMLInputElement?"checkbox"===r.type||"radio"===r.type?r.checked&&e.append(n,r.value):"file"===r.type?r.files&&Array.from(r.files).forEach(function(t){e.append(n,t)}):e.append(n,r.value):e.append(n,r.value)}),e}},{key:"setSubmitButtonState",value:function(e){var t=this.$('button[type="submit"], input[type="submit"]');if(t)if(e){t.disabled=!0,(0,a.iQ)(t,"loading");var n=t.textContent||t.value;t.setAttribute("data-original-text",n),"BUTTON"===t.tagName?t.innerHTML='<span class="spinner is-active"></span> 保存中...':t.value="保存中..."}else{t.disabled=!1,(0,a.vy)(t,"loading");var r=t.getAttribute("data-original-text");r&&("BUTTON"===t.tagName?t.textContent=r:t.value=r)}}},{key:"scheduleAutoSave",value:function(){var e=this;this.clearAutoSaveTimer(),this.autoSaveTimer=setTimeout(function(){e.autoSave()},this.formOptions.autoSaveDelay)}},{key:"autoSave",value:(l=h(c().m(function e(){var t;return c().w(function(e){for(;;)switch(e.n){case 0:if(this.validateForm()){e.n=1;break}return e.a(2);case 1:try{t=this.getFormData(),this.emit("form:autosave",{formData:t,component:this})}catch(e){}case 2:return e.a(2)}},e,this)})),function(){return l.apply(this,arguments)})},{key:"clearAutoSaveTimer",value:function(){this.autoSaveTimer&&(clearTimeout(this.autoSaveTimer),this.autoSaveTimer=null)}},{key:"addValidationRule",value:function(e,t){var n=this.fields.get(e);n&&n.rules.push(t)}},{key:"removeValidationRule",value:function(e,t){var n=this.fields.get(e);n&&(n.rules=n.rules.filter(function(e){return e.type!==t}))}},{key:"getFieldValue",value:function(e){var t=this.fields.get(e);return t?t.element.value:""}},{key:"setFieldValue",value:function(e,t){var n=this.fields.get(e);n&&(n.element.value=t,this.validateField(n))}},{key:"reset",value:function(){var e=this;this.element instanceof HTMLFormElement&&this.element.reset(),this.fields.forEach(function(t){t.isValid=!0,t.errorMessage="",e.updateFieldValidationState(t)})}},{key:"isValid",value:function(){return Array.from(this.fields.values()).every(function(e){return e.isValid})}}],r&&v(n.prototype,r),u&&v(n,u),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,u,l,f,d,b}(r.$)},6653:(e,t,n)=>{n.d(t,{Lr:()=>y});n(2675),n(9463),n(2259),n(5700),n(2008),n(3418),n(3792),n(2062),n(4782),n(9572),n(2010),n(6033),n(2892),n(9432),n(6099),n(7495),n(8781),n(7764),n(1392),n(3500),n(2953);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||i(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){if(e){if("string"==typeof e)return a(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,t):void 0}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,f(r.key),r)}}function c(e,t,n){return t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t,n){return(t=f(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}var h=function(){return c(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"notion_wp_";s(this,e),l(this,"prefix",void 0),this.prefix=t},[{key:"set",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={value:t,timestamp:Date.now(),ttl:n.ttl},o=this.getFullKey(e,n.prefix);try{window.localStorage.setItem(o,JSON.stringify(r))}catch(e){}}},{key:"get",value:function(e,t,n){var r=this.getFullKey(e,n);try{var o=window.localStorage.getItem(r);if(!o)return t;var i=JSON.parse(o);return this.isExpired(i)?(this.remove(e,n),t):i.value}catch(e){return t}}},{key:"remove",value:function(e,t){var n=this.getFullKey(e,t);window.localStorage.removeItem(n)}},{key:"clear",value:function(e){var t=e||this.prefix;Object.keys(window.localStorage).forEach(function(e){e.startsWith(t)&&window.localStorage.removeItem(e)})}},{key:"has",value:function(e,t){var n=this.getFullKey(e,t),r=window.localStorage.getItem(n);if(!r)return!1;try{var o=JSON.parse(r);return!this.isExpired(o)}catch(e){return!1}}},{key:"keys",value:function(e){var t=e||this.prefix,n=Object.keys(window.localStorage);return n.filter(function(e){return e.startsWith(t)}).map(function(e){return e.substring(t.length)})}},{key:"getSize",value:function(){var e=0,t=window.localStorage;for(var n in t)n.startsWith(this.prefix)&&(e+=t[n].length+n.length);return e}},{key:"cleanup",value:function(){var e=this,t=0;return this.keys().forEach(function(n){var r=e.getRawItem(n);r&&e.isExpired(r)&&(e.remove(n),t++)}),t}},{key:"getFullKey",value:function(e,t){return(t||this.prefix)+e}},{key:"isExpired",value:function(e){return!!e.ttl&&Date.now()-e.timestamp>e.ttl}},{key:"getRawItem",value:function(e){try{var t=window.localStorage.getItem(this.getFullKey(e));return t?JSON.parse(t):null}catch(e){return null}}}])}(),p=function(){return c(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"notion_wp_session_";s(this,e),l(this,"prefix",void 0),this.prefix=t},[{key:"set",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={value:t,timestamp:Date.now(),ttl:n.ttl},o=this.getFullKey(e,n.prefix);try{window.sessionStorage.setItem(o,JSON.stringify(r))}catch(e){}}},{key:"get",value:function(e,t,n){var r=this.getFullKey(e,n);try{var o=window.sessionStorage.getItem(r);if(!o)return t;var i=JSON.parse(o);return this.isExpired(i)?(this.remove(e,n),t):i.value}catch(e){return t}}},{key:"remove",value:function(e,t){var n=this.getFullKey(e,t);window.sessionStorage.removeItem(n)}},{key:"clear",value:function(e){var t=e||this.prefix;Object.keys(window.sessionStorage).forEach(function(e){e.startsWith(t)&&window.sessionStorage.removeItem(e)})}},{key:"getFullKey",value:function(e,t){return(t||this.prefix)+e}},{key:"isExpired",value:function(e){return!!e.ttl&&Date.now()-e.timestamp>e.ttl}}])}(),d=function(){return c(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;s(this,e),l(this,"cache",new Map),l(this,"maxSize",void 0),this.maxSize=t},[{key:"set",value:function(e,t,n){if(this.cache.size>=this.maxSize){var r=this.cache.keys().next().value;r&&this.cache.delete(r)}this.cache.set(e,{value:t,timestamp:Date.now(),ttl:n})}},{key:"get",value:function(e,t){var n=this.cache.get(e);return n?this.isExpired(n)?(this.cache.delete(e),t):n.value:t}},{key:"has",value:function(e){var t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}},{key:"delete",value:function(e){return this.cache.delete(e)}},{key:"clear",value:function(){this.cache.clear()}},{key:"size",value:function(){return this.cache.size}},{key:"keys",value:function(){return Array.from(this.cache.keys())}},{key:"cleanup",value:function(){var e,t=0,n=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=i(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==n.return||n.return()}finally{if(u)throw a}}}}(this.cache.entries());try{for(n.s();!(e=n.n()).done;){var r=o(e.value,2),a=r[0],s=r[1];this.isExpired(s)&&(this.cache.delete(a),t++)}}catch(e){n.e(e)}finally{n.f()}return t}},{key:"isExpired",value:function(e){return!!e.ttl&&Date.now()-e.timestamp>e.ttl}}])}(),y=new h;new p,new d},6919:(e,t,n)=>{n.d(t,{I9:()=>y,Qg:()=>d,Te:()=>p,cf:()=>v});n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3418),n(3792),n(4782),n(9572),n(2010),n(6033),n(2892),n(9085),n(3851),n(1278),n(9432),n(6099),n(7495),n(8781),n(7764),n(3500),n(2953);function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){c(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function a(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return s(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw i}}}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function c(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){var t=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==r(t)?t:t+""}var f=function(){return e=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),c(this,"toasts",new Map),c(this,"container",null),c(this,"position","top-right"),c(this,"maxToasts",5),this.position=t.position||"top-right",this.maxToasts=t.maxToasts||5,this.createContainer()},t=[{key:"show",value:function(e){var t,n,r,o=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=this.generateId(),s={id:a,message:e,type:i.type||"info",duration:null!==(t=i.duration)&&void 0!==t?t:4e3,closable:null===(n=i.closable)||void 0===n||n,element:this.createElement(a,e,i),options:i};if(this.toasts.size>=this.maxToasts){var u=this.toasts.values().next().value;u&&this.remove(u.id)}return this.toasts.set(a,s),null===(r=this.container)||void 0===r||r.appendChild(s.element),s.duration>0&&(s.timer=setTimeout(function(){o.remove(a)},s.duration)),requestAnimationFrame(function(){s.element.classList.add("toast-enter")}),a}},{key:"remove",value:function(e){var t=this,n=this.toasts.get(e);n&&(n.timer&&clearTimeout(n.timer),n.element.classList.add("toast-exit"),setTimeout(function(){n.element.parentNode&&n.element.parentNode.removeChild(n.element),t.toasts.delete(e),n.options.onClose&&n.options.onClose()},300))}},{key:"clear",value:function(){var e,t=a(this.toasts.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;this.remove(n)}}catch(e){t.e(e)}finally{t.f()}}},{key:"success",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.show(e,i(i({},t),{},{type:"success"}))}},{key:"error",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.show(e,i(i({},n),{},{type:"error",duration:null!==(t=n.duration)&&void 0!==t?t:6e3}))}},{key:"warning",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.show(e,i(i({},t),{},{type:"warning"}))}},{key:"info",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.show(e,i(i({},t),{},{type:"info"}))}},{key:"setMaxToasts",value:function(e){this.maxToasts=e}},{key:"setPosition",value:function(e){this.position=e,this.container&&this.updateContainerPosition()}},{key:"createContainer",value:function(){this.container=document.createElement("div"),this.container.className="toast-container",this.updateContainerPosition(),document.body.appendChild(this.container),this.injectStyles()}},{key:"updateContainerPosition",value:function(){if(this.container){var e={"top-right":{top:"20px",right:"20px"},"top-left":{top:"20px",left:"20px"},"bottom-right":{bottom:"20px",right:"20px"},"bottom-left":{bottom:"20px",left:"20px"},"top-center":{top:"20px",left:"50%",transform:"translateX(-50%)"},"bottom-center":{bottom:"20px",left:"50%",transform:"translateX(-50%)"}}[this.position||"top-right"];Object.assign(this.container.style,i({position:"fixed",zIndex:"10000",pointerEvents:"none"},e))}}},{key:"createElement",value:function(e,t,n){var r=this,o=document.createElement("div");o.className="toast toast-".concat(n.type||"info"," ").concat(n.className||""),o.setAttribute("data-toast-id",e);var i=this.getIcon(n.type||"info"),a=!1!==n.closable?'<button class="toast-close" type="button">&times;</button>':"";if(o.innerHTML='\n      <div class="toast-content">\n        <div class="toast-icon">'.concat(i,'</div>\n        <div class="toast-message">').concat(t,"</div>\n        ").concat(a,"\n      </div>\n    "),!1!==n.closable){var s=o.querySelector(".toast-close");null==s||s.addEventListener("click",function(){return r.remove(e)})}n.onClick&&(o.addEventListener("click",n.onClick),o.style.cursor="pointer");var u=this.toasts.get(e);return u&&u.duration>0&&(o.addEventListener("mouseenter",function(){u.timer&&(clearTimeout(u.timer),u.timer=void 0)}),o.addEventListener("mouseleave",function(){u.timer=setTimeout(function(){r.remove(e)},1e3)})),o}},{key:"getIcon",value:function(e){var t={success:"✓",error:"✕",warning:"⚠",info:"ℹ"};return t[e]||t.info}},{key:"generateId",value:function(){return"toast_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}},{key:"injectStyles",value:function(){if(!document.querySelector("#toast-styles")){var e=document.createElement("style");e.id="toast-styles",e.textContent='\n      .toast-container {\n        display: flex;\n        flex-direction: column;\n        gap: 10px;\n        max-width: 400px;\n      }\n\n      .toast {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        pointer-events: auto;\n        transform: translateX(100%);\n        opacity: 0;\n        transition: all 0.3s ease;\n        border-left: 4px solid;\n      }\n\n      .toast.toast-enter {\n        transform: translateX(0);\n        opacity: 1;\n      }\n\n      .toast.toast-exit {\n        transform: translateX(100%);\n        opacity: 0;\n      }\n\n      .toast-success {\n        border-left-color: #10b981;\n      }\n\n      .toast-error {\n        border-left-color: #ef4444;\n      }\n\n      .toast-warning {\n        border-left-color: #f59e0b;\n      }\n\n      .toast-info {\n        border-left-color: #3b82f6;\n      }\n\n      .toast-content {\n        display: flex;\n        align-items: flex-start;\n        padding: 16px;\n        gap: 12px;\n      }\n\n      .toast-icon {\n        flex-shrink: 0;\n        width: 20px;\n        height: 20px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 12px;\n        font-weight: bold;\n        color: white;\n      }\n\n      .toast-success .toast-icon {\n        background: #10b981;\n      }\n\n      .toast-error .toast-icon {\n        background: #ef4444;\n      }\n\n      .toast-warning .toast-icon {\n        background: #f59e0b;\n      }\n\n      .toast-info .toast-icon {\n        background: #3b82f6;\n      }\n\n      .toast-message {\n        flex: 1;\n        font-size: 14px;\n        line-height: 1.5;\n        color: #374151;\n      }\n\n      .toast-close {\n        flex-shrink: 0;\n        background: none;\n        border: none;\n        font-size: 18px;\n        color: #9ca3af;\n        cursor: pointer;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .toast-close:hover {\n        background: #f3f4f6;\n        color: #374151;\n      }\n\n      /* 左侧位置的动画 */\n      .toast-container[style*="left"] .toast {\n        transform: translateX(-100%);\n      }\n\n      .toast-container[style*="left"] .toast.toast-enter {\n        transform: translateX(0);\n      }\n\n      .toast-container[style*="left"] .toast.toast-exit {\n        transform: translateX(-100%);\n      }\n    ',document.head.appendChild(e)}}}],t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}(),h=new f;function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return h.success(e,t)}function d(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return h.error(e,t)}function y(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return h.warning(e,t)}function v(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return h.info(e,t)}},7295:(e,t,n)=>{n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3418),n(3792),n(4782),n(9572),n(2010),n(2892),n(5506),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(3362),n(7495),n(8781),n(7764),n(3500),n(2953),n(3296),n(7208),n(8408);var r=n(404),o=n(3040),i=n(7518),a=n(7232),s=n(6919);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach(function(t){v(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function f(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return h(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(h(t={},r,function(){return this}),t),p=c.prototype=s.prototype=Object.create(l);function d(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,h(e,o,"GeneratorFunction")),e.prototype=Object.create(p),e}return u.prototype=c,h(p,"constructor",c),h(c,"constructor",u),u.displayName="GeneratorFunction",h(c,o,"GeneratorFunction"),h(p),h(p,o,"Generator"),h(p,r,function(){return this}),h(p,"toString",function(){return"[object Generator]"}),(f=function(){return{w:i,m:d}})()}function h(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}h=function(e,t,n,r){function i(t,n){h(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},h(e,t,n,r)}function p(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function d(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){p(i,r,o,a,s,"next",e)}function s(e){p(i,r,o,a,s,"throw",e)}a(void 0)})}}function y(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,m(r.key),r)}}function v(e,t,n){return(t=m(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){var t=function(e,t){if("object"!=u(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==u(t)?t:t+""}var b=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),v(this,"currentSyncId",null),v(this,"statusCheckTimer",null),v(this,"sseConnection",null),v(this,"checkInterval",5e3),v(this,"maxRetries",3),v(this,"retryCount",0),this.setupEventListeners(),this.setupVisibilityHandling()},t=[{key:"startSync",value:(m=d(f().m(function e(t){var n,r,o;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!this.isRunning()){e.n=1;break}return e.a(2,{success:!1,message:"已有同步任务在运行中"});case 1:return n=this.generateSyncId(),this.currentSyncId=n,i.Gi.startSync(t.syncType,n),e.p=2,e.n=3,(0,a.bE)("notion_to_wordpress_sync",{sync_type:t.syncType,sync_id:n,incremental:t.incremental||!1,check_deletions:t.checkDeletions||!1,batch_size:t.batchSize||10});case 3:if(!(r=e.v).data.success){e.n=4;break}return this.startStatusMonitoring(),this.setupSSEConnection(n),(0,s.cf)("".concat(t.syncType,"已开始")),e.a(2,{success:!0,message:"同步已开始",data:r.data.data});case 4:return this.handleSyncError(new Error(r.data.message||"同步启动失败")),e.a(2,{success:!1,message:r.data.message||"同步启动失败"});case 5:e.n=7;break;case 6:return e.p=6,o=e.v,this.handleSyncError(o),e.a(2,{success:!1,message:o.message||"同步请求失败"});case 7:return e.a(2)}},e,this,[[2,6]])})),function(e){return m.apply(this,arguments)})},{key:"stopSync",value:(p=d(f().m(function e(){var t,n;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return e.a(2,{success:!1,message:"没有正在运行的同步任务"});case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_stop_sync",{sync_id:this.currentSyncId});case 2:if(!(t=e.v).data.success){e.n=3;break}return this.cleanup(),(0,s.cf)("同步已停止"),e.a(2,{success:!0,message:"同步已停止"});case 3:return e.a(2,{success:!1,message:t.data.message||"停止同步失败"});case 4:e.n=6;break;case 5:return e.p=5,n=e.v,e.a(2,{success:!1,message:n.message||"停止同步请求失败"});case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return p.apply(this,arguments)})},{key:"pauseSync",value:(h=d(f().m(function e(){var t,n;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return e.a(2,{success:!1,message:"没有正在运行的同步任务"});case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_pause_sync",{sync_id:this.currentSyncId});case 2:if(!(t=e.v).data.success){e.n=3;break}return i.Gi.setState({sync:l(l({},i.Gi.getState().sync),{},{status:"paused"})}),(0,s.cf)("同步已暂停"),e.a(2,{success:!0,message:"同步已暂停"});case 3:return e.a(2,{success:!1,message:t.data.message||"暂停同步失败"});case 4:e.n=6;break;case 5:return e.p=5,n=e.v,e.a(2,{success:!1,message:n.message||"暂停同步请求失败"});case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return h.apply(this,arguments)})},{key:"resumeSync",value:(c=d(f().m(function e(){var t,n;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return e.a(2,{success:!1,message:"没有暂停的同步任务"});case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_resume_sync",{sync_id:this.currentSyncId});case 2:if(!(t=e.v).data.success){e.n=3;break}return i.Gi.setState({sync:l(l({},i.Gi.getState().sync),{},{status:"running"})}),(0,s.cf)("同步已恢复"),e.a(2,{success:!0,message:"同步已恢复"});case 3:return e.a(2,{success:!1,message:t.data.message||"恢复同步失败"});case 4:e.n=6;break;case 5:return e.p=5,n=e.v,e.a(2,{success:!1,message:n.message||"恢复同步请求失败"});case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return c.apply(this,arguments)})},{key:"getSyncStatus",value:(u=d(f().m(function e(){var t;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return e.a(2,null);case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_sync_status",{sync_id:this.currentSyncId});case 2:if(!(t=e.v).data.success){e.n=3;break}return e.a(2,t.data.data);case 3:return e.a(2,null);case 4:e.n=6;break;case 5:return e.p=5,e.v,e.a(2,null);case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return u.apply(this,arguments)})},{key:"isRunning",value:function(){var e=i.Gi.getState().sync;return"running"===e.status||"paused"===e.status}},{key:"getCurrentSyncId",value:function(){return this.currentSyncId}},{key:"setupEventListeners",value:function(){var e=this;i.Gi.subscribe(function(t,n){var r=t.sync,o=n.sync;"completed"!==r.status&&"error"!==r.status||"running"!==o.status&&"paused"!==o.status||setTimeout(function(){e.cleanup()},2e3)}),window.addEventListener("beforeunload",function(){e.cleanup()})}},{key:"setupVisibilityHandling",value:function(){var e=this;document.addEventListener("visibilitychange",function(){document.hidden?e.checkInterval=15e3:(e.checkInterval=5e3,e.currentSyncId&&e.checkSyncStatus()),e.statusCheckTimer&&e.restartStatusMonitoring()})}},{key:"startStatusMonitoring",value:function(){var e=this;this.stopStatusMonitoring(),this.statusCheckTimer=setInterval(function(){e.checkSyncStatus()},this.checkInterval)}},{key:"stopStatusMonitoring",value:function(){this.statusCheckTimer&&(clearInterval(this.statusCheckTimer),this.statusCheckTimer=null)}},{key:"restartStatusMonitoring",value:function(){this.stopStatusMonitoring(),this.startStatusMonitoring()}},{key:"checkSyncStatus",value:(r=d(f().m(function e(){var t;return f().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return this.stopStatusMonitoring(),e.a(2);case 1:return e.p=1,e.n=2,this.getSyncStatus();case 2:(t=e.v)?(i.Gi.setState({sync:t}),"completed"===t.status?this.handleSyncComplete():"error"===t.status&&this.handleSyncError(new Error("同步过程中发生错误")),this.retryCount=0):this.handleStatusCheckError(),e.n=4;break;case 3:e.p=3,e.v,this.handleStatusCheckError();case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return r.apply(this,arguments)})},{key:"handleStatusCheckError",value:function(){this.retryCount++,this.retryCount>=this.maxRetries&&(this.stopStatusMonitoring(),this.retryCount=0)}},{key:"setupSSEConnection",value:function(e){var t=this;if(window.EventSource)try{var n,r="".concat(window.ajaxurl,"?action=notion_to_wordpress_sync_sse&sync_id=").concat(e,"&nonce=").concat(null===(n=window.notionToWp)||void 0===n?void 0:n.nonce);this.sseConnection=new EventSource(r),this.sseConnection.onmessage=function(e){try{var n=JSON.parse(e.data);t.handleSSEMessage(n)}catch(e){}},this.sseConnection.onerror=function(e){t.closeSSEConnection()}}catch(e){}}},{key:"handleSSEMessage",value:function(e){switch(e.type){case"progress":i.Gi.updateSyncProgress(e.progress,e.total,e.current_item);break;case"complete":this.handleSyncComplete();break;case"error":this.handleSyncError(new Error(e.message))}}},{key:"closeSSEConnection",value:function(){this.sseConnection&&(this.sseConnection.close(),this.sseConnection=null)}},{key:"handleSyncComplete",value:function(){i.Gi.completeSync(),(0,s.Te)("同步完成"),o.Bt.emit("sync:complete",{syncId:this.currentSyncId})}},{key:"handleSyncError",value:function(e){var t={id:"error_".concat(Date.now()),message:e.message,code:"SYNC_ERROR",timestamp:Date.now()};i.Gi.syncError(t),(0,s.Qg)("同步失败: ".concat(e.message)),o.Bt.emit("sync:error",{error:t,syncId:this.currentSyncId})}},{key:"cleanup",value:function(){this.stopStatusMonitoring(),this.closeSSEConnection(),this.currentSyncId=null,this.retryCount=0,i.Gi.clearStoredState()}},{key:"generateSyncId",value:function(){return"sync_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))}}],t&&y(e.prototype,t),n&&y(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,u,c,h,p,m}());function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}function w(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return k(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(k(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,k(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,k(f,"constructor",c),k(c,"constructor",u),u.displayName="GeneratorFunction",k(c,o,"GeneratorFunction"),k(f),k(f,o,"Generator"),k(f,r,function(){return this}),k(f,"toString",function(){return"[object Generator]"}),(w=function(){return{w:i,m:h}})()}function k(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}k=function(e,t,n,r){function i(t,n){k(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},k(e,t,n,r)}function S(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function O(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){S(i,r,o,a,s,"next",e)}function s(e){S(i,r,o,a,s,"throw",e)}a(void 0)})}}function T(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,j(r.key),r)}}function E(e,t,n){return(t=j(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=g(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:t+""}var _=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),E(this,"statusCheckTimer",null),E(this,"checkInterval",1e4),E(this,"isMonitoring",!1),this.setupEventListeners(),this.setupVisibilityHandling()},t=[{key:"startMonitoring",value:function(){var e=this;this.isMonitoring||(this.isMonitoring=!0,this.checkQueueStatus(),this.statusCheckTimer=setInterval(function(){e.checkQueueStatus()},this.checkInterval))}},{key:"stopMonitoring",value:function(){this.isMonitoring&&(this.isMonitoring=!1,this.statusCheckTimer&&(clearInterval(this.statusCheckTimer),this.statusCheckTimer=null))}},{key:"getQueueStatus",value:(y=O(w().m(function e(){var t;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_queue_status",{});case 1:if(!(t=e.v).data.success){e.n=2;break}return e.a(2,t.data.data);case 2:return e.a(2,null);case 3:e.n=5;break;case 4:return e.p=4,e.v,e.a(2,null);case 5:return e.a(2)}},e,null,[[0,4]])})),function(){return y.apply(this,arguments)})},{key:"getQueueJobs",value:(d=O(w().m(function e(){var t,n,r,o=arguments;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=o.length>0&&void 0!==o[0]?o[0]:50,n=o.length>1&&void 0!==o[1]?o[1]:0,e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_queue_jobs",{limit:t,offset:n});case 2:if(!(r=e.v).data.success){e.n=3;break}return e.a(2,r.data.data);case 3:return e.a(2,[]);case 4:e.n=6;break;case 5:return e.p=5,e.v,e.a(2,[]);case 6:return e.a(2)}},e,null,[[1,5]])})),function(){return d.apply(this,arguments)})},{key:"pauseQueue",value:(p=O(w().m(function e(){var t,n;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_pause_queue",{});case 1:if(!(t=e.v).data.success){e.n=2;break}return(0,s.cf)("队列处理已暂停"),this.checkQueueStatus(),e.a(2,{success:!0,message:"队列处理已暂停"});case 2:return e.a(2,{success:!1,message:t.data.message||"暂停队列失败"});case 3:e.n=5;break;case 4:return e.p=4,n=e.v,e.a(2,{success:!1,message:n.message||"暂停队列请求失败"});case 5:return e.a(2)}},e,this,[[0,4]])})),function(){return p.apply(this,arguments)})},{key:"resumeQueue",value:(h=O(w().m(function e(){var t,n;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_resume_queue",{});case 1:if(!(t=e.v).data.success){e.n=2;break}return(0,s.cf)("队列处理已恢复"),this.checkQueueStatus(),e.a(2,{success:!0,message:"队列处理已恢复"});case 2:return e.a(2,{success:!1,message:t.data.message||"恢复队列失败"});case 3:e.n=5;break;case 4:return e.p=4,n=e.v,e.a(2,{success:!1,message:n.message||"恢复队列请求失败"});case 5:return e.a(2)}},e,this,[[0,4]])})),function(){return h.apply(this,arguments)})},{key:"cleanupQueue",value:(f=O(w().m(function e(){var t,n,r,o,i=arguments;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=i.length>0&&void 0!==i[0]?i[0]:{},e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_cleanup_queue",{remove_completed:t.removeCompleted||!1,remove_failed:t.removeFailed||!1,older_than:t.olderThan||24});case 2:if(!(n=e.v).data.success){e.n=3;break}return r="队列清理完成，清理了 ".concat(n.data.data.cleaned_count," 个任务"),(0,s.Te)(r),this.checkQueueStatus(),e.a(2,{success:!0,message:r,data:n.data.data});case 3:return e.a(2,{success:!1,message:n.data.message||"清理队列失败"});case 4:e.n=6;break;case 5:return e.p=5,o=e.v,e.a(2,{success:!1,message:o.message||"清理队列请求失败"});case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return f.apply(this,arguments)})},{key:"retryFailedJobs",value:(l=O(w().m(function e(t){var n,r,o;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_retry_failed_jobs",{job_ids:t});case 1:if(!(n=e.v).data.success){e.n=2;break}return r="已重试 ".concat(n.data.data.retried_count," 个失败任务"),(0,s.Te)(r),this.checkQueueStatus(),e.a(2,{success:!0,message:r,data:n.data.data});case 2:return e.a(2,{success:!1,message:n.data.message||"重试失败任务失败"});case 3:e.n=5;break;case 4:return e.p=4,o=e.v,e.a(2,{success:!1,message:o.message||"重试失败任务请求失败"});case 5:return e.a(2)}},e,this,[[0,4]])})),function(e){return l.apply(this,arguments)})},{key:"deleteJobs",value:(c=O(w().m(function e(t){var n,r,o;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t.length){e.n=1;break}return e.a(2,{success:!1,message:"没有指定要删除的任务"});case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_delete_queue_jobs",{job_ids:t});case 2:if(!(n=e.v).data.success){e.n=3;break}return r="已删除 ".concat(n.data.data.deleted_count," 个任务"),(0,s.Te)(r),this.checkQueueStatus(),e.a(2,{success:!0,message:r,data:n.data.data});case 3:return e.a(2,{success:!1,message:n.data.message||"删除任务失败"});case 4:e.n=6;break;case 5:return e.p=5,o=e.v,e.a(2,{success:!1,message:o.message||"删除任务请求失败"});case 6:return e.a(2)}},e,this,[[1,5]])})),function(e){return c.apply(this,arguments)})},{key:"getQueueStats",value:(u=O(w().m(function e(){var t;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_queue_stats",{});case 1:if(!(t=e.v).data.success){e.n=2;break}return e.a(2,t.data.data);case 2:return e.a(2,null);case 3:e.n=5;break;case 4:return e.p=4,e.v,e.a(2,null);case 5:return e.a(2)}},e,null,[[0,4]])})),function(){return u.apply(this,arguments)})},{key:"checkQueueStatus",value:(r=O(w().m(function e(){var t;return w().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.getQueueStatus();case 1:(t=e.v)&&(i.Gi.updateQueueStatus(t),o.Bt.emit("queue:status:update",t)),e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return r.apply(this,arguments)})},{key:"setupEventListeners",value:function(){var e=this;o.Bt.on("sync:start",function(){e.startMonitoring()}),o.Bt.on("sync:complete",function(){setTimeout(function(){e.checkQueueStatus()},2e3)}),window.addEventListener("beforeunload",function(){e.stopMonitoring()})}},{key:"setupVisibilityHandling",value:function(){var e=this;document.addEventListener("visibilitychange",function(){document.hidden?e.checkInterval=3e4:(e.checkInterval=1e4,e.isMonitoring&&e.checkQueueStatus()),e.statusCheckTimer&&e.isMonitoring&&e.restartMonitoring()})}},{key:"restartMonitoring",value:function(){this.isMonitoring&&(this.stopMonitoring(),this.startMonitoring())}},{key:"isMonitoringActive",value:function(){return this.isMonitoring}},{key:"setCheckInterval",value:function(e){this.checkInterval=Math.max(1e3,e),this.isMonitoring&&this.restartMonitoring()}}],t&&T(e.prototype,t),n&&T(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,u,c,l,f,h,p,d,y}());function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function I(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function C(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,A(r.key),r)}}function x(e,t,n){return(t=A(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e){var t=function(e,t){if("object"!=P(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=P(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==P(t)?t:t+""}var L=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),x(this,"container",null),x(this,"progressBar",null),x(this,"progressText",null),x(this,"currentItemText",null),x(this,"etaText",null),x(this,"closeButton",null),x(this,"isVisible",!1),x(this,"options",{}),x(this,"startTime",0),x(this,"progressHistory",[]),this.setupEventListeners(),this.injectStyles()},t=[{key:"show",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?I(Object(n),!0).forEach(function(t){x(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):I(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({title:"处理中...",showPercentage:!0,showETA:!0,showCurrentItem:!0,closable:!1,position:"top",theme:"default"},e),this.startTime=Date.now(),this.progressHistory=[],this.createProgressUI(),this.showContainer(),this.isVisible=!0,o.Bt.emit("progress:show",{options:this.options})}},{key:"hide",value:function(){this.isVisible&&(this.hideContainer(),this.isVisible=!1,o.Bt.emit("progress:hide"))}},{key:"update",value:function(e){if(this.isVisible&&this.container){var t=e.total>0?Math.round(e.progress/e.total*100):0;if(this.progressBar&&(this.progressBar.style.width="".concat(t,"%"),this.progressBar.setAttribute("aria-valuenow",t.toString())),this.progressText&&this.options.showPercentage){var n="".concat(t,"%");e.total>0&&(n+=" (".concat(e.progress,"/").concat(e.total,")")),e.message&&(n="".concat(e.message," - ").concat(n)),this.progressText.textContent=n}if(this.currentItemText&&this.options.showCurrentItem&&e.currentItem&&(this.currentItemText.textContent="正在处理: ".concat(e.currentItem)),this.etaText&&this.options.showETA){var r=this.calculateETA(e.progress,e.total);r>0&&(this.etaText.textContent="预计剩余: ".concat(this.formatTime(r)))}this.updateProgressHistory(e.progress),o.Bt.emit("progress:update",e)}}},{key:"setStatus",value:function(e,t){var n=this;this.container&&((0,r.vy)(this.container,"progress-running","progress-completed","progress-error","progress-paused"),(0,r.iQ)(this.container,"progress-".concat(e)),t&&this.progressText&&(this.progressText.textContent=t),"completed"!==e&&"error"!==e||setTimeout(function(){n.hide()},"completed"===e?2e3:5e3),o.Bt.emit("progress:status",{status:e,message:t}))}},{key:"isShowing",value:function(){return this.isVisible}},{key:"createProgressUI",value:function(){var e=this;this.removeContainer(),this.container=(0,r.n)("div",{class:"progress-container progress-".concat(this.options.position," progress-theme-").concat(this.options.theme),role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":"0"});var t=(0,r.n)("div",{class:"progress-content"});if(this.options.title){var n=(0,r.n)("div",{class:"progress-title"},this.options.title);t.appendChild(n)}var o=(0,r.n)("div",{class:"progress-bar-container"}),i=(0,r.n)("div",{class:"progress-track"});this.progressBar=(0,r.n)("div",{class:"progress-bar"}),i.appendChild(this.progressBar),o.appendChild(i),this.options.showPercentage&&(this.progressText=(0,r.n)("div",{class:"progress-text"},"0%"),o.appendChild(this.progressText)),t.appendChild(o),this.options.showCurrentItem&&(this.currentItemText=(0,r.n)("div",{class:"progress-current-item"}),t.appendChild(this.currentItemText)),this.options.showETA&&(this.etaText=(0,r.n)("div",{class:"progress-eta"}),t.appendChild(this.etaText)),this.options.closable&&(this.closeButton=(0,r.n)("button",{class:"progress-close",type:"button","aria-label":"关闭"},"×"),this.closeButton.addEventListener("click",function(){e.hide()}),t.appendChild(this.closeButton)),this.container.appendChild(t),document.body.appendChild(this.container)}},{key:"showContainer",value:function(){this.container&&((0,r.iQ)(this.container,"progress-show"),this.container.offsetHeight,(0,r.iQ)(this.container,"progress-visible"))}},{key:"hideContainer",value:function(){var e=this;this.container&&((0,r.vy)(this.container,"progress-visible"),setTimeout(function(){e.removeContainer()},300))}},{key:"removeContainer",value:function(){this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.container=null,this.progressBar=null,this.progressText=null,this.currentItemText=null,this.etaText=null,this.closeButton=null}},{key:"calculateETA",value:function(e,t){if(e<=0||t<=0||e>=t)return 0;var n=Date.now()-this.startTime;return n<1e3?0:(t-e)/(e/n)}},{key:"updateProgressHistory",value:function(e){var t=Date.now();this.progressHistory=this.progressHistory.filter(function(e){return t-e.timestamp<1e4}),this.progressHistory.push({progress:e,timestamp:t})}},{key:"formatTime",value:function(e){var t=Math.floor(e/1e3),n=Math.floor(t/60),r=Math.floor(n/60);return r>0?"".concat(r,"小时").concat(n%60,"分钟"):n>0?"".concat(n,"分钟").concat(t%60,"秒"):"".concat(t,"秒")}},{key:"setupEventListeners",value:function(){var e=this;i.Gi.subscribe(function(t,n){var r=t.sync,o=n.sync;"running"===r.status&&"running"!==o.status&&e.show({title:"".concat(r.sync_type,"进行中"),showPercentage:!0,showETA:!0,showCurrentItem:!0}),"running"===r.status&&e.isVisible&&e.update({progress:r.progress,total:r.total,currentItem:r.current_item}),"completed"===r.status&&"running"===o.status&&e.setStatus("completed","同步完成"),"error"===r.status&&"running"===o.status&&e.setStatus("error","同步失败"),"paused"===r.status&&"running"===o.status&&e.setStatus("paused","同步已暂停")}),document.addEventListener("keydown",function(t){"Escape"===t.key&&e.isVisible&&e.options.closable&&e.hide()})}},{key:"injectStyles",value:function(){if(!document.querySelector("#progress-manager-styles")){var e=(0,r.n)("style",{id:"progress-manager-styles"});e.textContent="\n      .progress-container {\n        position: fixed;\n        left: 50%;\n        transform: translateX(-50%);\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n        padding: 20px;\n        min-width: 400px;\n        max-width: 600px;\n        z-index: 10000;\n        opacity: 0;\n        transition: all 0.3s ease;\n      }\n\n      .progress-container.progress-top {\n        top: 20px;\n      }\n\n      .progress-container.progress-center {\n        top: 50%;\n        transform: translate(-50%, -50%);\n      }\n\n      .progress-container.progress-bottom {\n        bottom: 20px;\n      }\n\n      .progress-container.progress-visible {\n        opacity: 1;\n      }\n\n      .progress-title {\n        font-size: 16px;\n        font-weight: 600;\n        margin-bottom: 15px;\n        color: #374151;\n      }\n\n      .progress-bar-container {\n        position: relative;\n        margin-bottom: 10px;\n      }\n\n      .progress-track {\n        width: 100%;\n        height: 8px;\n        background: #e5e7eb;\n        border-radius: 4px;\n        overflow: hidden;\n      }\n\n      .progress-bar {\n        height: 100%;\n        background: linear-gradient(90deg, #3b82f6, #1d4ed8);\n        border-radius: 4px;\n        transition: width 0.3s ease;\n        position: relative;\n      }\n\n      .progress-bar::after {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);\n        animation: shimmer 2s infinite;\n      }\n\n      @keyframes shimmer {\n        0% { transform: translateX(-100%); }\n        100% { transform: translateX(100%); }\n      }\n\n      .progress-text {\n        text-align: center;\n        font-size: 14px;\n        color: #6b7280;\n        margin-top: 8px;\n      }\n\n      .progress-current-item {\n        font-size: 13px;\n        color: #9ca3af;\n        margin-bottom: 5px;\n        white-space: nowrap;\n        overflow: hidden;\n        text-overflow: ellipsis;\n      }\n\n      .progress-eta {\n        font-size: 13px;\n        color: #9ca3af;\n        text-align: right;\n      }\n\n      .progress-close {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n        background: none;\n        border: none;\n        font-size: 20px;\n        color: #9ca3af;\n        cursor: pointer;\n        width: 24px;\n        height: 24px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 50%;\n        transition: all 0.2s ease;\n      }\n\n      .progress-close:hover {\n        background: #f3f4f6;\n        color: #374151;\n      }\n\n      .progress-container.progress-completed .progress-bar {\n        background: linear-gradient(90deg, #10b981, #059669);\n      }\n\n      .progress-container.progress-error .progress-bar {\n        background: linear-gradient(90deg, #ef4444, #dc2626);\n      }\n\n      .progress-container.progress-paused .progress-bar {\n        background: linear-gradient(90deg, #f59e0b, #d97706);\n      }\n\n      @media (max-width: 640px) {\n        .progress-container {\n          left: 10px;\n          right: 10px;\n          transform: none;\n          min-width: auto;\n        }\n\n        .progress-container.progress-center {\n          transform: translateY(-50%);\n        }\n      }\n    ",document.head.appendChild(e)}}}],t&&C(e.prototype,t),n&&C(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}());n(2062),n(6033);function D(e){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},D(e)}function R(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return M(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(M(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,M(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,M(f,"constructor",c),M(c,"constructor",u),u.displayName="GeneratorFunction",M(c,o,"GeneratorFunction"),M(f),M(f,o,"Generator"),M(f,r,function(){return this}),M(f,"toString",function(){return"[object Generator]"}),(R=function(){return{w:i,m:h}})()}function M(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}M=function(e,t,n,r){function i(t,n){M(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},M(e,t,n,r)}function N(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function F(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){N(i,r,o,a,s,"next",e)}function s(e){N(i,r,o,a,s,"throw",e)}a(void 0)})}}function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach(function(t){U(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function V(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,q(r.key),r)}}function U(e,t,n){return(t=q(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e){var t=function(e,t){if("object"!=D(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=D(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==D(t)?t:t+""}var z=new(function(){return e=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),U(this,"modules",new Map),U(this,"intersectionObserver",null),U(this,"loadingPromises",new Map),U(this,"defaultOptions",{threshold:.1,rootMargin:"50px",timeout:1e4,retries:3}),this.defaultOptions=G(G({},this.defaultOptions),t),this.setupIntersectionObserver()},t=[{key:"setupIntersectionObserver",value:function(){var e=this;window.IntersectionObserver&&(this.intersectionObserver=new IntersectionObserver(function(t){t.forEach(function(t){if(t.isIntersecting){var n,r=t.target.getAttribute("data-lazy-module");r&&(e.loadModule(r),null===(n=e.intersectionObserver)||void 0===n||n.unobserve(t.target))}})},{threshold:this.defaultOptions.threshold,rootMargin:this.defaultOptions.rootMargin}))}},{key:"registerModule",value:function(e,t){this.modules.has(e)||this.modules.set(e,{id:e,loader:t,loaded:!1,loading:!1,retryCount:0})}},{key:"observe",value:function(e,t){this.intersectionObserver?(e.setAttribute("data-lazy-module",t),this.intersectionObserver.observe(e)):this.loadModule(t)}},{key:"unobserve",value:function(e){this.intersectionObserver&&this.intersectionObserver.unobserve(e)}},{key:"loadModule",value:(u=F(R().m(function e(t){var n,r,i,a;return R().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=this.modules.get(t)){e.n=1;break}throw new Error("Module ".concat(t," not registered"));case 1:if(!n.loaded){e.n=2;break}return e.a(2);case 2:if(!n.loading||!this.loadingPromises.has(t)){e.n=3;break}return e.a(2,this.loadingPromises.get(t));case 3:return n.loading=!0,r=this.performLoad(n),this.loadingPromises.set(t,r),e.p=4,e.n=5,r;case 5:return i=e.v,n.loaded=!0,n.loading=!1,n.error=void 0,o.Bt.emit("lazy:loaded",{moduleId:t,result:i}),e.a(2,i);case 6:throw e.p=6,a=e.v,n.loading=!1,n.error=a,n.retryCount++,o.Bt.emit("lazy:error",{moduleId:t,error:a,retryCount:n.retryCount}),a;case 7:return e.p=7,this.loadingPromises.delete(t),e.f(7);case 8:return e.a(2)}},e,this,[[4,6,7,8]])})),function(e){return u.apply(this,arguments)})},{key:"performLoad",value:(s=F(R().m(function e(t){var n=this;return R().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,r){var o=setTimeout(function(){r(new Error("Module ".concat(t.id," load timeout")))},n.defaultOptions.timeout);t.loader().then(function(t){clearTimeout(o),e(t)}).catch(function(e){clearTimeout(o),r(e)})}))},e)})),function(e){return s.apply(this,arguments)})},{key:"retryModule",value:(a=F(R().m(function e(t){var n;return R().w(function(e){for(;;)switch(e.n){case 0:if(n=this.modules.get(t)){e.n=1;break}throw new Error("Module ".concat(t," not registered"));case 1:if(!(n.retryCount>=this.defaultOptions.retries)){e.n=2;break}throw new Error("Module ".concat(t," exceeded max retries"));case 2:return n.loaded=!1,n.loading=!1,n.error=void 0,e.a(2,this.loadModule(t))}},e,this)})),function(e){return a.apply(this,arguments)})},{key:"preloadModule",value:(i=F(R().m(function e(t){return R().w(function(e){for(;;)if(0===e.n)return e.a(2,this.loadModule(t))},e,this)})),function(e){return i.apply(this,arguments)})},{key:"preloadModules",value:(r=F(R().m(function e(t){var n,r=this;return R().w(function(e){for(;;)if(0===e.n)return n=t.map(function(e){return r.loadModule(e).catch(function(e){return null})}),e.a(2,Promise.all(n))},e)})),function(e){return r.apply(this,arguments)})},{key:"getModuleStatus",value:function(e){return this.modules.get(e)}},{key:"getAllModuleStatus",value:function(){return Array.from(this.modules.values())}},{key:"isModuleLoaded",value:function(e){var t=this.modules.get(e);return!!t&&t.loaded}},{key:"isModuleLoading",value:function(e){var t=this.modules.get(e);return!!t&&t.loading}},{key:"unregisterModule",value:function(e){this.modules.delete(e),this.loadingPromises.delete(e)}},{key:"cleanup",value:function(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=null),this.modules.clear(),this.loadingPromises.clear()}},{key:"getStats",value:function(){var e=Array.from(this.modules.values());return{total:e.length,loaded:e.filter(function(e){return e.loaded}).length,loading:e.filter(function(e){return e.loading}).length,failed:e.filter(function(e){return e.error}).length}}}],t&&V(e.prototype,t),n&&V(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,i,a,s,u}());n(1415);function Q(e){return Q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Q(e)}function H(e){return function(e){if(Array.isArray(e))return W(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return W(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?W(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function K(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?K(Object(n),!0).forEach(function(t){te(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function J(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return X(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(X(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,X(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,X(f,"constructor",c),X(c,"constructor",u),u.displayName="GeneratorFunction",X(c,o,"GeneratorFunction"),X(f),X(f,o,"Generator"),X(f,r,function(){return this}),X(f,"toString",function(){return"[object Generator]"}),(J=function(){return{w:i,m:h}})()}function X(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}X=function(e,t,n,r){function i(t,n){X(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},X(e,t,n,r)}function Y(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Z(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Y(i,r,o,a,s,"next",e)}function s(e){Y(i,r,o,a,s,"throw",e)}a(void 0)})}}function ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ne(r.key),r)}}function te(e,t,n){return(t=ne(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e){var t=function(e,t){if("object"!=Q(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Q(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Q(t)?t:t+""}var re=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),te(this,"loadedResources",new Set),te(this,"loadingResources",new Map),te(this,"preloadSupported",void 0),te(this,"prefetchSupported",void 0),this.preloadSupported=this.checkPreloadSupport(),this.prefetchSupported=this.checkPrefetchSupport()},t=[{key:"checkPreloadSupport",value:function(){var e=document.createElement("link");return e.relList&&e.relList.supports&&e.relList.supports("preload")}},{key:"checkPrefetchSupport",value:function(){var e=document.createElement("link");return e.relList&&e.relList.supports&&e.relList.supports("prefetch")}},{key:"preload",value:(p=Z(J().m(function e(t){var n,r,i,a;return J().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=t.url,!this.loadedResources.has(n)){e.n=1;break}return e.a(2,{url:n,success:!0,loadTime:0});case 1:if(!this.loadingResources.has(n)){e.n=2;break}return e.a(2,this.loadingResources.get(n));case 2:return r=performance.now(),i=this.performPreload(t,r),this.loadingResources.set(n,i),e.p=3,e.n=4,i;case 4:return(a=e.v).success&&this.loadedResources.add(n),o.Bt.emit("resource:preloaded",a),e.a(2,a);case 5:return e.p=5,this.loadingResources.delete(n),e.f(5);case 6:return e.a(2)}},e,this,[[3,,5,6]])})),function(e){return p.apply(this,arguments)})},{key:"performPreload",value:(h=Z(J().m(function e(t,n){var r,o,i,a,s;return J().w(function(e){for(;;)switch(e.p=e.n){case 0:if(r=t.url,o=t.type,e.p=1,!this.preloadSupported||"script"!==o&&"style"!==o&&"font"!==o){e.n=3;break}return e.n=2,this.preloadWithLink(t,n);case 2:i=e.v,e.n=5;break;case 3:return e.n=4,this.preloadWithFetch(t,n);case 4:i=e.v;case 5:return e.a(2,i);case 6:return e.p=6,s=e.v,a=performance.now()-n,e.a(2,{url:r,success:!1,loadTime:a,error:s})}},e,this,[[1,6]])})),function(e,t){return h.apply(this,arguments)})},{key:"preloadWithLink",value:function(e,t){var n=this,r=e.url,o=e.type,i=e.priority,a=e.crossorigin,s=e.integrity;return new Promise(function(e){var u=document.createElement("link");u.rel="low"===i&&n.prefetchSupported?"prefetch":"preload",u.href=r,"script"===o?u.as="script":"style"===o?u.as="style":"font"===o&&(u.as="font",u.crossOrigin=a||"anonymous"),a&&(u.crossOrigin=a),s&&(u.integrity=s),u.onload=function(){var n=performance.now()-t;e({url:r,success:!0,loadTime:n}),document.head.removeChild(u)},u.onerror=function(n){var o=performance.now()-t;e({url:r,success:!1,loadTime:o,error:new Error("Failed to preload ".concat(r))}),document.head.removeChild(u)},document.head.appendChild(u)})}},{key:"preloadWithFetch",value:(f=Z(J().m(function e(t,n){var r,o,i,a,s,u,c,l;return J().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=t.url,o=t.crossorigin,i=t.integrity,e.p=1,a={},o&&(a.mode="anonymous"===o?"cors":"same-origin"),i&&(a.integrity=i),e.n=2,fetch(r,a);case 2:if((s=e.v).ok){e.n=3;break}throw new Error("HTTP ".concat(s.status,": ").concat(s.statusText));case 3:return e.n=4,s.blob();case 4:return u=performance.now()-n,e.a(2,{url:r,success:!0,loadTime:u});case 5:return e.p=5,l=e.v,c=performance.now()-n,e.a(2,{url:r,success:!1,loadTime:c,error:l})}},e,null,[[1,5]])})),function(e,t){return f.apply(this,arguments)})},{key:"preloadBatch",value:(l=Z(J().m(function e(t){var n,r=this;return J().w(function(e){for(;;)if(0===e.n)return n=t.map(function(e){return r.preload(e).catch(function(t){return{url:e.url,success:!1,loadTime:0,error:t}})}),e.a(2,Promise.all(n))},e)})),function(e){return l.apply(this,arguments)})},{key:"preloadScript",value:(c=Z(J().m(function e(t){var n,r=arguments;return J().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.preload($({url:t,type:"script"},n)))},e,this)})),function(e){return c.apply(this,arguments)})},{key:"preloadStyle",value:(u=Z(J().m(function e(t){var n,r=arguments;return J().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.preload($({url:t,type:"style"},n)))},e,this)})),function(e){return u.apply(this,arguments)})},{key:"preloadImage",value:(s=Z(J().m(function e(t){var n,r=arguments;return J().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.preload($({url:t,type:"image"},n)))},e,this)})),function(e){return s.apply(this,arguments)})},{key:"preloadFont",value:(a=Z(J().m(function e(t){var n,r=arguments;return J().w(function(e){for(;;)if(0===e.n)return n=r.length>1&&void 0!==r[1]?r[1]:{},e.a(2,this.preload($({url:t,type:"font",crossorigin:"anonymous"},n)))},e,this)})),function(e){return a.apply(this,arguments)})},{key:"smartPreload",value:(i=Z(J().m(function e(t){var n,r,o,i,a,s,u,c,l;return J().w(function(e){for(;;)switch(e.n){case 0:n=navigator.connection,r=navigator.deviceMemory,o=t,n&&("slow-2g"===(i=n.effectiveType)||"2g"===i?o=t.filter(function(e){return"high"===e.priority}):"3g"===i&&(o=t.slice(0,Math.ceil(.5*t.length)))),a=4,r&&r<4?a=2:r&&r>=8&&(a=6),s=[],u=0;case 1:if(!(u<o.length)){e.n=4;break}return c=o.slice(u,u+a),e.n=2,this.preloadBatch(c);case 2:l=e.v,s.push.apply(s,H(l));case 3:u+=a,e.n=1;break;case 4:return e.a(2,s)}},e,this)})),function(e){return i.apply(this,arguments)})},{key:"preloadCritical",value:(r=Z(J().m(function e(){var t;return J().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=[{url:"/wp-content/plugins/Notion-to-WordPress/assets/dist/css/admin.css",type:"style",priority:"high"},{url:"/wp-includes/css/dashicons.css",type:"style",priority:"high"}],e.p=1,e.n=2,this.preloadBatch(t);case 2:e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return r.apply(this,arguments)})},{key:"isResourceLoaded",value:function(e){return this.loadedResources.has(e)}},{key:"isResourceLoading",value:function(e){return this.loadingResources.has(e)}},{key:"getStats",value:function(){return{loaded:this.loadedResources.size,loading:this.loadingResources.size,total:this.loadedResources.size+this.loadingResources.size}}},{key:"cleanup",value:function(){this.loadedResources.clear(),this.loadingResources.clear()}}],t&&ee(e.prototype,t),n&&ee(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,i,a,s,u,c,l,f,h,p}());n(4423),n(1699),n(1761);function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function ie(e){return function(e){if(Array.isArray(e))return ae(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ae(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ae(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ae(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach(function(t){le(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ce(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,fe(r.key),r)}}function le(e,t,n){return(t=fe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fe(e){var t=function(e,t){if("object"!=oe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=oe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oe(t)?t:t+""}var he=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),le(this,"metrics",[]),le(this,"observers",new Map),le(this,"timers",new Map),le(this,"counters",new Map),le(this,"maxMetrics",1e3),le(this,"reportInterval",3e4),le(this,"reportTimer",null),this.setupPerformanceObservers(),this.startPeriodicReporting(),this.monitorPageLoad()},t=[{key:"setupPerformanceObservers",value:function(){var e=this;if(window.PerformanceObserver){try{var t=new PerformanceObserver(function(t){t.getEntries().forEach(function(t){if("navigation"===t.entryType){var n=t;e.recordNavigationMetrics(n)}})});t.observe({entryTypes:["navigation"]}),this.observers.set("navigation",t)}catch(e){}try{var n=new PerformanceObserver(function(t){t.getEntries().forEach(function(t){"resource"===t.entryType&&e.recordResourceMetric(t)})});n.observe({entryTypes:["resource"]}),this.observers.set("resource",n)}catch(e){}try{var r=new PerformanceObserver(function(t){t.getEntries().forEach(function(t){"longtask"===t.entryType&&e.recordMetric({name:"long_task_duration",value:t.duration,timestamp:Date.now(),type:"timing",tags:{type:"longtask"}})})});r.observe({entryTypes:["longtask"]}),this.observers.set("longtask",r)}catch(e){}try{var o=new PerformanceObserver(function(t){t.getEntries().forEach(function(t){"layout-shift"!==t.entryType||t.hadRecentInput||e.recordMetric({name:"cumulative_layout_shift",value:t.value,timestamp:Date.now(),type:"gauge",tags:{type:"cls"}})})});o.observe({entryTypes:["layout-shift"]}),this.observers.set("layout-shift",o)}catch(e){}}}},{key:"recordNavigationMetrics",value:function(e){var t=this;[{name:"dns_lookup_time",value:e.domainLookupEnd-e.domainLookupStart},{name:"tcp_connect_time",value:e.connectEnd-e.connectStart},{name:"request_time",value:e.responseStart-e.requestStart},{name:"response_time",value:e.responseEnd-e.responseStart},{name:"dom_parse_time",value:e.domContentLoadedEventStart-e.responseEnd},{name:"dom_ready_time",value:e.domContentLoadedEventEnd-e.fetchStart},{name:"load_complete_time",value:e.loadEventEnd-e.fetchStart},{name:"first_paint",value:e.responseEnd-e.fetchStart}].forEach(function(e){e.value>=0&&t.recordMetric(ue(ue({},e),{},{timestamp:Date.now(),type:"timing",tags:{category:"navigation"}}))})}},{key:"recordResourceMetric",value:function(e){var t=new URL(e.name),n=this.getResourceType(e);this.recordMetric({name:"resource_load_time",value:e.responseEnd-e.startTime,timestamp:Date.now(),type:"timing",tags:{category:"resource",type:n,domain:t.hostname}}),e.transferSize>0&&this.recordMetric({name:"resource_size",value:e.transferSize,timestamp:Date.now(),type:"gauge",tags:{category:"resource",type:n,domain:t.hostname}})}},{key:"getResourceType",value:function(e){var t=e.name.toLowerCase();return t.includes(".js")?"script":t.includes(".css")?"stylesheet":t.match(/\.(png|jpg|jpeg|gif|webp|svg)$/)?"image":t.match(/\.(woff|woff2|ttf|eot)$/)?"font":e.initiatorType?e.initiatorType:"other"}},{key:"monitorPageLoad",value:function(){var e=this;if("PerformanceObserver"in window)try{var t=new PerformanceObserver(function(t){t.getEntries().forEach(function(t){"first-contentful-paint"===t.name&&e.recordMetric({name:"first_contentful_paint",value:t.startTime,timestamp:Date.now(),type:"timing",tags:{category:"paint"}})})});t.observe({entryTypes:["paint"]}),this.observers.set("paint",t)}catch(e){}document.addEventListener("visibilitychange",function(){e.recordMetric({name:"page_visibility_change",value:document.hidden?0:1,timestamp:Date.now(),type:"counter",tags:{category:"user_interaction",state:document.hidden?"hidden":"visible"}})})}},{key:"recordMetric",value:function(e){this.metrics.push(e),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics)),o.Bt.emit("performance:metric",e)}},{key:"startTimer",value:function(e){this.timers.set(e,performance.now())}},{key:"endTimer",value:function(e,t){var n=this.timers.get(e);if(!n)return 0;var r=performance.now()-n;return this.timers.delete(e),this.recordMetric({name:e,value:r,timestamp:Date.now(),type:"timing",tags:t}),r}},{key:"incrementCounter",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,n=arguments.length>2?arguments[2]:void 0,r=(this.counters.get(e)||0)+t;this.counters.set(e,r),this.recordMetric({name:e,value:r,timestamp:Date.now(),type:"counter",tags:n})}},{key:"recordMemoryUsage",value:function(){if("memory"in performance){var e=performance.memory;this.recordMetric({name:"memory_used",value:e.usedJSHeapSize,timestamp:Date.now(),type:"gauge",tags:{category:"memory"}}),this.recordMetric({name:"memory_total",value:e.totalJSHeapSize,timestamp:Date.now(),type:"gauge",tags:{category:"memory"}}),this.recordMetric({name:"memory_limit",value:e.jsHeapSizeLimit,timestamp:Date.now(),type:"gauge",tags:{category:"memory"}})}}},{key:"generateReport",value:function(){var e=Date.now(),t={};return this.metrics.forEach(function(e){var n,r=(null===(n=e.tags)||void 0===n?void 0:n.category)||"other";t[r]=(t[r]||0)+1}),{metrics:ie(this.metrics),summary:{totalMetrics:this.metrics.length,timeRange:{start:this.metrics.length>0?Math.min.apply(Math,ie(this.metrics.map(function(e){return e.timestamp}))):e,end:e},categories:t}}}},{key:"startPeriodicReporting",value:function(){var e=this;this.reportTimer=setInterval(function(){var t=e.generateReport();o.Bt.emit("performance:report",t),e.recordMemoryUsage()},this.reportInterval)}},{key:"stopPeriodicReporting",value:function(){this.reportTimer&&(clearInterval(this.reportTimer),this.reportTimer=null)}},{key:"cleanup",value:function(){this.stopPeriodicReporting(),this.observers.forEach(function(e){e.disconnect()}),this.observers.clear(),this.metrics=[],this.timers.clear(),this.counters.clear()}},{key:"getMetricStats",value:function(e){var t=this.metrics.filter(function(t){return t.name===e});if(0===t.length)return null;var n=t.map(function(e){return e.value});return{count:t.length,min:Math.min.apply(Math,ie(n)),max:Math.max.apply(Math,ie(n)),avg:n.reduce(function(e,t){return e+t},0)/n.length,latest:n[n.length-1]}}}],t&&ce(e.prototype,t),n&&ce(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}());n(9868);function pe(e){return pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pe(e)}function de(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return ye(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(ye(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,ye(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,ye(f,"constructor",c),ye(c,"constructor",u),u.displayName="GeneratorFunction",ye(c,o,"GeneratorFunction"),ye(f),ye(f,o,"Generator"),ye(f,r,function(){return this}),ye(f,"toString",function(){return"[object Generator]"}),(de=function(){return{w:i,m:h}})()}function ye(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}ye=function(e,t,n,r){function i(t,n){ye(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},ye(e,t,n,r)}function ve(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function me(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ve(i,r,o,a,s,"next",e)}function s(e){ve(i,r,o,a,s,"throw",e)}a(void 0)})}}function be(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,we(r.key),r)}}function ge(e,t,n){return(t=we(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function we(e){var t=function(e,t){if("object"!=pe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=pe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pe(t)?t:t+""}var ke=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ge(this,"chunks",new Map),ge(this,"loadedChunks",new Set),ge(this,"chunkCache",new Map),this.registerBuiltinChunks()},t=[{key:"registerBuiltinChunks",value:function(){this.registerChunk({name:"admin-settings",path:function(){return n.e(783).then(n.bind(n,783))},priority:"medium"}),this.registerChunk({name:"admin-logs",path:function(){return Promise.all([n.e(96),n.e(33)]).then(n.bind(n,5033))},priority:"low"})}},{key:"registerChunk",value:function(e){var t={name:e.name,path:"string"==typeof e.path?e.path:"dynamic",priority:e.priority||"medium",dependencies:e.dependencies,size:e.size};this.chunks.set(e.name,t),"function"==typeof e.path&&z.registerModule(e.name,e.path)}},{key:"loadChunk",value:(l=me(de().m(function e(t){var n,r,o,i,a=arguments;return de().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=a.length>1&&void 0!==a[1]?a[1]:{},r=this.chunks.get(t)){e.n=1;break}throw new Error("Chunk ".concat(t," not registered"));case 1:if(!1===n.cache||!this.chunkCache.has(t)){e.n=2;break}return e.a(2,this.chunkCache.get(t));case 2:if(!this.loadedChunks.has(t)){e.n=3;break}return e.a(2,this.chunkCache.get(t));case 3:if(he.startTimer("chunk_load_".concat(t)),e.p=4,!r.dependencies){e.n=5;break}return e.n=5,this.loadDependencies(r.dependencies);case 5:if(!n.preload){e.n=6;break}return e.n=6,this.preloadChunkResources(r);case 6:return e.n=7,z.loadModule(t);case 7:return o=e.v,!1!==n.cache&&this.chunkCache.set(t,o),this.loadedChunks.add(t),he.endTimer("chunk_load_".concat(t),{chunk:t,priority:r.priority}),e.a(2,o);case 8:throw e.p=8,i=e.v,he.endTimer("chunk_load_".concat(t),{chunk:t,priority:r.priority,error:"true"}),i;case 9:return e.a(2)}},e,this,[[4,8]])})),function(e){return l.apply(this,arguments)})},{key:"loadDependencies",value:(c=me(de().m(function e(t){var n,r=this;return de().w(function(e){for(;;)switch(e.n){case 0:return n=t.map(function(e){return r.loadChunk(e).catch(function(e){return null})}),e.n=1,Promise.all(n);case 1:return e.a(2)}},e)})),function(e){return c.apply(this,arguments)})},{key:"preloadChunkResources",value:(u=me(de().m(function e(t){return de().w(function(e){for(;;)switch(e.p=e.n){case 0:if("dynamic"!==t.path){e.n=1;break}return e.a(2);case 1:return e.p=1,e.n=2,re.preloadScript(t.path,{priority:"high"===t.priority?"high":"low"});case 2:e.n=4;break;case 3:e.p=3,e.v;case 4:return e.a(2)}},e,null,[[1,3]])})),function(e){return u.apply(this,arguments)})},{key:"loadChunks",value:(s=me(de().m(function e(t){var n,r,o=this,i=arguments;return de().w(function(e){for(;;)if(0===e.n)return n=i.length>1&&void 0!==i[1]?i[1]:{},r=t.map(function(e){return o.loadChunk(e,n).catch(function(e){return null})}),e.a(2,Promise.all(r))},e)})),function(e){return s.apply(this,arguments)})},{key:"preloadHighPriorityChunks",value:(a=me(de().m(function e(){var t;return de().w(function(e){for(;;)switch(e.n){case 0:if(!((t=Array.from(this.chunks.values()).filter(function(e){return"high"===e.priority}).map(function(e){return e.name})).length>0)){e.n=1;break}return e.n=1,this.loadChunks(t,{preload:!0});case 1:return e.a(2)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"smartPreload",value:(i=me(de().m(function e(){var t,n,r=this;return de().w(function(e){for(;;)switch(e.n){case 0:t=this.getCurrentPageType(),(n=this.predictChunksForPage(t)).length>0&&setTimeout(function(){r.loadChunks(n,{preload:!0,cache:!0}).catch(console.error)},1e3);case 1:return e.a(2)}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getCurrentPageType",value:function(){var e=window.location.href;return e.includes("wp-admin")?e.includes("notion-to-wordpress")?"admin-plugin":"admin":"frontend"}},{key:"predictChunksForPage",value:function(e){return{"admin-plugin":["admin-settings","admin-logs"],admin:[],frontend:["frontend-comments","frontend-search"]}[e]||[]}},{key:"loadOnDemand",value:(o=me(de().m(function e(t,n){return de().w(function(e){for(;;)if(0===e.n)return z.observe(n,t),e.a(2,z.loadModule(t))},e)})),function(e,t){return o.apply(this,arguments)})},{key:"getChunkInfo",value:function(e){return this.chunks.get(e)}},{key:"getAllChunks",value:function(){return Array.from(this.chunks.values())}},{key:"isChunkLoaded",value:function(e){return this.loadedChunks.has(e)}},{key:"getLoadStats",value:function(){var e=Array.from(this.chunks.values()),t={};return e.forEach(function(e){t[e.priority]=(t[e.priority]||0)+1}),{total:e.length,loaded:this.loadedChunks.size,cached:this.chunkCache.size,byPriority:t}}},{key:"clearCache",value:function(){this.chunkCache.clear()}},{key:"unloadChunk",value:function(e){this.loadedChunks.delete(e),this.chunkCache.delete(e),z.unregisterModule(e)}},{key:"cleanup",value:function(){this.clearCache(),this.loadedChunks.clear(),this.chunks.clear()}}],t&&be(e.prototype,t),r&&be(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,r,o,i,a,s,u,c,l}()),Se=n(9223);n(825),n(2762);function Oe(e){return Oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Oe(e)}function Te(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Ee(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Ee(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Ee(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Ee(f,"constructor",c),Ee(c,"constructor",u),u.displayName="GeneratorFunction",Ee(c,o,"GeneratorFunction"),Ee(f),Ee(f,o,"Generator"),Ee(f,r,function(){return this}),Ee(f,"toString",function(){return"[object Generator]"}),(Te=function(){return{w:i,m:h}})()}function Ee(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Ee=function(e,t,n,r){function i(t,n){Ee(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Ee(e,t,n,r)}function je(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function _e(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){je(i,r,o,a,s,"next",e)}function s(e){je(i,r,o,a,s,"throw",e)}a(void 0)})}}function Pe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,De(r.key),r)}}function Ie(e,t,n){return t=xe(t),function(e,t){if(t&&("object"==Oe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Ce()?Reflect.construct(t,n||[],xe(e).constructor):t.apply(e,n))}function Ce(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Ce=function(){return!!e})()}function xe(e){return xe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xe(e)}function Ae(e,t){return Ae=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ae(e,t)}function Le(e,t,n){return(t=De(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function De(e){var t=function(e,t){if("object"!=Oe(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Oe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Oe(t)?t:t+""}var Re=function(e){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Le(n=Ie(this,t,[e]),"syncOptions",void 0),Le(n,"originalText",""),Le(n,"isProcessing",!1),n.syncOptions=e,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ae(e,t)}(t,e),n=t,o=[{key:"onInit",value:function(){this.element&&(this.originalText=this.element.textContent||"",this.element.setAttribute("data-sync-type",this.syncOptions.syncType),(0,r.iQ)(this.element,"sync-button","sync-button-".concat(this.syncOptions.syncType)))}},{key:"onMount",value:function(){this.updateButtonState()}},{key:"onUnmount",value:function(){this.isProcessing=!1}},{key:"onDestroy",value:function(){}},{key:"onRender",value:function(){this.updateButtonState()}},{key:"bindEvents",value:function(){this.element&&this.addEventListener(this.element,"click",this.handleClick.bind(this))}},{key:"onStateChange",value:function(e,t,n){e.sync!==t.sync&&this.updateButtonState()}},{key:"handleClick",value:(f=_e(Te().m(function e(t){var n;return Te().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t.preventDefault(),!this.isProcessing&&this.element){e.n=1;break}return e.a(2);case 1:if(!this.syncOptions.confirmMessage){e.n=2;break}if(confirm(this.syncOptions.confirmMessage)){e.n=2;break}return e.a(2);case 2:if("test"===this.syncOptions.syncType||this.validateConfiguration()){e.n=3;break}return e.a(2);case 3:return this.isProcessing=!0,this.setLoadingState(!0),e.p=4,e.n=5,this.performSync();case 5:e.n=7;break;case 6:e.p=6,n=e.v,(0,s.Qg)("同步失败: ".concat(n.message));case 7:return e.p=7,this.isProcessing=!1,this.setLoadingState(!1),e.f(7);case 8:return e.a(2)}},e,this,[[4,6,7,8]])})),function(e){return f.apply(this,arguments)})},{key:"performSync",value:(l=_e(Te().m(function e(){var t,n;return Te().w(function(e){for(;;)switch(e.n){case 0:t=this.getSyncTypeName(),n=this.syncOptions.syncType,e.n="smart"===n?1:"full"===n?3:"test"===n?5:7;break;case 1:return e.n=2,this.performSmartSync(t);case 2:case 4:case 6:return e.a(3,8);case 3:return e.n=4,this.performFullSync(t);case 5:return e.n=6,this.performTestConnection();case 7:throw new Error("未知的同步类型: ".concat(this.syncOptions.syncType));case 8:return e.a(2)}},e,this)})),function(){return l.apply(this,arguments)})},{key:"performSmartSync",value:(c=_e(Te().m(function e(t){var n;return Te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,b.startSync({syncType:t,incremental:!0,checkDeletions:this.syncOptions.checkDeletions||!0,batchSize:10});case 1:if(!(n=e.v).success){e.n=2;break}(0,s.cf)("".concat(t,"已开始")),e.n=3;break;case 2:throw new Error(n.message);case 3:return e.a(2)}},e,this)})),function(e){return c.apply(this,arguments)})},{key:"performFullSync",value:(u=_e(Te().m(function e(t){var n;return Te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,b.startSync({syncType:t,incremental:!1,checkDeletions:this.syncOptions.checkDeletions||!0,batchSize:10});case 1:if(!(n=e.v).success){e.n=2;break}(0,s.cf)("".concat(t,"已开始")),e.n=3;break;case 2:throw new Error(n.message);case 3:return e.a(2)}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"performTestConnection",value:(a=_e(Te().m(function e(){var t,n,r;return Te().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,fetch(window.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"notion_to_wordpress_test_connection",nonce:(null===(t=window.notionToWp)||void 0===t?void 0:t.nonce)||"",api_key:this.getConfigValue("notion_to_wordpress_api_key"),database_id:this.getConfigValue("notion_to_wordpress_database_id")})});case 1:return n=e.v,e.n=2,n.json();case 2:if(!(r=e.v).success){e.n=3;break}(0,s.Te)(r.data.message||"连接测试成功"),e.n=4;break;case 3:throw new Error(r.data.message||"连接测试失败");case 4:return e.a(2)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"validateConfiguration",value:function(){var e=this.getConfigValue("notion_to_wordpress_api_key"),t=this.getConfigValue("notion_to_wordpress_database_id");return!(!e||!t)||((0,s.Qg)("请先配置API密钥和数据库ID"),e||this.highlightField("notion_to_wordpress_api_key"),t||this.highlightField("notion_to_wordpress_database_id"),!1)}},{key:"getConfigValue",value:function(e){var t=document.querySelector("#".concat(e));return t?t.value.trim():""}},{key:"highlightField",value:function(e){var t=document.querySelector("#".concat(e));t&&((0,r.iQ)(t,"error"),(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement)&&t.focus(),setTimeout(function(){(0,r.vy)(t,"error")},2e3))}},{key:"getSyncTypeName",value:function(){return{smart:"智能同步",full:"完全同步",test:"测试连接"}[this.syncOptions.syncType]||"同步"}},{key:"setLoadingState",value:function(e){if(this.element)if(e){this.element.setAttribute("disabled","true"),(0,r.iQ)(this.element,"loading");var t=this.getSyncTypeName();this.element.innerHTML="".concat('<span class="spinner is-active"></span> ').concat(t,"中...")}else this.element.removeAttribute("disabled"),(0,r.vy)(this.element,"loading"),this.element.innerHTML=this.originalText}},{key:"updateButtonState",value:function(){if(this.element){var e=this.getState().sync;"running"===e.status?this.isProcessing||(this.element.setAttribute("disabled","true"),(0,r.iQ)(this.element,"sync-running")):this.isProcessing||(this.element.removeAttribute("disabled"),(0,r.vy)(this.element,"sync-running")),this.updateButtonStyle(e.status)}}},{key:"updateButtonStyle",value:function(e){this.element&&((0,r.vy)(this.element,"status-idle","status-running","status-completed","status-error","status-paused"),(0,r.iQ)(this.element,"status-".concat(e)))}},{key:"setText",value:function(e){this.element&&!this.isProcessing&&(this.element.textContent=e,this.originalText=e)}},{key:"getText",value:function(){return this.element&&this.element.textContent||""}},{key:"enable",value:function(){this.element&&!this.isProcessing&&(this.element.removeAttribute("disabled"),(0,r.vy)(this.element,"disabled"))}},{key:"disable",value:function(){this.element&&(this.element.setAttribute("disabled","true"),(0,r.iQ)(this.element,"disabled"))}},{key:"isLoading",value:function(){return this.isProcessing}}],o&&Pe(n.prototype,o),i&&Pe(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i,a,u,c,l,f}(Se.$),Me=(n(5440),n(8055));function Ne(e){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ne(e)}function Fe(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Be(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Be(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Be(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Be(f,"constructor",c),Be(c,"constructor",u),u.displayName="GeneratorFunction",Be(c,o,"GeneratorFunction"),Be(f),Be(f,o,"Generator"),Be(f,r,function(){return this}),Be(f,"toString",function(){return"[object Generator]"}),(Fe=function(){return{w:i,m:h}})()}function Be(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Be=function(e,t,n,r){function i(t,n){Be(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Be(e,t,n,r)}function Ge(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Ve(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Ge(i,r,o,a,s,"next",e)}function s(e){Ge(i,r,o,a,s,"throw",e)}a(void 0)})}}function Ue(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function qe(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,$e(r.key),r)}}function ze(e,t,n){return t=He(t),function(e,t){if(t&&("object"==Ne(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Qe()?Reflect.construct(t,n||[],He(e).constructor):t.apply(e,n))}function Qe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Qe=function(){return!!e})()}function He(e){return He=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},He(e)}function We(e,t){return We=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},We(e,t)}function Ke(e,t,n){return(t=$e(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $e(e){var t=function(e,t){if("object"!=Ne(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ne(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ne(t)?t:t+""}var Je=function(e){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Ke(n=ze(this,t,[e]),"statusOptions",void 0),Ke(n,"refreshTimer",null),Ke(n,"statusData",null),n.statusOptions=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ue(Object(n),!0).forEach(function(t){Ke(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ue(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({refreshInterval:5e3,autoRefresh:!0},e),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&We(e,t)}(t,e),n=t,o=[{key:"onInit",value:function(){this.element&&(0,r.iQ)(this.element,"status-display","status-display-".concat(this.statusOptions.type))}},{key:"onMount",value:function(){this.render(),this.statusOptions.autoRefresh&&this.startAutoRefresh()}},{key:"onUnmount",value:function(){this.stopAutoRefresh()}},{key:"onDestroy",value:function(){this.stopAutoRefresh()}},{key:"onRender",value:function(){this.updateDisplay()}},{key:"bindEvents",value:function(){var e=this.$(".status-refresh-button");e&&this.addEventListener(e,"click",this.handleRefresh.bind(this))}},{key:"onStateChange",value:function(e,t,n){var r=!1;switch(this.statusOptions.type){case"sync":e.sync!==t.sync&&(this.statusData=e.sync,r=!0);break;case"queue":e.queue!==t.queue&&(this.statusData=e.queue,r=!0)}r&&this.render()}},{key:"updateDisplay",value:function(){this.element&&this.statusData&&(this.updateStatusIndicator(),this.updateDetails(),this.updateProgress(),this.updateTimeInfo())}},{key:"updateStatusIndicator",value:function(){var e=this.$(".status-indicator");if(e&&this.statusData){var t=this.statusData.status;(0,r.vy)(e,"status-idle","status-running","status-paused","status-completed","status-error"),(0,r.iQ)(e,"status-".concat(t));var n=this.$(".status-text");n&&(n.textContent=this.getStatusText(t));var o=this.$(".status-icon");o&&(o.innerHTML=this.getStatusIcon(t))}}},{key:"updateDetails",value:function(){if(this.statusData)switch(this.statusOptions.type){case"sync":this.updateSyncDetails();break;case"queue":this.updateQueueDetails();break;case"async":this.updateAsyncDetails()}}},{key:"updateSyncDetails",value:function(){if(this.statusData){var e=this.$(".current-item");e&&this.statusData.current_item&&(e.textContent="正在处理: ".concat(this.statusData.current_item));var t=this.$(".error-count");t&&this.statusData.errors&&(t.textContent=this.statusData.errors.length.toString());var n=this.$(".warning-count");n&&this.statusData.warnings&&(n.textContent=this.statusData.warnings.length.toString())}}},{key:"updateQueueDetails",value:function(){var e=this;this.statusData&&["total_jobs","pending_jobs","processing_jobs","completed_jobs","failed_jobs"].forEach(function(t){var n=e.$(".".concat(t.replace("_","-")));n&&void 0!==e.statusData[t]&&(n.textContent=e.statusData[t].toString())})}},{key:"updateAsyncDetails",value:function(){this.statusData}},{key:"updateProgress",value:function(){var e=this.$(".progress-bar"),t=this.$(".progress-text");if(e&&this.statusData){var n=this.statusData,r=n.progress,o=void 0===r?0:r,i=n.total,a=void 0===i?0:i,s=a>0?Math.round(o/a*100):0,u=e.querySelector(".progress-fill");u&&(u.style.width="".concat(s,"%")),t&&(t.textContent=a>0?"".concat(s,"% (").concat(o,"/").concat(a,")"):"".concat(s,"%"))}}},{key:"updateTimeInfo",value:function(){if(this.statusData){var e=this.$(".start-time"),t=this.$(".elapsed-time"),n=this.$(".estimated-time");if(this.statusData.start_time){var r=Date.now()-this.statusData.start_time;e&&(e.textContent=new Date(this.statusData.start_time).toLocaleTimeString()),t&&(t.textContent=(0,Me.Az)(r))}if(n&&this.statusData.estimated_completion){var o=this.statusData.estimated_completion-Date.now();n.textContent=o>0?(0,Me.Az)(o):"即将完成"}}}},{key:"getStatusText",value:function(e){return{idle:"空闲",running:"运行中",paused:"已暂停",completed:"已完成",error:"错误",cancelled:"已取消"}[e]||e}},{key:"getStatusIcon",value:function(e){return{idle:'<span class="dashicons dashicons-clock"></span>',running:'<span class="dashicons dashicons-update spin"></span>',paused:'<span class="dashicons dashicons-controls-pause"></span>',completed:'<span class="dashicons dashicons-yes-alt"></span>',error:'<span class="dashicons dashicons-warning"></span>',cancelled:'<span class="dashicons dashicons-dismiss"></span>'}[e]||'<span class="dashicons dashicons-info"></span>'}},{key:"handleRefresh",value:(l=Ve(Fe().m(function e(t){var n,r;return Fe().w(function(e){for(;;)switch(e.p=e.n){case 0:return t.preventDefault(),n=t.target,r=n.textContent,n.setAttribute("disabled","true"),n.innerHTML='<span class="spinner is-active"></span> 刷新中...',e.p=1,e.n=2,this.refreshStatus();case 2:e.n=4;break;case 3:e.p=3,e.v;case 4:return e.p=4,n.removeAttribute("disabled"),n.textContent=r||"刷新",e.f(4);case 5:return e.a(2)}},e,this,[[1,3,4,5]])})),function(e){return l.apply(this,arguments)})},{key:"refreshStatus",value:(c=Ve(Fe().m(function e(){var t;return Fe().w(function(e){for(;;)switch(e.n){case 0:t=this.statusOptions.type,e.n="sync"===t?1:"queue"===t?3:"async"===t?5:7;break;case 1:return e.n=2,this.refreshSyncStatus();case 2:case 4:case 6:return e.a(3,7);case 3:return e.n=4,this.refreshQueueStatus();case 5:return e.n=6,this.refreshAsyncStatus();case 7:return e.a(2)}},e,this)})),function(){return c.apply(this,arguments)})},{key:"refreshSyncStatus",value:(u=Ve(Fe().m(function e(){var t;return Fe().w(function(e){for(;;)switch(e.n){case 0:t=this.getState(),this.statusData=t.sync,this.render();case 1:return e.a(2)}},e,this)})),function(){return u.apply(this,arguments)})},{key:"refreshQueueStatus",value:(s=Ve(Fe().m(function e(){var t;return Fe().w(function(e){for(;;)switch(e.n){case 0:t=this.getState(),this.statusData=t.queue,this.render();case 1:return e.a(2)}},e,this)})),function(){return s.apply(this,arguments)})},{key:"refreshAsyncStatus",value:(a=Ve(Fe().m(function e(){var t,n,r;return Fe().w(function(e){for(;;)switch(e.n){case 0:return e.n=1,fetch(window.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"notion_to_wordpress_get_async_status",nonce:(null===(t=window.notionToWp)||void 0===t?void 0:t.nonce)||""})});case 1:return n=e.v,e.n=2,n.json();case 2:(r=e.v).success&&(this.statusData=r.data.status,this.render());case 3:return e.a(2)}},e,this)})),function(){return a.apply(this,arguments)})},{key:"startAutoRefresh",value:function(){var e=this;this.refreshTimer&&clearInterval(this.refreshTimer),this.refreshTimer=setInterval(function(){e.isMounted()&&!document.hidden&&e.refreshStatus().catch(console.error)},this.statusOptions.refreshInterval)}},{key:"stopAutoRefresh",value:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)}},{key:"setStatusData",value:function(e){this.statusData=e,this.render()}},{key:"getStatusData",value:function(){return this.statusData}},{key:"setAutoRefresh",value:function(e){this.statusOptions.autoRefresh=e,e?this.startAutoRefresh():this.stopAutoRefresh()}}],o&&qe(n.prototype,o),i&&qe(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i,a,s,u,c,l}(Se.$),Xe=n(2852),Ye=(n(113),n(5746),n(6653));function Ze(e){return Ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ze(e)}function et(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function tt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?et(Object(n),!0).forEach(function(t){st(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):et(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function nt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ut(r.key),r)}}function rt(e,t,n){return t=it(t),function(e,t){if(t&&("object"==Ze(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,ot()?Reflect.construct(t,n||[],it(e).constructor):t.apply(e,n))}function ot(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ot=function(){return!!e})()}function it(e){return it=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},it(e)}function at(e,t){return at=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},at(e,t)}function st(e,t,n){return(t=ut(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){var t=function(e,t){if("object"!=Ze(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ze(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ze(t)?t:t+""}var ct=function(e){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),st(n=rt(this,t,[e]),"tabOptions",void 0),st(n,"tabs",new Map),st(n,"activeTabId",null),n.tabOptions=tt({activeTabClass:"active",tabContentClass:"tab-content",saveActiveTab:!0,storageKey:"active_tab",animationDuration:300},e),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&at(e,t)}(t,e),n=t,o=[{key:"onInit",value:function(){this.element&&((0,r.iQ)(this.element,"tab-manager"),this.discoverTabs(),this.restoreActiveTab())}},{key:"onMount",value:function(){this.updateTabStates()}},{key:"onUnmount",value:function(){}},{key:"onDestroy",value:function(){this.tabs.clear()}},{key:"onRender",value:function(){this.updateTabStates()}},{key:"bindEvents",value:function(){var e=this;this.tabs.forEach(function(t,n){e.addEventListener(t.element,"click",function(t){t.preventDefault(),e.activateTab(n)}),e.addEventListener(t.element,"keydown",function(t){var r=t;"Enter"!==r.key&&" "!==r.key||(r.preventDefault(),e.activateTab(n))})})}},{key:"onStateChange",value:function(e,t,n){var r,o;(null===(r=e.ui)||void 0===r?void 0:r.activeTab)!==(null===(o=t.ui)||void 0===o?void 0:o.activeTab)&&this.activateTab(e.ui.activeTab)}},{key:"discoverTabs",value:function(){var e=this;this.element&&(this.element.querySelectorAll("[data-tab]").forEach(function(t){var n=t,o=n.getAttribute("data-tab");if(o){var i,a=document.querySelector("#".concat(o)),s=(null===(i=n.textContent)||void 0===i?void 0:i.trim())||o,u=n.hasAttribute("disabled")||(0,r.nB)(n,"disabled");e.tabs.set(o,{id:o,element:n,contentElement:a,title:s,disabled:u}),n.setAttribute("role","tab"),n.setAttribute("aria-controls",o),n.setAttribute("aria-selected","false"),n.setAttribute("tabindex","-1"),a&&(a.setAttribute("role","tabpanel"),a.setAttribute("aria-labelledby",n.id||"tab-".concat(o)),(0,r.iQ)(a,e.tabOptions.tabContentClass))}}),this.element.setAttribute("role","tablist"))}},{key:"activateTab",value:function(e){var t=this.tabs.get(e);if(t&&!t.disabled&&this.activeTabId!==e){var n=this.activeTabId;this.activeTabId=e,this.updateTabStates(),this.switchTabContent(n,e),this.tabOptions.saveActiveTab&&this.saveActiveTab(e),this.setState({ui:tt(tt({},this.getState().ui),{},{activeTab:e})}),this.emit("tab:change",{activeTab:e,previousTab:n,tabInfo:t})}}},{key:"switchTabContent",value:function(e,t){var n=this.tabs.get(t);if(null!=n&&n.contentElement){if(e){var r=this.tabs.get(e);null!=r&&r.contentElement&&this.hideTabContent(r.contentElement)}this.showTabContent(n.contentElement)}}},{key:"showTabContent",value:function(e){(0,r.vy)(e,"hidden"),(0,r.iQ)(e,this.tabOptions.activeTabClass),e.style.display="none",e.style.display="block",this.tabOptions.animationDuration>0&&(e.style.opacity="0",e.style.transition="opacity ".concat(this.tabOptions.animationDuration,"ms ease"),e.offsetHeight,e.style.opacity="1"),this.emit("tab:content:show",{contentElement:e})}},{key:"hideTabContent",value:function(e){(0,r.vy)(e,this.tabOptions.activeTabClass),this.tabOptions.animationDuration>0?(e.style.transition="opacity ".concat(this.tabOptions.animationDuration,"ms ease"),e.style.opacity="0",setTimeout(function(){(0,r.iQ)(e,"hidden"),e.style.transition="",e.style.opacity=""},this.tabOptions.animationDuration)):(0,r.iQ)(e,"hidden"),this.emit("tab:content:hide",{contentElement:e})}},{key:"updateTabStates",value:function(){var e=this;this.tabs.forEach(function(t,n){n===e.activeTabId?((0,r.iQ)(t.element,e.tabOptions.activeTabClass),t.element.setAttribute("aria-selected","true"),t.element.setAttribute("tabindex","0")):((0,r.vy)(t.element,e.tabOptions.activeTabClass),t.element.setAttribute("aria-selected","false"),t.element.setAttribute("tabindex","-1")),t.disabled?((0,r.iQ)(t.element,"disabled"),t.element.setAttribute("aria-disabled","true")):((0,r.vy)(t.element,"disabled"),t.element.removeAttribute("aria-disabled"))})}},{key:"restoreActiveTab",value:function(){var e=this.tabOptions.defaultTab;if(this.tabOptions.saveActiveTab&&this.tabOptions.storageKey){var t=Ye.Lr.get(this.tabOptions.storageKey);t&&this.tabs.has(t)&&(e=t)}var n=new URLSearchParams(window.location.search).get("tab");if(n&&this.tabs.has(n)&&(e=n),!e){var r=Array.from(this.tabs.values()).find(function(e){return!e.disabled});e=null==r?void 0:r.id}e&&this.activateTab(e)}},{key:"saveActiveTab",value:function(e){this.tabOptions.storageKey&&Ye.Lr.set(this.tabOptions.storageKey,e)}},{key:"addTab",value:function(e){var t=this,n=tt({disabled:!1},e);this.tabs.set(n.id,n),n.element.setAttribute("role","tab"),n.element.setAttribute("aria-controls",n.id),n.element.setAttribute("aria-selected","false"),n.element.setAttribute("tabindex","-1"),n.contentElement&&(n.contentElement.setAttribute("role","tabpanel"),n.contentElement.setAttribute("aria-labelledby",n.element.id||"tab-".concat(n.id)),(0,r.iQ)(n.contentElement,this.tabOptions.tabContentClass)),this.addEventListener(n.element,"click",function(e){e.preventDefault(),t.activateTab(n.id)}),this.updateTabStates()}},{key:"removeTab",value:function(e){if(this.tabs.get(e)){if(this.activeTabId===e){var t=Array.from(this.tabs.keys()).filter(function(t){return t!==e});t.length>0?this.activateTab(t[0]):this.activeTabId=null}this.tabs.delete(e),this.updateTabStates()}}},{key:"enableTab",value:function(e){var t=this.tabs.get(e);t&&(t.disabled=!1,this.updateTabStates())}},{key:"disableTab",value:function(e){var t=this.tabs.get(e);if(t){if(t.disabled=!0,this.activeTabId===e){var n=Array.from(this.tabs.values()).filter(function(e){return!e.disabled});n.length>0&&this.activateTab(n[0].id)}this.updateTabStates()}}},{key:"getActiveTabId",value:function(){return this.activeTabId}},{key:"getTabInfo",value:function(e){return this.tabs.get(e)}},{key:"getAllTabs",value:function(){return Array.from(this.tabs.values())}},{key:"hasTab",value:function(e){return this.tabs.has(e)}}],o&&nt(n.prototype,o),i&&nt(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(Se.$);n(8598),n(888);function lt(e){return lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lt(e)}function ft(e){return function(e){if(Array.isArray(e))return ht(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ht(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ht(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ht(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function pt(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return dt(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(dt(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,dt(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,dt(f,"constructor",c),dt(c,"constructor",u),u.displayName="GeneratorFunction",dt(c,o,"GeneratorFunction"),dt(f),dt(f,o,"Generator"),dt(f,r,function(){return this}),dt(f,"toString",function(){return"[object Generator]"}),(pt=function(){return{w:i,m:h}})()}function dt(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}dt=function(e,t,n,r){function i(t,n){dt(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},dt(e,t,n,r)}function yt(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function vt(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){yt(i,r,o,a,s,"next",e)}function s(e){yt(i,r,o,a,s,"throw",e)}a(void 0)})}}function mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function bt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?mt(Object(n),!0).forEach(function(t){wt(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):mt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function gt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kt(r.key),r)}}function wt(e,t,n){return(t=kt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kt(e){var t=function(e,t){if("object"!=lt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=lt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lt(t)?t:t+""}var St=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),wt(this,"options",void 0),wt(this,"databases",new Map),wt(this,"refreshTimers",new Map),wt(this,"currentQuery",new Map),e.instance)return e.instance;e.instance=this,this.options=bt({pageSize:20,enableSearch:!0,enableFilter:!0,enableSort:!0,autoRefresh:!1,refreshInterval:3e4},t),this.init()}return t=e,n=[{key:"init",value:function(){this.setupEventListeners(),(0,o.Ic)("database:record:manager:initialized")}},{key:"setupEventListeners",value:function(){var e=this;document.addEventListener("visibilitychange",function(){!document.hidden&&e.options.autoRefresh&&e.refreshAllDatabases()}),window.addEventListener("focus",function(){e.options.autoRefresh&&e.refreshAllDatabases()})}},{key:"getDatabaseRecords",value:(p=vt(pt().m(function e(t){var n,r,i,s,u,c,l,f,h,p=arguments;return pt().w(function(e){for(;;)switch(e.p=e.n){case 0:return n=p.length>1&&void 0!==p[1]?p[1]:{},r=bt({database_id:t,page_size:this.options.pageSize},n),this.currentQuery.set(t,r),this.updateDatabaseState(t,{loading:!0,error:void 0}),e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_database_records",r);case 2:if(!(i=e.v)||!i.data){e.n=3;break}return s=i.data,u=s.records,c=s.has_more,l=s.next_cursor,f=s.total_count,this.updateDatabaseState(t,{loading:!1,records:u||[],totalCount:f||0,hasMore:c||!1,nextCursor:l,error:void 0}),(0,o.Ic)("database:records:loaded",{databaseId:t,records:u||[],totalCount:f||0}),e.a(2,u||[]);case 3:throw new Error("获取数据库记录失败");case 4:e.n=6;break;case 5:throw e.p=5,h=e.v,this.updateDatabaseState(t,{loading:!1,error:h.message}),(0,o.Ic)("database:records:error",{databaseId:t,error:h}),h;case 6:return e.a(2)}},e,this,[[1,5]])})),function(e){return p.apply(this,arguments)})},{key:"loadMoreRecords",value:(h=vt(pt().m(function e(t){var n,r,i,u,c,l,f,h,p,d;return pt().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=this.databases.get(t),r=this.currentQuery.get(t),n&&r&&n.hasMore&&!n.loading){e.n=1;break}return e.a(2,[]);case 1:return e.p=1,i=bt(bt({},r),{},{start_cursor:n.nextCursor}),e.n=2,(0,a.bE)("notion_to_wordpress_get_database_records",i);case 2:if(!(u=e.v)||!u.data){e.n=3;break}return c=u.data,l=c.records,f=c.has_more,h=c.next_cursor,p=[].concat(ft(n.records),ft(l||[])),this.updateDatabaseState(t,{records:p,hasMore:f||!1,nextCursor:h}),(0,o.Ic)("database:records:more:loaded",{databaseId:t,newRecords:l||[],allRecords:p}),e.a(2,l||[]);case 3:throw new Error("加载更多记录失败");case 4:e.n=6;break;case 5:throw e.p=5,d=e.v,(0,s.Qg)("加载更多记录失败: ".concat(d.message)),d;case 6:return e.a(2)}},e,this,[[1,5]])})),function(e){return h.apply(this,arguments)})},{key:"searchRecords",value:(f=vt(pt().m(function e(t,n,r){var o;return pt().w(function(e){for(;;)switch(e.n){case 0:if(this.options.enableSearch){e.n=1;break}return e.a(2,[]);case 1:return o={property:r||"title",condition:"contains",value:n},e.a(2,this.getDatabaseRecords(t,{filter:[o]}))}},e,this)})),function(e,t,n){return f.apply(this,arguments)})},{key:"filterRecords",value:(l=vt(pt().m(function e(t,n){return pt().w(function(e){for(;;)switch(e.n){case 0:if(this.options.enableFilter){e.n=1;break}return e.a(2,[]);case 1:return e.a(2,this.getDatabaseRecords(t,{filter:n}))}},e,this)})),function(e,t){return l.apply(this,arguments)})},{key:"sortRecords",value:(c=vt(pt().m(function e(t,n){return pt().w(function(e){for(;;)switch(e.n){case 0:if(this.options.enableSort){e.n=1;break}return e.a(2,[]);case 1:return e.a(2,this.getDatabaseRecords(t,{sorts:n}))}},e,this)})),function(e,t){return c.apply(this,arguments)})},{key:"refreshDatabase",value:(u=vt(pt().m(function e(t){var n,r;return pt().w(function(e){for(;;)switch(e.n){case 0:if(!(n=this.currentQuery.get(t))){e.n=2;break}return r=bt(bt({},n),{},{start_cursor:void 0}),e.n=1,this.getDatabaseRecords(t,r);case 1:(0,s.cf)("数据库记录已刷新");case 2:return e.a(2)}},e,this)})),function(e){return u.apply(this,arguments)})},{key:"refreshAllDatabases",value:(i=vt(pt().m(function e(){var t,n=this;return pt().w(function(e){for(;;)switch(e.n){case 0:return t=Array.from(this.databases.keys()).map(function(e){return n.refreshDatabase(e).catch(function(e){})}),e.n=1,Promise.all(t);case 1:return e.a(2)}},e,this)})),function(){return i.apply(this,arguments)})},{key:"getDatabaseState",value:function(e){return this.databases.get(e)}},{key:"getAllDatabaseStates",value:function(){return new Map(this.databases)}},{key:"updateDatabaseState",value:function(e,t){var n=bt(bt({},this.databases.get(e)||{loading:!1,records:[],totalCount:0,currentPage:1,hasMore:!1}),t);this.databases.set(e,n),(0,o.Ic)("database:state:changed",{databaseId:e,state:n})}},{key:"startAutoRefresh",value:function(e){var t=this;if(this.options.autoRefresh){this.stopAutoRefresh(e);var n=setInterval(function(){t.refreshDatabase(e).catch(function(e){})},this.options.refreshInterval);this.refreshTimers.set(e,n)}}},{key:"stopAutoRefresh",value:function(e){var t=this.refreshTimers.get(e);t&&(clearInterval(t),this.refreshTimers.delete(e))}},{key:"stopAllAutoRefresh",value:function(){this.refreshTimers.forEach(function(e){clearInterval(e)}),this.refreshTimers.clear()}},{key:"clearDatabaseState",value:function(e){this.databases.delete(e),this.currentQuery.delete(e),this.stopAutoRefresh(e),(0,o.Ic)("database:state:cleared",{databaseId:e})}},{key:"clearAllStates",value:function(){this.databases.clear(),this.currentQuery.clear(),this.stopAllAutoRefresh(),(0,o.Ic)("database:all:states:cleared")}},{key:"getOptions",value:function(){return bt({},this.options)}},{key:"updateOptions",value:function(e){this.options=bt(bt({},this.options),e),(0,o.Ic)("database:options:updated",this.options)}},{key:"destroy",value:function(){this.stopAllAutoRefresh(),this.clearAllStates(),document.removeEventListener("visibilitychange",this.refreshAllDatabases),window.removeEventListener("focus",this.refreshAllDatabases),e.instance=null,(0,o.Ic)("database:record:manager:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&gt(t.prototype,n),r&&gt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c,l,f,h,p}();wt(St,"instance",null);var Ot=St.getInstance();function Tt(e){return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tt(e)}function Et(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return jt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?jt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function _t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Pt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ct(r.key),r)}}function It(e,t,n){return(t=Ct(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ct(e){var t=function(e,t){if("object"!=Tt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Tt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Tt(t)?t:t+""}var xt=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),e.instance)return e.instance;e.instance=this}return t=e,n=[{key:"renderDatabase",value:function(e,t,n){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_t(Object(n),!0).forEach(function(t){It(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_t(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({viewType:"table",showProperties:[],hideProperties:[],maxRecords:50,enableInteraction:!0,responsive:!0},arguments.length>3&&void 0!==arguments[3]?arguments[3]:{});if(e.innerHTML="",t.title){var o=this.createTitleElement(t.title);e.appendChild(o)}var i=n.slice(0,r.maxRecords),a=this.renderView(t,i,r);if(e.appendChild(a),n.length>r.maxRecords){var s=this.createInfoElement("显示 ".concat(r.maxRecords," / ").concat(n.length," 条记录"));e.appendChild(s)}r.responsive&&e.classList.add("notion-database-responsive")}},{key:"renderView",value:function(e,t,n){switch(n.viewType){case"table":default:return this.renderTableView(e,t,n);case"list":return this.renderListView(e,t,n);case"gallery":return this.renderGalleryView(e,t,n);case"board":return this.renderBoardView(e,t,n);case"calendar":return this.renderCalendarView(e,t,n);case"timeline":return this.renderTimelineView(e,t,n)}}},{key:"renderTableView",value:function(e,t,n){var r=this,o=document.createElement("div");o.className="notion-database-table";var i=this.getVisibleProperties(e,n),a=this.createTableHeader(i);o.appendChild(a);var s=document.createElement("div");return s.className="notion-table-body",t.forEach(function(e){var t=r.createTableRow(e,i,n);s.appendChild(t)}),o.appendChild(s),o}},{key:"renderListView",value:function(e,t,n){var r=this,o=document.createElement("div");return o.className="notion-database-list",t.forEach(function(t){var i=r.createListItem(t,e,n);o.appendChild(i)}),o}},{key:"renderGalleryView",value:function(e,t,n){var r=this,o=document.createElement("div");return o.className="notion-database-gallery",t.forEach(function(t){var i=r.createGalleryCard(t,e,n);o.appendChild(i)}),o}},{key:"renderBoardView",value:function(e,t,n){var r=this,o=document.createElement("div");o.className="notion-database-board";var i=this.groupRecordsByStatus(t);return Object.entries(i).forEach(function(t){var i=Et(t,2),a=i[0],s=i[1],u=r.createBoardColumn(a,s,e,n);o.appendChild(u)}),o}},{key:"renderCalendarView",value:function(e,t,n){var r=document.createElement("div");return r.className="notion-database-calendar",r.innerHTML='<div class="calendar-placeholder">日历视图开发中...</div>',r}},{key:"renderTimelineView",value:function(e,t,n){var r=document.createElement("div");return r.className="notion-database-timeline",r.innerHTML='<div class="timeline-placeholder">时间线视图开发中...</div>',r}},{key:"createTitleElement",value:function(e){var t=document.createElement("h3");return t.className="notion-database-title",t.textContent=e,t}},{key:"createInfoElement",value:function(e){var t=document.createElement("div");return t.className="notion-database-info",t.textContent=e,t}},{key:"getVisibleProperties",value:function(e,t){var n=this,r=[];return Object.entries(e.properties).forEach(function(e){var o=Et(e,2),i=o[0],a=o[1],s=0===t.showProperties.length||t.showProperties.includes(i),u=t.hideProperties.includes(i);s&&!u&&r.push({name:i,type:a.type||"text",visible:!0,width:n.getPropertyWidth(a.type),format:n.getPropertyFormat(a.type)})}),r}},{key:"createTableHeader",value:function(e){var t=document.createElement("div");return t.className="notion-table-header",e.forEach(function(e){var n=document.createElement("div");n.className="notion-table-header-cell",n.textContent=e.name,e.width&&(n.style.width=e.width),t.appendChild(n)}),t}},{key:"createTableRow",value:function(e,t,n){var r=this,o=document.createElement("div");return o.className="notion-table-row",o.dataset.recordId=e.id,t.forEach(function(t){var i=r.createTableCell(e,t,n);o.appendChild(i)}),n.enableInteraction&&(o.addEventListener("click",function(){r.handleRecordClick(e)}),o.classList.add("notion-table-row-interactive")),o}},{key:"createTableCell",value:function(e,t,n){var r=document.createElement("div");r.className="notion-table-cell",t.width&&(r.style.width=t.width);var o=e.properties[t.name],i=this.formatPropertyValue(o,t);return"string"==typeof i?r.textContent=i:r.appendChild(i),r}},{key:"createListItem",value:function(e,t,n){var r=this,o=document.createElement("div");if(o.className="notion-list-item",o.dataset.recordId=e.id,e.icon){var i=this.createIcon(e.icon);o.appendChild(i)}var a=this.createRecordTitle(e);o.appendChild(a);var s=this.createRecordProperties(e,t,n);return o.appendChild(s),n.enableInteraction&&(o.addEventListener("click",function(){r.handleRecordClick(e)}),o.classList.add("notion-list-item-interactive")),o}},{key:"createGalleryCard",value:function(e,t,n){var r=this,o=document.createElement("div");if(o.className="notion-gallery-card",o.dataset.recordId=e.id,e.cover){var i=this.createCover(e.cover);o.appendChild(i)}var a=document.createElement("div");a.className="notion-gallery-content";var s=this.createRecordTitle(e);a.appendChild(s);var u=this.createRecordProperties(e,t,n);return a.appendChild(u),o.appendChild(a),n.enableInteraction&&(o.addEventListener("click",function(){r.handleRecordClick(e)}),o.classList.add("notion-gallery-card-interactive")),o}},{key:"createBoardColumn",value:function(e,t,n,r){var o=this,i=document.createElement("div");i.className="notion-board-column";var a=document.createElement("div");a.className="notion-board-header",a.textContent="".concat(e," (").concat(t.length,")"),i.appendChild(a);var s=document.createElement("div");return s.className="notion-board-list",t.forEach(function(e){var t=o.createBoardCard(e,n,r);s.appendChild(t)}),i.appendChild(s),i}},{key:"createBoardCard",value:function(e,t,n){var r=this,o=document.createElement("div");o.className="notion-board-card",o.dataset.recordId=e.id;var i=this.createRecordTitle(e);o.appendChild(i);var a=this.createRecordProperties(e,t,n);return o.appendChild(a),n.enableInteraction&&(o.addEventListener("click",function(){r.handleRecordClick(e)}),o.classList.add("notion-board-card-interactive")),o}},{key:"groupRecordsByStatus",value:function(e){var t={};return e.forEach(function(e){var n="未分类";Object.entries(e.properties).forEach(function(e){var t=Et(e,2),r=t[0],o=t[1];(r.toLowerCase().includes("status")||r.toLowerCase().includes("状态"))&&(o&&"object"===Tt(o)&&"name"in o?n=o.name:"string"==typeof o&&(n=o))}),t[n]||(t[n]=[]),t[n].push(e)}),t}},{key:"createIcon",value:function(e){var t,n,r=document.createElement("span");if(r.className="notion-record-icon","emoji"===e.type&&e.emoji)r.textContent=e.emoji;else if("file"===e.type&&null!==(t=e.file)&&void 0!==t&&t.url){var o=document.createElement("img");o.src=e.file.url,o.alt="Icon",r.appendChild(o)}else if("external"===e.type&&null!==(n=e.external)&&void 0!==n&&n.url){var i=document.createElement("img");i.src=e.external.url,i.alt="Icon",r.appendChild(i)}return r}},{key:"createCover",value:function(e){var t,n,r=document.createElement("div");if(r.className="notion-record-cover","file"===e.type&&null!==(t=e.file)&&void 0!==t&&t.url){var o=document.createElement("img");o.src=e.file.url,o.alt="Cover",r.appendChild(o)}else if("external"===e.type&&null!==(n=e.external)&&void 0!==n&&n.url){var i=document.createElement("img");i.src=e.external.url,i.alt="Cover",r.appendChild(i)}return r}},{key:"createRecordTitle",value:function(e){var t=document.createElement("div");t.className="notion-record-title";var n="无标题";return Object.entries(e.properties).forEach(function(e){var t=Et(e,2),r=t[0],o=t[1];(r.toLowerCase().includes("title")||r.toLowerCase().includes("名称"))&&(o&&"object"===Tt(o)&&"title"in o&&Array.isArray(o.title)?n=o.title.map(function(e){return e.plain_text||""}).join(""):"string"==typeof o&&(n=o))}),t.textContent=n,t}},{key:"createRecordProperties",value:function(e,t,n){var r=this,o=document.createElement("div");return o.className="notion-record-properties",this.getVisibleProperties(t,n).slice(0,3).forEach(function(t){var n=e.properties[t.name];if(n){var i=document.createElement("div");i.className="notion-record-property";var a=document.createElement("span");a.className="notion-property-label",a.textContent=t.name+": ";var s=document.createElement("span");s.className="notion-property-value";var u=r.formatPropertyValue(n,t);"string"==typeof u?s.textContent=u:s.appendChild(u),i.appendChild(a),i.appendChild(s),o.appendChild(i)}}),o}},{key:"formatPropertyValue",value:function(e,t){var n,r;if(!e)return"";switch(t.type){case"title":return Array.isArray(e.title)?e.title.map(function(e){return e.plain_text||""}).join(""):String(e);case"rich_text":return Array.isArray(e.rich_text)?e.rich_text.map(function(e){return e.plain_text||""}).join(""):String(e);case"number":return e.number?String(e.number):"";case"select":return(null===(n=e.select)||void 0===n?void 0:n.name)||"";case"multi_select":return Array.isArray(e.multi_select)?e.multi_select.map(function(e){return e.name}).join(", "):"";case"date":return null!==(r=e.date)&&void 0!==r&&r.start?new Date(e.date.start).toLocaleDateString():"";case"checkbox":return e.checkbox?"✓":"✗";case"url":if(e.url){var o=document.createElement("a");return o.href=e.url,o.textContent=e.url,o.target="_blank",o}return"";case"email":if(e.email){var i=document.createElement("a");return i.href="mailto:".concat(e.email),i.textContent=e.email,i}return"";case"phone_number":return e.phone_number||"";default:return String(e)}}},{key:"getPropertyWidth",value:function(e){switch(e){case"checkbox":return"60px";case"date":return"120px";case"number":return"100px";case"select":return"150px";default:return"auto"}}},{key:"getPropertyFormat",value:function(e){return e}},{key:"handleRecordClick",value:function(e){e.url&&window.open(e.url,"_blank")}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}},{key:"detectViewType",value:function(e){var t=Object.keys(e.properties);return t.some(function(e){return e.toLowerCase().includes("status")})?"board":t.some(function(e){return e.toLowerCase().includes("date")})?"calendar":t.length>5?"table":"list"}}],n&&Pt(t.prototype,n),r&&Pt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();It(xt,"instance",null);xt.getInstance();function At(e){return At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},At(e)}function Lt(e){return function(e){if(Array.isArray(e))return Dt(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Dt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Dt(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Dt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Rt(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Mt(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Mt(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Mt(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Mt(f,"constructor",c),Mt(c,"constructor",u),u.displayName="GeneratorFunction",Mt(c,o,"GeneratorFunction"),Mt(f),Mt(f,o,"Generator"),Mt(f,r,function(){return this}),Mt(f,"toString",function(){return"[object Generator]"}),(Rt=function(){return{w:i,m:h}})()}function Mt(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Mt=function(e,t,n,r){function i(t,n){Mt(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Mt(e,t,n,r)}function Nt(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Ft(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Nt(i,r,o,a,s,"next",e)}function s(e){Nt(i,r,o,a,s,"throw",e)}a(void 0)})}}function Bt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bt(Object(n),!0).forEach(function(t){Ut(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Vt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,qt(r.key),r)}}function Ut(e,t,n){return(t=qt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function qt(e){var t=function(e,t){if("object"!=At(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=At(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==At(t)?t:t+""}var zt=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ut(this,"options",void 0),Ut(this,"logs",[]),Ut(this,"filteredLogs",[]),Ut(this,"currentFilter",{}),Ut(this,"refreshTimer",null),Ut(this,"isLoading",!1),Ut(this,"logFiles",[]),e.instance)return e.instance;e.instance=this,this.options=Gt({autoRefresh:!1,refreshInterval:1e4,maxEntries:1e3,enableExport:!0,enableClear:!0},t),this.init()}return t=e,n=[{key:"init",value:function(){this.setupEventListeners(),this.loadLogFiles(),(0,o.Ic)("log:manager:initialized")}},{key:"setupEventListeners",value:function(){var e=this;document.addEventListener("visibilitychange",function(){!document.hidden&&e.options.autoRefresh&&e.refreshLogs()}),window.addEventListener("focus",function(){e.options.autoRefresh&&e.refreshLogs()})}},{key:"loadLogFiles",value:(h=Ft(Rt().m(function e(){var t,n;return Rt().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_log_files",{});case 1:if(!(t=e.v)||!t.data){e.n=2;break}return this.logFiles=t.data,(0,o.Ic)("log:files:loaded",{files:this.logFiles}),e.a(2,this.logFiles);case 2:throw new Error("获取日志文件列表失败");case 3:e.n=5;break;case 4:return e.p=4,n=e.v,(0,s.Qg)("加载日志文件列表失败: ".concat(n.message)),e.a(2,[]);case 5:return e.a(2)}},e,this,[[0,4]])})),function(){return h.apply(this,arguments)})},{key:"loadLogContent",value:(f=Ft(Rt().m(function e(t){var n,r;return Rt().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t||0!==this.logFiles.length){e.n=1;break}return e.n=1,this.loadLogFiles();case 1:if(n=t||this.logFiles[0]){e.n=2;break}throw new Error("没有可用的日志文件");case 2:return e.p=2,e.n=3,(0,a.bE)("notion_to_wordpress_view_log",{file:n});case 3:if(!(r=e.v)||!r.data){e.n=4;break}return(0,o.Ic)("log:content:loaded",{file:n,content:r.data}),e.a(2,r.data);case 4:throw new Error("加载日志内容失败");case 5:e.n=7;break;case 6:throw e.p=6,e.v;case 7:return e.a(2)}},e,this,[[2,6]])})),function(e){return f.apply(this,arguments)})},{key:"getLogs",value:(l=Ft(Rt().m(function e(){var t,n,r,i=arguments;return Rt().w(function(e){for(;;)switch(e.p=e.n){case 0:return t=i.length>0&&void 0!==i[0]?i[0]:{},this.setLoading(!0),e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_logs",Gt({limit:this.options.maxEntries},t));case 2:if(!(n=e.v)||!n.data){e.n=3;break}return this.logs=this.parseLogEntries(n.data),this.currentFilter=t,this.applyFilters(),(0,o.Ic)("log:entries:loaded",{logs:this.logs,filtered:this.filteredLogs,filter:t}),e.a(2,this.logs);case 3:throw new Error("获取日志失败");case 4:e.n=6;break;case 5:throw e.p=5,r=e.v,(0,s.Qg)("获取日志失败: ".concat(r.message)),r;case 6:return e.p=6,this.setLoading(!1),e.f(6);case 7:return e.a(2)}},e,this,[[1,5,6,7]])})),function(){return l.apply(this,arguments)})},{key:"parseLogEntries",value:function(e){var t=this;return Array.isArray(e)?e.map(function(e){return t.parseLogEntry(e)}):"string"==typeof e?this.parseLogString(e):[]}},{key:"parseLogEntry",value:function(e){return{id:e.id||this.generateLogId(),timestamp:e.timestamp||Date.now(),level:e.level||"info",message:e.message||"",context:e.context,source:e.source||"unknown",file:e.file}}},{key:"parseLogString",value:function(e){var t=this,n=e.split("\n").filter(function(e){return e.trim()}),r=[];return n.forEach(function(e){var n=t.parseLogLine(e);n&&r.push(n)}),r}},{key:"parseLogLine",value:function(e){for(var t=0,n=[/^\[([^\]]+)\]\s+(\w+):\s*(.+)$/,/^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,/^(\w+):\s*(.+)$/];t<n.length;t++){var r=n[t],o=e.match(r);if(o){var i=Date.now(),a="info",s="";return 4===o.length?(i=new Date(o[1]).getTime()||Date.now(),a=o[2].toLowerCase(),s=o[3]):3===o.length&&(a=o[1].toLowerCase(),s=o[2]),{id:this.generateLogId(),timestamp:i,level:this.normalizeLogLevel(a),message:s.trim(),source:"file",file:this.currentFilter.file}}}return{id:this.generateLogId(),timestamp:Date.now(),level:"info",message:e.trim(),source:"file",file:this.currentFilter.file}}},{key:"normalizeLogLevel",value:function(e){switch(e.toLowerCase()){case"debug":case"trace":return"debug";case"info":case"notice":default:return"info";case"warning":case"warn":return"warning";case"error":case"critical":case"alert":case"emergency":return"error"}}},{key:"generateLogId",value:function(){return"log_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))}},{key:"applyFilters",value:function(){var e=this;this.filteredLogs=this.logs.filter(function(t){if(e.currentFilter.level&&t.level!==e.currentFilter.level)return!1;if(e.currentFilter.source&&t.source!==e.currentFilter.source)return!1;if(e.currentFilter.dateFrom){var n=new Date(e.currentFilter.dateFrom).getTime();if(t.timestamp<n)return!1}if(e.currentFilter.dateTo){var r=new Date(e.currentFilter.dateTo).getTime()+864e5;if(t.timestamp>r)return!1}if(e.currentFilter.search){var o=e.currentFilter.search.toLowerCase();if(!"".concat(t.message," ").concat(t.source).toLowerCase().includes(o))return!1}return!0}),(0,o.Ic)("log:filtered",{total:this.logs.length,filtered:this.filteredLogs.length,filter:this.currentFilter})}},{key:"setFilter",value:function(e){this.currentFilter=Gt(Gt({},this.currentFilter),e),this.applyFilters(),(0,o.Ic)("log:filter:changed",{filter:this.currentFilter})}},{key:"clearFilter",value:function(){this.currentFilter={},this.applyFilters(),(0,o.Ic)("log:filter:cleared")}},{key:"refreshLogs",value:(c=Ft(Rt().m(function e(){return Rt().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.getLogs(this.currentFilter);case 1:(0,s.cf)("日志已刷新"),e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return c.apply(this,arguments)})},{key:"clearLogs",value:(u=Ft(Rt().m(function e(){var t,n;return Rt().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.options.enableClear){e.n=1;break}return(0,s.Qg)("清除日志功能已禁用"),e.a(2);case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_clear_logs",{});case 2:if(!(t=e.v)||!t.data){e.n=3;break}this.logs=[],this.filteredLogs=[],(0,o.Ic)("log:cleared"),(0,s.Te)("日志已清除"),e.n=4;break;case 3:throw new Error("清除日志失败");case 4:e.n=6;break;case 5:e.p=5,n=e.v,(0,s.Qg)("清除日志失败: ".concat(n.message));case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return u.apply(this,arguments)})},{key:"exportLogs",value:(i=Ft(Rt().m(function e(){var t,n,r,i,a=arguments;return Rt().w(function(e){for(;;)switch(e.n){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:"txt",this.options.enableExport){e.n=1;break}return(0,s.Qg)("导出日志功能已禁用"),e.a(2);case 1:try{n=this.filteredLogs.length>0?this.filteredLogs:this.logs,r=this.formatLogsForExport(n,t),i="logs_".concat((new Date).toISOString().split("T")[0],".").concat(t),this.downloadFile(r,i,this.getMimeType(t)),(0,o.Ic)("log:exported",{format:t,count:n.length}),(0,s.Te)("已导出 ".concat(n.length," 条日志"))}catch(e){(0,s.Qg)("导出日志失败: ".concat(e.message))}case 2:return e.a(2)}},e,this)})),function(){return i.apply(this,arguments)})},{key:"formatLogsForExport",value:function(e,t){switch(t){case"json":return JSON.stringify(e,null,2);case"csv":return"Timestamp,Level,Source,Message,Context\n"+e.map(function(e){var t=new Date(e.timestamp).toISOString(),n=e.context?JSON.stringify(e.context).replace(/"/g,'""'):"",r=e.message.replace(/"/g,'""');return'"'.concat(t,'","').concat(e.level,'","').concat(e.source,'","').concat(r,'","').concat(n,'"')}).join("\n");default:return e.map(function(e){var t=new Date(e.timestamp).toLocaleString(),n=e.context?"\nContext: ".concat(JSON.stringify(e.context,null,2)):"";return"[".concat(t,"] ").concat(e.level.toUpperCase(),": ").concat(e.message).concat(n)}).join("\n\n")}}},{key:"getMimeType",value:function(e){switch(e){case"json":return"application/json";case"csv":return"text/csv";default:return"text/plain"}}},{key:"downloadFile",value:function(e,t,n){var r=new Blob([e],{type:n}),o=URL.createObjectURL(r),i=document.createElement("a");i.href=o,i.download=t,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(o)}},{key:"startAutoRefresh",value:function(){var e=this;this.refreshTimer&&this.stopAutoRefresh(),this.refreshTimer=setInterval(function(){e.refreshLogs().catch(console.error)},this.options.refreshInterval),(0,o.Ic)("log:auto:refresh:started")}},{key:"stopAutoRefresh",value:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null),(0,o.Ic)("log:auto:refresh:stopped")}},{key:"setLoading",value:function(e){this.isLoading=e,(0,o.Ic)("log:loading:changed",{loading:e})}},{key:"getStats",value:function(){var e={total:this.filteredLogs.length,debug:0,info:0,warning:0,error:0,sources:{}};return this.filteredLogs.forEach(function(t){e[t.level]++,e.sources[t.source]||(e.sources[t.source]=0),e.sources[t.source]++}),e}},{key:"getCurrentLogs",value:function(){return Lt(this.logs)}},{key:"getFilteredLogs",value:function(){return Lt(this.filteredLogs)}},{key:"getCurrentFilter",value:function(){return Gt({},this.currentFilter)}},{key:"getLogFiles",value:function(){return Lt(this.logFiles)}},{key:"isLoadingLogs",value:function(){return this.isLoading}},{key:"getOptions",value:function(){return Gt({},this.options)}},{key:"updateOptions",value:function(e){this.options=Gt(Gt({},this.options),e),(0,o.Ic)("log:options:updated",this.options)}},{key:"destroy",value:function(){this.stopAutoRefresh(),document.removeEventListener("visibilitychange",this.refreshLogs),window.removeEventListener("focus",this.refreshLogs),e.instance=null,(0,o.Ic)("log:manager:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&Vt(t.prototype,n),r&&Vt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c,l,f,h}();Ut(zt,"instance",null);var Qt=zt.getInstance();function Ht(e){return Ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ht(e)}function Wt(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Kt(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Kt(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Kt(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Kt(f,"constructor",c),Kt(c,"constructor",u),u.displayName="GeneratorFunction",Kt(c,o,"GeneratorFunction"),Kt(f),Kt(f,o,"Generator"),Kt(f,r,function(){return this}),Kt(f,"toString",function(){return"[object Generator]"}),(Wt=function(){return{w:i,m:h}})()}function Kt(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Kt=function(e,t,n,r){function i(t,n){Kt(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Kt(e,t,n,r)}function $t(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Jt(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){$t(i,r,o,a,s,"next",e)}function s(e){$t(i,r,o,a,s,"throw",e)}a(void 0)})}}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Yt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach(function(t){en(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Zt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,tn(r.key),r)}}function en(e,t,n){return(t=tn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function tn(e){var t=function(e,t){if("object"!=Ht(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ht(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ht(t)?t:t+""}var nn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),en(this,"options",void 0),en(this,"settings",null),en(this,"validationRules",[]),en(this,"autoSaveTimer",null),en(this,"isLoading",!1),en(this,"isSaving",!1),en(this,"hasUnsavedChanges",!1),en(this,"debouncedAutoSave",void 0),e.instance)return e.instance;e.instance=this,this.options=Yt({autoSave:!0,autoSaveDelay:3e3,validateOnChange:!0,enableImportExport:!0,enableConnectionTest:!0},t),this.init()}return t=e,n=[{key:"init",value:function(){this.setupValidationRules(),this.setupEventListeners(),this.setupAutoSave(),(0,o.Ic)("settings:manager:initialized")}},{key:"setupValidationRules",value:function(){this.validationRules=[{field:"notion_api_key",type:"required",message:"Notion API密钥不能为空"},{field:"notion_api_key",type:"custom",message:"API密钥格式不正确",validator:function(e){return!e||/^[a-zA-Z0-9_-]{30,80}$/.test(e.trim())}},{field:"notion_database_id",type:"required",message:"数据库ID不能为空"},{field:"notion_database_id",type:"custom",message:"数据库ID格式不正确",validator:function(e){return!e||/^[a-f0-9]{32}$/.test(e.replace(/-/g,""))}},{field:"sync_interval",type:"number",message:"同步间隔必须是正整数",min:1,max:1440},{field:"api_page_size",type:"number",message:"API页面大小必须在1-100之间",min:1,max:100},{field:"concurrent_requests",type:"number",message:"并发请求数必须在1-10之间",min:1,max:10},{field:"batch_size",type:"number",message:"批处理大小必须在1-50之间",min:1,max:50},{field:"cache_duration",type:"number",message:"缓存时长必须是正整数",min:1,max:86400}]}},{key:"setupEventListeners",value:function(){var e=this;window.addEventListener("beforeunload",function(t){if(e.hasUnsavedChanges)return t.preventDefault(),t.returnValue="您有未保存的设置更改，确定要离开吗？",t.returnValue}),document.addEventListener("keydown",function(t){(t.ctrlKey||t.metaKey)&&"s"===t.key&&(t.preventDefault(),e.saveSettings())})}},{key:"setupAutoSave",value:function(){var e=this;this.options.autoSave&&(this.debouncedAutoSave=(0,r.sg)(function(){e.hasUnsavedChanges&&e.settings&&e.saveSettings(!0)},this.options.autoSaveDelay))}},{key:"loadSettings",value:(h=Jt(Wt().m(function e(){var t,n;return Wt().w(function(e){for(;;)switch(e.p=e.n){case 0:return this.setLoading(!0),e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_settings",{});case 2:if(!(t=e.v)||!t.data){e.n=3;break}return this.settings=this.normalizeSettings(t.data),this.hasUnsavedChanges=!1,(0,o.Ic)("settings:loaded",{settings:this.settings}),e.a(2,this.settings);case 3:throw new Error("获取设置失败");case 4:e.n=6;break;case 5:throw e.p=5,n=e.v,(0,s.Qg)("加载设置失败: ".concat(n.message)),n;case 6:return e.p=6,this.setLoading(!1),e.f(6);case 7:return e.a(2)}},e,this,[[1,5,6,7]])})),function(){return h.apply(this,arguments)})},{key:"normalizeSettings",value:function(e){return{notion_api_key:e.notion_api_key||"",notion_database_id:e.notion_database_id||"",sync_interval:parseInt(e.sync_interval)||60,auto_sync:Boolean(e.auto_sync),webhook_enabled:Boolean(e.webhook_enabled),webhook_secret:e.webhook_secret||"",default_post_status:e.default_post_status||"draft",default_post_type:e.default_post_type||"post",enable_math_rendering:Boolean(e.enable_math_rendering),enable_mermaid_diagrams:Boolean(e.enable_mermaid_diagrams),api_page_size:parseInt(e.api_page_size)||20,concurrent_requests:parseInt(e.concurrent_requests)||3,batch_size:parseInt(e.batch_size)||10,enable_performance_mode:Boolean(e.enable_performance_mode),enable_caching:Boolean(e.enable_caching),cache_duration:parseInt(e.cache_duration)||3600,delete_protection:Boolean(e.delete_protection),image_optimization:Boolean(e.image_optimization),debug_mode:Boolean(e.debug_mode),log_level:e.log_level||"info"}}},{key:"saveSettings",value:(f=Jt(Wt().m(function e(){var t,n,r,i,u,c=arguments;return Wt().w(function(e){for(;;)switch(e.p=e.n){case 0:if(t=c.length>0&&void 0!==c[0]&&c[0],this.settings){e.n=1;break}throw new Error("没有可保存的设置");case 1:if((n=this.validateSettings(this.settings)).isValid){e.n=2;break}throw r=n.errors.join(", "),t||(0,s.Qg)("设置验证失败: ".concat(r)),new Error(r);case 2:return this.setSaving(!0),e.p=3,e.n=4,(0,a.bE)("notion_to_wordpress_save_settings",this.settings);case 4:if(!(i=e.v)||!i.data){e.n=5;break}return this.hasUnsavedChanges=!1,t||(0,s.Te)("设置保存成功"),(0,o.Ic)("settings:saved",{settings:this.settings}),e.a(2);case 5:throw new Error("保存设置失败");case 6:e.n=8;break;case 7:throw e.p=7,u=e.v,t||(0,s.Qg)("保存设置失败: ".concat(u.message)),u;case 8:return e.p=8,this.setSaving(!1),e.f(8);case 9:return e.a(2)}},e,this,[[3,7,8,9]])})),function(){return f.apply(this,arguments)})},{key:"validateSettings",value:function(e){var t=[],n=[];return this.validationRules.forEach(function(n){var r=e[n.field],o=!0,i=n.message||"".concat(n.field," 验证失败");switch(n.type){case"required":o=null!=r&&""!==r;break;case"number":var a=Number(r);(o=!isNaN(a))&&void 0!==n.min&&(o=a>=n.min),o&&void 0!==n.max&&(o=a<=n.max);break;case"custom":if(n.validator){var s=n.validator(r);"boolean"==typeof s?o=s:(o=!1,i=s)}}o||t.push(i)}),e.debug_mode&&n.push("调试模式已启用，可能影响性能"),e.concurrent_requests>5&&n.push("并发请求数较高，可能触发API限制"),{isValid:0===t.length,errors:t,warnings:n}}},{key:"resetSettings",value:(l=Jt(Wt().m(function e(){var t,n;return Wt().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_reset_settings",{});case 1:if(!(t=e.v)||!t.data){e.n=2;break}return this.settings=this.normalizeSettings(t.data),this.hasUnsavedChanges=!1,(0,o.Ic)("settings:reset",{settings:this.settings}),(0,s.Te)("设置已重置为默认值"),e.a(2);case 2:throw new Error("重置设置失败");case 3:e.n=5;break;case 4:throw e.p=4,n=e.v,(0,s.Qg)("重置设置失败: ".concat(n.message)),n;case 5:return e.a(2)}},e,this,[[0,4]])})),function(){return l.apply(this,arguments)})},{key:"testConnection",value:(c=Jt(Wt().m(function e(){var t,n,r,i,u,c,l;return Wt().w(function(e){for(;;)switch(e.p=e.n){case 0:if(null!==(t=this.settings)&&void 0!==t&&t.notion_api_key&&null!==(n=this.settings)&&void 0!==n&&n.notion_database_id){e.n=1;break}return(0,s.Qg)("请先配置API密钥和数据库ID"),e.a(2,!1);case 1:return e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_test_connection",{api_key:this.settings.notion_api_key,database_id:this.settings.notion_database_id});case 2:if(!((r=e.v)&&r.data&&r.data.success)){e.n=3;break}return(0,s.Te)("连接测试成功"),(0,o.Ic)("settings:connection:success"),e.a(2,!0);case 3:return u=(null==r||null===(i=r.data)||void 0===i?void 0:i.message)||"连接测试失败",(0,s.Qg)("连接测试失败: ".concat(u)),(0,o.Ic)("settings:connection:failed",{message:u}),e.a(2,!1);case 4:e.n=6;break;case 5:return e.p=5,l=e.v,c=l.message,(0,s.Qg)("连接测试失败: ".concat(c)),(0,o.Ic)("settings:connection:failed",{message:c}),e.a(2,!1);case 6:return e.a(2)}},e,this,[[1,5]])})),function(){return c.apply(this,arguments)})},{key:"exportSettings",value:function(){if(this.settings)try{var e=Yt(Yt({},this.settings),{},{notion_api_key:"",webhook_secret:"",exported_at:(new Date).toISOString(),version:"2.0.0"}),t=JSON.stringify(e,null,2),n="notion-wp-settings-".concat((new Date).toISOString().split("T")[0],".json");this.downloadFile(t,n,"application/json"),(0,o.Ic)("settings:exported",{filename:n}),(0,s.Te)("设置已导出")}catch(e){(0,s.Qg)("导出设置失败: ".concat(e.message))}else(0,s.Qg)("没有可导出的设置")}},{key:"importSettings",value:(u=Jt(Wt().m(function e(t){var n,r,i,a,u,c,l;return Wt().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,this.readFileContent(t);case 1:if(i=e.v,a=JSON.parse(i),this.validateImportData(a)){e.n=2;break}throw new Error("导入文件格式不正确");case 2:if(u=Yt(Yt({},a),{},{notion_api_key:(null===(n=this.settings)||void 0===n?void 0:n.notion_api_key)||"",webhook_secret:(null===(r=this.settings)||void 0===r?void 0:r.webhook_secret)||""}),this.settings=this.normalizeSettings(u),(c=this.validateSettings(this.settings)).isValid){e.n=3;break}throw new Error("导入的设置无效: ".concat(c.errors.join(", ")));case 3:this.hasUnsavedChanges=!0,(0,o.Ic)("settings:imported",{settings:this.settings}),(0,s.Te)("设置已导入，请检查并保存"),c.warnings.length>0&&(0,s.cf)("警告: ".concat(c.warnings.join(", "))),e.n=5;break;case 4:throw e.p=4,l=e.v,(0,s.Qg)("导入设置失败: ".concat(l.message)),l;case 5:return e.a(2)}},e,this,[[0,4]])})),function(e){return u.apply(this,arguments)})},{key:"validateImportData",value:function(e){return!(!e||"object"!==Ht(e))&&["sync_interval","default_post_status"].every(function(t){return t in e})}},{key:"readFileContent",value:function(e){return new Promise(function(t,n){var r=new FileReader;r.onload=function(e){var n;return t(null===(n=e.target)||void 0===n?void 0:n.result)},r.onerror=function(){return n(new Error("文件读取失败"))},r.readAsText(e)})}},{key:"downloadFile",value:function(e,t,n){var r=new Blob([e],{type:n}),o=URL.createObjectURL(r),i=document.createElement("a");i.href=o,i.download=t,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(o)}},{key:"updateSetting",value:function(e,t){if(this.settings){var n=this.settings[e];if(this.settings[e]=t,n!==t){if(this.hasUnsavedChanges=!0,this.options.validateOnChange){var r=this.validationRules.filter(function(t){return t.field===e}),i=Yt({},this.settings),a=this.validateSettings(i);if(!a.isValid){var s=a.errors.filter(function(e){return r.some(function(t){return e.includes(t.message||"")})});s.length>0&&(0,o.Ic)("settings:validation:error",{field:e,errors:s})}}(0,o.Ic)("settings:changed",{key:e,value:t,oldValue:n}),this.options.autoSave&&this.debouncedAutoSave&&this.debouncedAutoSave()}}}},{key:"setLoading",value:function(e){this.isLoading=e,(0,o.Ic)("settings:loading:changed",{loading:e})}},{key:"setSaving",value:function(e){this.isSaving=e,(0,o.Ic)("settings:saving:changed",{saving:e})}},{key:"getCurrentSettings",value:function(){return this.settings?Yt({},this.settings):null}},{key:"hasChanges",value:function(){return this.hasUnsavedChanges}},{key:"isLoadingSettings",value:function(){return this.isLoading}},{key:"isSavingSettings",value:function(){return this.isSaving}},{key:"getOptions",value:function(){return Yt({},this.options)}},{key:"updateOptions",value:function(e){this.options=Yt(Yt({},this.options),e),(0,o.Ic)("settings:options:updated",this.options)}},{key:"destroy",value:function(){this.autoSaveTimer&&clearTimeout(this.autoSaveTimer),window.removeEventListener("beforeunload",function(){}),document.removeEventListener("keydown",function(){}),e.instance=null,(0,o.Ic)("settings:manager:destroyed")}}],i=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&Zt(t.prototype,n),i&&Zt(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,i,u,c,l,f,h}();en(nn,"instance",null);var rn=nn.getInstance();function on(e){return on="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},on(e)}function an(e){return function(e){if(Array.isArray(e))return sn(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return sn(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?sn(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function sn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function un(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return cn(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(cn(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,cn(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,cn(f,"constructor",c),cn(c,"constructor",u),u.displayName="GeneratorFunction",cn(c,o,"GeneratorFunction"),cn(f),cn(f,o,"Generator"),cn(f,r,function(){return this}),cn(f,"toString",function(){return"[object Generator]"}),(un=function(){return{w:i,m:h}})()}function cn(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}cn=function(e,t,n,r){function i(t,n){cn(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},cn(e,t,n,r)}function ln(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function fn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function hn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fn(Object(n),!0).forEach(function(t){dn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function pn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,yn(r.key),r)}}function dn(e,t,n){return(t=yn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yn(e){var t=function(e,t){if("object"!=on(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=on(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==on(t)?t:t+""}var vn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),dn(this,"options",void 0),dn(this,"errorHistory",[]),dn(this,"retryConfig",{maxAttempts:3,baseDelay:1e3,maxDelay:3e4,backoffMultiplier:2,retryableErrors:["NETWORK_ERROR","SERVER_ERROR","RATE_LIMIT_ERROR"]}),dn(this,"retryTimers",new Map),e.instance)return e.instance;e.instance=this,this.options=hn({enableGlobalHandling:!0,enableRetry:!0,enableNotifications:!0,enableReporting:!1,maxErrorHistory:100,reportingEndpoint:""},t),this.init()}return t=e,n=[{key:"init",value:function(){this.options.enableGlobalHandling&&this.setupGlobalErrorHandling(),(0,o.Ic)("error:manager:initialized")}},{key:"setupGlobalErrorHandling",value:function(){var e=this;window.addEventListener("error",function(t){e.handleGlobalError(t.error,{filename:t.filename,lineno:t.lineno,colno:t.colno,type:"javascript"})}),window.addEventListener("unhandledrejection",function(t){e.handleGlobalError(t.reason,{type:"promise_rejection"}),t.preventDefault()})}},{key:"handleGlobalError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.isOurError(e,t)){var n=this.createErrorInfo(e,t);this.handleError(n)}}},{key:"isOurError",value:function(e,t){var n=t.filename||"",r=(null==e?void 0:e.stack)||"";return n.includes("notion-to-wordpress")||r.includes("notion-to-wordpress")||"manual"===t.type}},{key:"createErrorInfo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{id:this.generateErrorId(),type:this.classifyError(e),severity:this.determineSeverity(e),message:this.extractMessage(e),originalError:e,context:t,timestamp:Date.now(),stack:null==e?void 0:e.stack,userAgent:navigator.userAgent,url:window.location.href,retryCount:0,resolved:!1}}},{key:"generateErrorId",value:function(){return"error_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))}},{key:"classifyError",value:function(e){var t=String((null==e?void 0:e.message)||e||"").toLowerCase();return t.includes("filter")&&t.includes("validation")?"FILTER_ERROR":t.includes("unauthorized")||t.includes("auth")||t.includes("api key")||t.includes("token")?"AUTH_ERROR":t.includes("rate limit")||t.includes("too many requests")||t.includes("quota exceeded")?"RATE_LIMIT_ERROR":t.includes("network")||t.includes("timeout")||t.includes("connection")||t.includes("fetch")?"NETWORK_ERROR":t.includes("server error")||t.includes("internal error")||(null==e?void 0:e.status)>=500?"SERVER_ERROR":t.includes("bad request")||t.includes("invalid")||(null==e?void 0:e.status)>=400&&(null==e?void 0:e.status)<500?"CLIENT_ERROR":t.includes("validation")||t.includes("invalid format")||t.includes("required field")?"VALIDATION_ERROR":t.includes("permission")||t.includes("forbidden")||t.includes("access denied")?"PERMISSION_ERROR":t.includes("data")||t.includes("parse")||t.includes("json")||t.includes("format")?"DATA_ERROR":"UNKNOWN_ERROR"}},{key:"determineSeverity",value:function(e){switch(this.classifyError(e)){case"AUTH_ERROR":case"PERMISSION_ERROR":return"critical";case"SERVER_ERROR":case"DATA_ERROR":return"high";case"RATE_LIMIT_ERROR":case"NETWORK_ERROR":case"VALIDATION_ERROR":return"medium";default:return"low"}}},{key:"extractMessage",value:function(e){var t;return"string"==typeof e?e:null!=e&&e.message?e.message:null!=e&&null!==(t=e.data)&&void 0!==t&&t.message?e.data.message:"未知错误"}},{key:"handleError",value:function(e){this.addToHistory(e),this.logError(e),this.executeErrorStrategy(e),(0,o.Ic)("error:handled",{errorInfo:e})}},{key:"addToHistory",value:function(e){this.errorHistory.unshift(e),this.errorHistory.length>this.options.maxErrorHistory&&(this.errorHistory=this.errorHistory.slice(0,this.options.maxErrorHistory))}},{key:"logError",value:function(e){var t={id:e.id,type:e.type,severity:e.severity,message:e.message,context:e.context,timestamp:e.timestamp,stack:e.stack,url:e.url};this.options.enableReporting&&this.reportError(t).catch(console.error)}},{key:"executeErrorStrategy",value:function(e){switch(e.type){case"RATE_LIMIT_ERROR":this.handleRateLimitError(e);break;case"AUTH_ERROR":this.handleAuthError(e);break;case"NETWORK_ERROR":this.handleNetworkError(e);break;case"SERVER_ERROR":this.handleServerError(e);break;case"VALIDATION_ERROR":this.handleValidationError(e);break;default:this.handleGenericError(e)}}},{key:"handleRateLimitError",value:function(e){this.options.enableNotifications&&(0,s.I9)("请求过于频繁，请稍后重试"),this.options.enableRetry&&this.scheduleRetry(e,6e4)}},{key:"handleAuthError",value:function(e){this.options.enableNotifications&&(0,s.Qg)("认证失败，请检查API密钥配置"),this.notifyAdmin("认证失败",e)}},{key:"handleNetworkError",value:function(e){this.options.enableNotifications&&(0,s.Qg)("网络连接失败，请检查网络连接"),this.options.enableRetry&&this.scheduleRetry(e,5e3)}},{key:"handleServerError",value:function(e){this.options.enableNotifications&&(0,s.Qg)("服务器错误，请稍后重试"),this.options.enableRetry&&this.scheduleRetry(e,1e4)}},{key:"handleValidationError",value:function(e){this.options.enableNotifications&&(0,s.Qg)("数据验证失败: ".concat(e.message))}},{key:"handleGenericError",value:function(e){this.options.enableNotifications&&"low"!==e.severity&&(0,s.Qg)("发生了一个错误，请刷新页面重试")}},{key:"scheduleRetry",value:function(e,t){var n=this;if(this.retryConfig.retryableErrors.includes(e.type)){var r=e.retryCount||0;if(!(r>=this.retryConfig.maxAttempts)){var o=Math.min(t*Math.pow(this.retryConfig.backoffMultiplier,r),this.retryConfig.maxDelay),i=setTimeout(function(){n.executeRetry(e),n.retryTimers.delete(e.id)},o);this.retryTimers.set(e.id,i)}}}},{key:"executeRetry",value:function(e){var t=(e.retryCount||0)+1;e.retryCount=t,(0,o.Ic)("error:retry",{errorInfo:e,retryCount:t})}},{key:"notifyAdmin",value:function(e,t){(0,o.Ic)("error:admin:notify",{message:e,errorInfo:t})}},{key:"reportError",value:(i=un().m(function e(t){return un().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_report_error",t);case 1:e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,null,[[0,2]])}),u=function(){var e=this,t=arguments;return new Promise(function(n,r){var o=i.apply(e,t);function a(e){ln(o,n,r,a,s,"next",e)}function s(e){ln(o,n,r,a,s,"throw",e)}a(void 0)})},function(e){return u.apply(this,arguments)})},{key:"reportManualError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.createErrorInfo(e,hn(hn({},t),{},{type:"manual"}));return this.handleError(n),n.id}},{key:"resolveError",value:function(e){var t=this.errorHistory.find(function(t){return t.id===e});t&&(t.resolved=!0,(0,o.Ic)("error:resolved",{errorInfo:t}))}},{key:"getErrorHistory",value:function(){return an(this.errorHistory)}},{key:"getUnresolvedErrors",value:function(){return this.errorHistory.filter(function(e){return!e.resolved})}},{key:"getErrorStats",value:function(){var e={total:this.errorHistory.length,unresolved:this.getUnresolvedErrors().length,byType:{},bySeverity:{},recentErrors:this.errorHistory.slice(0,10)};return this.errorHistory.forEach(function(t){e.byType[t.type]=(e.byType[t.type]||0)+1,e.bySeverity[t.severity]=(e.bySeverity[t.severity]||0)+1}),e}},{key:"clearErrorHistory",value:function(){this.errorHistory=[],(0,o.Ic)("error:history:cleared")}},{key:"updateRetryConfig",value:function(e){this.retryConfig=hn(hn({},this.retryConfig),e),(0,o.Ic)("error:retry:config:updated",this.retryConfig)}},{key:"getOptions",value:function(){return hn({},this.options)}},{key:"updateOptions",value:function(e){this.options=hn(hn({},this.options),e),(0,o.Ic)("error:options:updated",this.options)}},{key:"destroy",value:function(){this.retryTimers.forEach(function(e){return clearTimeout(e)}),this.retryTimers.clear(),this.options.enableGlobalHandling&&(window.removeEventListener("error",this.handleGlobalError),window.removeEventListener("unhandledrejection",this.handleGlobalError)),e.instance=null,(0,o.Ic)("error:manager:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&pn(t.prototype,n),r&&pn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u}();dn(vn,"instance",null);var mn=vn.getInstance();function bn(e){return bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bn(e)}function gn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||wn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wn(e,t){if(e){if("string"==typeof e)return kn(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?kn(e,t):void 0}}function kn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Sn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function On(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,En(r.key),r)}}function Tn(e,t,n){return(t=En(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function En(e){var t=function(e,t){if("object"!=bn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=bn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==bn(t)?t:t+""}var jn=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Tn(this,"components",new Map),Tn(this,"registry",{}),Tn(this,"initialized",!1),this.registerBuiltinComponents(),this.setupEventListeners()},t=[{key:"registerBuiltinComponents",value:function(){this.registry={"sync-button":Re,"status-display":Je,"form-component":Xe.s,"tab-manager":ct}}},{key:"setupEventListeners",value:function(){var e=this;o.Bt.on("component:mount",function(e,t){}),o.Bt.on("component:unmount",function(e,t){}),o.Bt.on("component:destroy",function(t,n){e.removeComponentFromRegistry(n.component)}),window.addEventListener("beforeunload",function(){e.destroyAll()})}},{key:"init",value:function(){this.initialized||(this.initialized=!0,this.autoDiscoverComponents())}},{key:"autoDiscoverComponents",value:function(){this.discoverSyncButtons(),this.discoverStatusDisplays(),this.discoverFormComponents(),this.discoverTabManagers()}},{key:"discoverSyncButtons",value:function(){var e=this;document.querySelectorAll("[data-sync-type]").forEach(function(t,n){var r=t,o=r.getAttribute("data-sync-type"),i=r.hasAttribute("data-incremental"),a=r.hasAttribute("data-check-deletions"),s=r.getAttribute("data-confirm"),u="sync-button-".concat(o,"-").concat(n),c=new Re({element:r,syncType:o,incremental:i,checkDeletions:a,confirmMessage:s||void 0});e.components.set(u,c)})}},{key:"discoverStatusDisplays",value:function(){var e=this;document.querySelectorAll("[data-status-type]").forEach(function(t,n){var r=t,o=r.getAttribute("data-status-type"),i=parseInt(r.getAttribute("data-refresh-interval")||"5000"),a=!r.hasAttribute("data-no-auto-refresh"),s="status-display-".concat(o,"-").concat(n),u=new Je({element:r,type:o,refreshInterval:i,autoRefresh:a});e.components.set(s,u)})}},{key:"discoverFormComponents",value:function(){var e=this;document.querySelectorAll('form[data-component="form"]').forEach(function(t,n){var r=t,o=!r.hasAttribute("data-no-validate-input"),i=!r.hasAttribute("data-no-validate-blur"),a=r.hasAttribute("data-submit-enter"),s=r.hasAttribute("data-auto-save"),u=parseInt(r.getAttribute("data-auto-save-delay")||"2000"),c="form-component-".concat(n),l=new Xe.s({element:r,validateOnInput:o,validateOnBlur:i,submitOnEnter:a,autoSave:s,autoSaveDelay:u});e.components.set(c,l)})}},{key:"discoverTabManagers",value:function(){var e=this;document.querySelectorAll('[data-component="tab-manager"]').forEach(function(t,n){var r=t,o=r.getAttribute("data-active-class")||"active",i=r.getAttribute("data-content-class")||"tab-content",a=!r.hasAttribute("data-no-save-tab"),s=r.getAttribute("data-storage-key")||"active_tab",u=parseInt(r.getAttribute("data-animation-duration")||"300"),c=r.getAttribute("data-default-tab")||void 0,l="tab-manager-".concat(n),f=new ct({element:r,activeTabClass:o,tabContentClass:i,saveActiveTab:a,storageKey:s,animationDuration:u,defaultTab:c});e.components.set(l,f)})}},{key:"registerComponent",value:function(e,t){this.registry[e]=t}},{key:"createComponent",value:function(e,t){var n=this.registry[e];if(!n)return null;try{var r=new n(t),o=this.generateComponentId(e);return this.components.set(o,r),r}catch(e){return null}}},{key:"createComponents",value:function(e){var t=this,n=[];return e.forEach(function(e){var r=t.createComponent(e.type,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Sn(Object(n),!0).forEach(function(t){Tn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Sn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({selector:e.selector,autoInit:!1!==e.autoInit},e.options));r&&n.push(r)}),n}},{key:"getComponent",value:function(e){return this.components.get(e)}},{key:"getComponentsByType",value:function(e){return Array.from(this.components.values()).filter(function(t){return t.constructor.name.toLowerCase().includes(e.toLowerCase())})}},{key:"destroyComponent",value:function(e){var t=this.components.get(e);t&&(t.destroy(),this.components.delete(e))}},{key:"destroyAll",value:function(){this.components.forEach(function(e,t){try{e.destroy()}catch(e){}}),this.components.clear(),this.initialized=!1}},{key:"reinitialize",value:function(){this.destroyAll(),this.init()}},{key:"removeComponentFromRegistry",value:function(e){var t,n=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=wn(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}(this.components.entries());try{for(n.s();!(t=n.n()).done;){var r=gn(t.value,2),o=r[0];if(r[1]===e){this.components.delete(o);break}}}catch(e){n.e(e)}finally{n.f()}}},{key:"generateComponentId",value:function(e){var t=Date.now(),n=Math.random().toString(36).substring(2,8);return"".concat(e,"-").concat(t,"-").concat(n)}},{key:"getAllComponents",value:function(){return new Map(this.components)}},{key:"getComponentCount",value:function(){return this.components.size}},{key:"hasComponent",value:function(e){return this.components.has(e)}},{key:"getRegisteredTypes",value:function(){return Object.keys(this.registry)}},{key:"isInitialized",value:function(){return this.initialized}}],t&&On(e.prototype,t),n&&On(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}());function _n(e){return _n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_n(e)}function Pn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,In(r.key),r)}}function In(e){var t=function(e,t){if("object"!=_n(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=_n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==_n(t)?t:t+""}var Cn=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},n=[{key:"debounce",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=null;return function(){for(var o=this,i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];var u=n&&!r;r&&clearTimeout(r),r=setTimeout(function(){r=null,n||e.apply(o,a)},t),u&&e.apply(this,a)}}},{key:"throttle",value:function(e,t){var n=!1;return function(){if(!n){for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];e.apply(this,o),n=!0,setTimeout(function(){n=!1},t)}}}},{key:"handleAjaxError",value:function(e,t,n){var r,o="网络请求失败";null!==(r=e.responseJSON)&&void 0!==r&&null!==(r=r.data)&&void 0!==r&&r.message?o=e.responseJSON.data.message:n?o=n:"timeout"===t?o="请求超时，请稍后重试":"abort"===t?o="请求被取消":0===e.status?o="网络连接失败，请检查网络":e.status>=500?o="服务器内部错误":e.status>=400&&(o="请求错误"),(0,s.Qg)(o)}},{key:"setButtonLoading",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.loadingText||"处理中...";if(t)e.dataset.originalText||(e instanceof HTMLInputElement?e.dataset.originalText=e.value:e.dataset.originalText=e.textContent||""),e.disabled=!0,e.classList.add("loading"),e instanceof HTMLInputElement?e.value=r:e.textContent=r;else{e.disabled=!1,e.classList.remove("loading");var o=n.originalText||e.dataset.originalText;o&&(e instanceof HTMLInputElement?e.value=o:e.textContent=o),delete e.dataset.originalText}}},{key:"updateProgress",value:function(e,t){var n=document.getElementById("sync-progress");if(n){var r=n.querySelector(".progress-fill"),o=n.querySelector(".current-step"),i=n.querySelector(".progress-percentage");n.classList.contains("notion-wp-hidden")&&(n.classList.remove("notion-wp-hidden"),n.style.display="block"),r&&(r.style.width="".concat(e,"%")),o&&t&&(o.textContent=t),i&&(i.textContent="".concat(Math.round(e),"%"))}}},{key:"hideProgress",value:function(){var e=document.getElementById("sync-progress");if(e){e.style.display="none",e.classList.add("notion-wp-hidden");var t=e.querySelector(".progress-fill"),n=e.querySelector(".current-step"),r=e.querySelector(".progress-percentage");t&&(t.style.width="0%"),n&&(n.textContent="准备同步..."),r&&(r.textContent="0%")}}},{key:"setSyncButtonState",value:function(e,t,n){switch(e.classList.remove("loading","success","error"),t){case"loading":this.setButtonLoading(e,!0);break;case"success":if(this.setButtonLoading(e,!1),e.classList.add("success"),n){var r=e.querySelector(".button-text");r&&(r.textContent=n)}setTimeout(function(){e.classList.remove("success");var t=e.dataset.originalText;if(t){var n=e.querySelector(".button-text");n&&(n.textContent=t)}},3e3);break;case"error":if(this.setButtonLoading(e,!1),e.classList.add("error"),n){var o=e.querySelector(".button-text");o&&(o.textContent=n)}setTimeout(function(){e.classList.remove("error");var t=e.dataset.originalText;if(t){var n=e.querySelector(".button-text");n&&(n.textContent=t)}},3e3);break;default:this.setButtonLoading(e,!1)}}},{key:"validateInput",value:function(e,t){var n=e.value.trim(),r={isValid:!1,message:"",level:"error"};switch(t){case"api-key":n?n.length<30||n.length>80?(r.message="API密钥长度可能不正确，请检查是否完整",r.level="warning"):/^[a-zA-Z0-9_-]+$/.test(n)?(r.message="API密钥格式正确",r.level="success",r.isValid=!0):(r.message="API密钥格式可能不正确，应只包含字母、数字、下划线和连字符",r.level="warning"):r.message="API密钥不能为空";break;case"database-id":n?32!==n.length?r.message="数据库ID长度应为32位字符":/^[a-f0-9]{32}$/i.test(n)?(r.message="数据库ID格式正确",r.level="success",r.isValid=!0):r.message="数据库ID格式不正确，应为32位十六进制字符":r.message="数据库ID不能为空"}return this.updateValidationFeedback(e,r),r}},{key:"updateValidationFeedback",value:function(e,t){var n=e.closest(".input-with-validation");if(n){var r=n.querySelector(".validation-feedback");r&&(r.className="validation-feedback ".concat(t.level),r.textContent=t.message,e.className=e.className.replace(/\b(valid|invalid|warning)\b/g,""),t.isValid?e.classList.add("valid"):"warning"===t.level?e.classList.add("warning"):e.classList.add("invalid"))}}},{key:"formatDateTime",value:function(e){if(!e)return"从未";if(e.includes(" ")){var t=e.indexOf(" ");return e.slice(0,t)+"<br>"+e.slice(t+1)}return e}},{key:"generateId",value:function(){return"".concat(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"id","_").concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11))}},{key:"safeJsonParse",value:function(e,t){try{return JSON.parse(e)}catch(e){return t}}},{key:"isElementInViewport",value:function(e){var t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}}],(t=null)&&Pn(e.prototype,t),n&&Pn(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();function xn(e){return xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xn(e)}function An(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Ln(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Ln(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Ln(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Ln(f,"constructor",c),Ln(c,"constructor",u),u.displayName="GeneratorFunction",Ln(c,o,"GeneratorFunction"),Ln(f),Ln(f,o,"Generator"),Ln(f,r,function(){return this}),Ln(f,"toString",function(){return"[object Generator]"}),(An=function(){return{w:i,m:h}})()}function Ln(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Ln=function(e,t,n,r){function i(t,n){Ln(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Ln(e,t,n,r)}function Dn(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Rn(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Dn(i,r,o,a,s,"next",e)}function s(e){Dn(i,r,o,a,s,"throw",e)}a(void 0)})}}function Mn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Nn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Bn(r.key),r)}}function Fn(e,t,n){return(t=Bn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Bn(e){var t=function(e,t){if("object"!=xn(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=xn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==xn(t)?t:t+""}var Gn=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Fn(this,"STORAGE_KEY","notion_wp_sync_status"),Fn(this,"CHECK_INTERVAL_VISIBLE",5e3),Fn(this,"CHECK_INTERVAL_HIDDEN",3e4),Fn(this,"STATUS_EXPIRE_TIME",36e5),Fn(this,"checkTimer",null),Fn(this,"isPageVisible",!0),Fn(this,"currentSyncId",null),e.instance)return e.instance;e.instance=this,this.init()}return t=e,n=[{key:"init",value:function(){this.setupVisibilityHandling(),this.restoreSyncStatus(),this.startStatusMonitoring()}},{key:"setupVisibilityHandling",value:function(){var e=this;document.addEventListener("visibilitychange",function(){e.isPageVisible=!document.hidden,e.isPageVisible?(e.checkSyncStatus(),e.adjustCheckInterval()):e.adjustCheckInterval()}),window.addEventListener("focus",function(){e.checkSyncStatus()})}},{key:"saveSyncStatus",value:function(e){var t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mn(Object(n),!0).forEach(function(t){Fn(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({isActive:!0,syncType:e.syncType||"unknown",startTime:Date.now(),syncId:this.generateSyncId()},e);this.currentSyncId=t.syncId,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t)),(0,o.Ic)("sync:status:saved",t)}},{key:"clearSyncStatus",value:function(){localStorage.removeItem(this.STORAGE_KEY),this.currentSyncId=null,(0,o.Ic)("sync:status:cleared")}},{key:"restoreSyncStatus",value:function(){var e=localStorage.getItem(this.STORAGE_KEY);if(e)try{var t=Cn.safeJsonParse(e,{});if(Date.now()-t.startTime>this.STATUS_EXPIRE_TIME)return void this.clearSyncStatus();this.currentSyncId=t.syncId,this.showSyncStatusRecovery(t)}catch(e){this.clearSyncStatus()}}},{key:"showSyncStatusRecovery",value:function(e){var t=this,n=Math.floor((Date.now()-e.startTime)/1e3),r=n<60?"".concat(n,"秒"):"".concat(Math.floor(n/60),"分").concat(n%60,"秒"),i=document.createElement("div");i.className="notice notice-info is-dismissible",i.id="sync-status-recovery",i.innerHTML="\n      <p>\n        <strong>🔄 检测到进行中的同步操作</strong><br>\n        同步类型：".concat(e.syncType||"未知","<br>\n        已运行：").concat(r,'<br>\n        <button type="button" class="button button-secondary" id="check-sync-status-now">立即检查状态</button>\n        <button type="button" class="button button-link" id="clear-sync-status">清除状态</button>\n      </p>\n    ');var a=document.querySelector(".wrap.notion-wp-admin");a&&a.insertBefore(i,a.firstChild);var s=document.getElementById("check-sync-status-now"),u=document.getElementById("clear-sync-status");s&&s.addEventListener("click",function(){t.checkSyncStatus(),i.style.display="none"}),u&&u.addEventListener("click",function(){t.clearSyncStatus(),i.style.display="none"}),(0,o.Ic)("sync:status:recovery:shown",e)}},{key:"generateSyncId",value:function(){return Cn.generateId("sync")}},{key:"adjustCheckInterval",value:function(){var e=this;this.checkTimer&&clearInterval(this.checkTimer);var t=this.isPageVisible?this.CHECK_INTERVAL_VISIBLE:this.CHECK_INTERVAL_HIDDEN;this.checkTimer=setInterval(function(){e.checkSyncStatus()},t)}},{key:"startStatusMonitoring",value:function(){this.adjustCheckInterval(),(0,o.Ic)("sync:monitoring:started")}},{key:"stopStatusMonitoring",value:function(){this.checkTimer&&(clearInterval(this.checkTimer),this.checkTimer=null),(0,o.Ic)("sync:monitoring:stopped")}},{key:"checkSyncStatus",value:(c=Rn(An().m(function e(){return An().w(function(e){for(;;)switch(e.p=e.n){case 0:if(this.currentSyncId){e.n=1;break}return e.a(2);case 1:return e.p=2,e.n=3,this.refreshAsyncStatus();case 3:return e.n=4,this.refreshQueueStatus();case 4:e.n=6;break;case 5:e.p=5,e.v;case 6:return e.a(2)}},e,this,[[2,5]])})),function(){return c.apply(this,arguments)})},{key:"refreshAsyncStatus",value:(u=Rn(An().m(function e(){var t,n;return An().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_async_status",{});case 1:(t=e.v).data.success?(this.updateAsyncStatusDisplay(t.data.data.status),"idle"===(null===(n=t.data.data.status)||void 0===n?void 0:n.status)&&this.clearSyncStatus(),(0,o.Ic)("sync:async:status:updated",t.data.data.status)):this.showStatusError("async","获取异步状态失败: "+(t.data.message||"未知错误")),e.n=3;break;case 2:e.p=2,e.v,this.showStatusError("async","网络错误，无法获取异步状态");case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return u.apply(this,arguments)})},{key:"refreshQueueStatus",value:(i=Rn(An().m(function e(){var t;return An().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_queue_status",{});case 1:(t=e.v).data.success?(this.updateQueueStatusDisplay(t.data.data.status),(0,o.Ic)("sync:queue:status:updated",t.data.data.status)):this.showStatusError("queue","获取队列状态失败: "+(t.data.message||"未知错误")),e.n=3;break;case 2:e.p=2,e.v,this.showStatusError("queue","网络错误，无法获取队列状态");case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return i.apply(this,arguments)})},{key:"updateAsyncStatusDisplay",value:function(e){var t=document.getElementById("async-status-container");if(t){var n=t.querySelector(".async-status-display");if(n){n.classList.remove("status-idle","status-running","status-paused","status-error");var r="status-idle",o="空闲";switch("object"===xn(e)?e.status:e){case"running":r="status-running",o="运行中";break;case"paused":r="status-paused",o="已暂停";break;case"error":r="status-error",o="错误"}n.classList.add(r);var i=n.querySelector(".status-value");i&&(i.textContent=o),"object"===xn(e)&&e.operation}}}},{key:"updateQueueStatusDisplay",value:function(e){var t=document.getElementById("queue-status-container");if(t){var n=function(e,n){var r=t.querySelector(e);r&&(r.textContent=n.toString())};n(".queue-pending",e.pending||0),n(".queue-processing",e.processing||0),n(".queue-completed",e.completed||0),n(".queue-failed",e.failed||0)}}},{key:"showStatusError",value:function(e,t){var n="async"===e?"async-status-container":"queue-status-container",r=document.getElementById(n);r&&(r.innerHTML='\n        <div class="error-message" style="color: #d63638; padding: 10px; background: #fef7f7; border: 1px solid #d63638; border-radius: 4px;">\n          '.concat(t,"\n        </div>\n      ")),(0,s.Qg)(t)}},{key:"getCurrentSyncId",value:function(){return this.currentSyncId}},{key:"hasActivSync",value:function(){return null!==this.currentSyncId}},{key:"destroy",value:function(){this.stopStatusMonitoring(),e.instance=null}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],n&&Nn(t.prototype,n),r&&Nn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c}();Fn(Gn,"instance",null);var Vn=Gn.getInstance();function Un(e){return Un="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Un(e)}function qn(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return zn(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(zn(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,zn(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,zn(f,"constructor",c),zn(c,"constructor",u),u.displayName="GeneratorFunction",zn(c,o,"GeneratorFunction"),zn(f),zn(f,o,"Generator"),zn(f,r,function(){return this}),zn(f,"toString",function(){return"[object Generator]"}),(qn=function(){return{w:i,m:h}})()}function zn(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}zn=function(e,t,n,r){function i(t,n){zn(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},zn(e,t,n,r)}function Qn(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function Hn(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Qn(i,r,o,a,s,"next",e)}function s(e){Qn(i,r,o,a,s,"throw",e)}a(void 0)})}}function Wn(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||$n(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Kn(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=$n(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}function $n(e,t){if(e){if("string"==typeof e)return Jn(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Jn(e,t):void 0}}function Jn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Xn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Zn(r.key),r)}}function Yn(e,t,n){return(t=Zn(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zn(e){var t=function(e,t){if("object"!=Un(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Un(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Un(t)?t:t+""}var er=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Yn(this,"forms",new Map),Yn(this,"originalValues",new Map),e.instance)return e.instance;e.instance=this,this.init()}return t=e,n=[{key:"init",value:function(){this.setupFormHandlers(),this.setupValidationHandlers(),this.setupSpecialHandlers()}},{key:"setupFormHandlers",value:function(){var e=this,t=document.getElementById("notion-to-wordpress-settings-form");t&&(this.registerForm("settings",t),this.storeOriginalValues("settings",t),t.addEventListener("submit",function(n){n.preventDefault(),e.handleSettingsFormSubmit(t)}))}},{key:"setupValidationHandlers",value:function(){var e=document.getElementById("notion_to_wordpress_api_key");if(e){var t=Cn.debounce(function(){Cn.validateInput(e,"api-key")},500);e.addEventListener("input",t),e.addEventListener("blur",t)}var n=document.getElementById("notion_to_wordpress_database_id");if(n){var r=Cn.debounce(function(){Cn.validateInput(n,"database-id")},500);n.addEventListener("input",r),n.addEventListener("blur",r)}}},{key:"setupSpecialHandlers",value:function(){var e=this,t=document.getElementById("notion_to_wordpress_auto_import");if(t){var n=document.getElementById("auto_import_schedule_field"),r=Cn.debounce(function(){n&&(t.checked?n.style.display="block":n.style.display="none")},200);t.addEventListener("change",r),r()}var o=document.getElementById("refresh-verification-token");if(o){var i=Cn.debounce(function(t){t.preventDefault(),e.handleRefreshVerificationToken(o)},500);o.addEventListener("click",i)}}},{key:"registerForm",value:function(e,t){this.forms.set(e,t),(0,o.Ic)("form:registered",{id:e,form:t})}},{key:"storeOriginalValues",value:function(e,t){var n,r={},o=Kn(new FormData(t).entries());try{for(o.s();!(n=o.n()).done;){var i=Wn(n.value,2),a=i[0],s=i[1];r[a]=s}}catch(e){o.e(e)}finally{o.f()}this.originalValues.set(e,r)}},{key:"hasFormChanged",value:function(e){var t=this.forms.get(e),n=this.originalValues.get(e);if(!t||!n)return!1;for(var r=new FormData(t),o=0,i=Object.entries(n);o<i.length;o++){var a=Wn(i[o],2),s=a[0],u=a[1];if(r.get(s)!==u)return!0}return!1}},{key:"handleSettingsFormSubmit",value:(c=Hn(qn().m(function e(t){var n,r,i,a,u,c,l,f,h,p,d,y,v,m,b,g,w,k;return qn().w(function(e){for(;;)switch(e.p=e.n){case 0:if(i=document.getElementById("notion-save-settings")){e.n=1;break}return(0,s.Qg)("无法找到保存按钮，请刷新页面重试"),e.a(2);case 1:if(!i.disabled){e.n=2;break}return e.a(2);case 2:if(a=document.getElementById("notion_to_wordpress_api_key"),u=document.getElementById("notion_to_wordpress_database_id"),null!=a&&a.value&&null!=u&&u.value){e.n=3;break}return(0,s.Qg)("请填写必填字段"),null!=a&&a.value||null==a||a.classList.add("error"),null!=u&&u.value||null==u||u.classList.add("error"),setTimeout(function(){document.querySelectorAll(".error").forEach(function(e){return e.classList.remove("error")})},2e3),e.a(2);case 3:return c=this.originalValues.get("settings")||{},l=null===(n=document.getElementById("plugin_language"))||void 0===n?void 0:n.value,f=null===(r=document.getElementById("webhook_enabled"))||void 0===r?void 0:r.checked,Cn.setButtonLoading(i,!0,{loadingText:"保存中..."}),e.p=4,(h=new FormData(t)).set("action","notion_to_wordpress_save_settings"),(p=t.querySelector('input[name="notion_to_wordpress_options_nonce"]'))&&!h.has("notion_to_wordpress_options_nonce")&&h.set("notion_to_wordpress_options_nonce",p.value),e.n=5,this.submitFormData(h);case 5:if(!(d=e.v).success){e.n=6;break}y=c.plugin_language!==l,v=c.webhook_enabled!==f,(m=y||v)?(b=[],y&&b.push("语言设置"),v&&b.push("Webhook设置"),(0,s.Te)("设置保存成功！页面即将刷新以应用".concat(b.join("和"),"变更...")),setTimeout(function(){window.location.reload()},2e3)):((0,s.Te)((null===(g=d.data)||void 0===g?void 0:g.message)||"设置保存成功"),this.storeOriginalValues("settings",t)),(0,o.Ic)("form:settings:saved",{response:d,needsRefresh:m}),e.n=7;break;case 6:throw new Error((null===(w=d.data)||void 0===w?void 0:w.message)||"保存失败");case 7:e.n=9;break;case 8:e.p=8,k=e.v,(0,s.Qg)("保存失败: ".concat(k.message)),(0,o.Ic)("form:settings:error",k);case 9:return e.p=9,Cn.setButtonLoading(i,!1),e.f(9);case 10:return e.a(2)}},e,this,[[4,8,9,10]])})),function(e){return c.apply(this,arguments)})},{key:"submitFormData",value:(u=Hn(qn().m(function e(t){return qn().w(function(e){for(;;)if(0===e.n)return e.a(2,new Promise(function(e,n){var r,o=new XMLHttpRequest;o.open("POST",(null===(r=window.notionToWp)||void 0===r?void 0:r.ajax_url)||"/wp-admin/admin-ajax.php"),o.onload=function(){try{var t=JSON.parse(o.responseText);e(t)}catch(e){n(new Error("响应解析失败"))}},o.onerror=function(){n(new Error("网络请求失败"))},o.ontimeout=function(){n(new Error("请求超时"))},o.timeout=3e4,o.send(t)}))},e)})),function(e){return u.apply(this,arguments)})},{key:"handleRefreshVerificationToken",value:(i=Hn(qn().m(function e(t){var n,r,i;return qn().w(function(e){for(;;)switch(e.p=e.n){case 0:if(!t.disabled){e.n=1;break}return e.a(2);case 1:return n=document.getElementById("verification_token"),Cn.setButtonLoading(t,!0,{loadingText:"刷新中..."}),e.p=2,e.n=3,(0,a.bE)("notion_to_wordpress_refresh_verification_token",{});case 3:if(!(r=e.v).data.success){e.n=4;break}n&&(n.value=r.data.data.verification_token||""),r.data.data.verification_token?(0,s.Te)(r.data.data.message||"验证令牌已更新"):(0,s.cf)("没有新的验证令牌"),(0,o.Ic)("form:token:refreshed",r.data.data),e.n=5;break;case 4:throw new Error(r.data.data.message||"刷新失败");case 5:e.n=7;break;case 6:e.p=6,i=e.v,(0,s.Qg)("刷新失败: ".concat(i.message)),(0,o.Ic)("form:token:error",i);case 7:return e.p=7,Cn.setButtonLoading(t,!1),e.f(7);case 8:return e.a(2)}},e,null,[[2,6,7,8]])})),function(e){return i.apply(this,arguments)})},{key:"validateForm",value:function(e){var t=this.forms.get(e);if(!t)return!1;var n=!0;return t.querySelectorAll("input[required], select[required], textarea[required]").forEach(function(e){var t=e;t.value.trim()?t.classList.remove("error"):(t.classList.add("error"),n=!1)}),n}},{key:"resetForm",value:function(e){var t=this.forms.get(e);t&&(t.reset(),t.querySelectorAll(".error, .valid, .invalid, .warning").forEach(function(e){e.classList.remove("error","valid","invalid","warning")}),(0,o.Ic)("form:reset",{formId:e,form:t}))}},{key:"getFormData",value:function(e){var t=this.forms.get(e);if(!t)return null;var n,r={},o=Kn(new FormData(t).entries());try{for(o.s();!(n=o.n()).done;){var i=Wn(n.value,2),a=i[0],s=i[1];r[a]=s}}catch(e){o.e(e)}finally{o.f()}return r}},{key:"setFormData",value:function(e,t){var n=this.forms.get(e);n&&(Object.entries(t).forEach(function(e){var t=Wn(e,2),r=t[0],o=t[1],i=n.querySelector('[name="'.concat(r,'"]'));i&&("checkbox"===i.type?i.checked=Boolean(o):i.value=String(o))}),(0,o.Ic)("form:data:set",{formId:e,data:t}))}},{key:"destroy",value:function(){this.forms.clear(),this.originalValues.clear(),e.instance=null}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],n&&Xn(t.prototype,n),r&&Xn(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c}();Yn(er,"instance",null);var tr=er.getInstance();function nr(e){return nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nr(e)}function rr(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return or(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(or(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,or(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,or(f,"constructor",c),or(c,"constructor",u),u.displayName="GeneratorFunction",or(c,o,"GeneratorFunction"),or(f),or(f,o,"Generator"),or(f,r,function(){return this}),or(f,"toString",function(){return"[object Generator]"}),(rr=function(){return{w:i,m:h}})()}function or(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}or=function(e,t,n,r){function i(t,n){or(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},or(e,t,n,r)}function ir(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function ar(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ir(i,r,o,a,s,"next",e)}function s(e){ir(i,r,o,a,s,"throw",e)}a(void 0)})}}function sr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,cr(r.key),r)}}function ur(e,t,n){return(t=cr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function cr(e){var t=function(e,t){if("object"!=nr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=nr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nr(t)?t:t+""}var lr=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ur(this,"refreshTimer",null),ur(this,"autoRefreshInterval",3e4),ur(this,"isAutoRefreshEnabled",!1),e.instance)return e.instance;e.instance=this,this.init()}return t=e,n=[{key:"init",value:function(){this.setupEventHandlers(),this.fetchStats()}},{key:"setupEventHandlers",value:function(){var e=this;document.addEventListener("click",function(t){var n=t.target;n.classList.contains("refresh-single")&&(t.preventDefault(),e.handleRefreshSingle(n))});var t=document.getElementById("refresh-stats");t&&t.addEventListener("click",function(t){t.preventDefault(),e.fetchStats()});var n=document.getElementById("auto-refresh-stats");n&&n.addEventListener("change",function(){e.toggleAutoRefresh(n.checked)})}},{key:"fetchStats",value:(l=ar(rr().m(function e(){var t,n,r,i;return rr().w(function(e){for(;;)switch(e.p=e.n){case 0:return(t=document.querySelectorAll(".notion-stats-grid .stat-card h3, .notion-stats-grid .stat-card span")).forEach(function(e){return e.classList.add("loading")}),e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_get_stats",{});case 2:if(!(n=e.v).data.success){e.n=3;break}r=n.data.data,this.updateStatsDisplay(r),(0,o.Ic)("stats:updated",r),e.n=4;break;case 3:throw new Error(n.data.data.message||"获取统计失败");case 4:e.n=6;break;case 5:e.p=5,i=e.v,(0,s.Qg)("获取统计失败: ".concat(i.message)),(0,o.Ic)("stats:error",i);case 6:return e.p=6,t.forEach(function(e){return e.classList.remove("loading")}),e.f(6);case 7:return e.a(2)}},e,this,[[1,5,6,7]])})),function(){return l.apply(this,arguments)})},{key:"updateStatsDisplay",value:function(e){var t=document.querySelector(".stat-imported-count");t&&(t.textContent=(e.imported_count||0).toString());var n=document.querySelector(".stat-published-count");n&&(n.textContent=(e.published_count||0).toString());var r=document.querySelector(".stat-last-update");r&&(r.innerHTML=Cn.formatDateTime(e.last_update));var o=document.querySelector(".stat-next-run");if(o){var i=e.next_run||"未计划";o.innerHTML=Cn.formatDateTime(i)}}},{key:"handleRefreshSingle",value:(c=ar(rr().m(function e(t){var n,r;return rr().w(function(e){for(;;)switch(e.n){case 0:if((n=t.dataset.pageId)&&"string"==typeof n&&""!==n.trim()){e.n=1;break}return(0,s.Qg)("无效的页面ID"),e.a(2);case 1:if(null!=(r=window.notionToWp)&&r.nonce&&null!=r&&r.ajax_url){e.n=2;break}return(0,s.Qg)("安全参数缺失"),e.a(2);case 2:return e.n=3,this.refreshSinglePage({pageId:n,showOverlay:!0});case 3:return e.a(2)}},e,this)})),function(e){return c.apply(this,arguments)})},{key:"refreshSinglePage",value:(u=ar(rr().m(function e(t){var n,r,i,a,u,c,l,f,h,p;return rr().w(function(e){for(;;)switch(e.p=e.n){case 0:return n=t.pageId,r=t.showOverlay,i=void 0!==r&&r,a=t.timeout,u=void 0===a?6e4:a,c=null,i&&(c=this.showLoadingOverlay()),e.p=1,e.n=2,this.performSingleRefresh(n,u);case 2:if(!(l=e.v).success){e.n=4;break}return(0,s.Te)("页面刷新成功"),e.n=3,this.fetchStats();case 3:(0,o.Ic)("stats:single:refreshed",{pageId:n,response:l}),e.n=5;break;case 4:throw new Error((null===(f=l.data)||void 0===f?void 0:f.message)||"刷新失败");case 5:e.n=7;break;case 6:e.p=6,p=e.v,h="页面刷新失败",p.message.includes("timeout")?h="刷新超时，请稍后重试":p.message&&(h+=": ".concat(p.message)),(0,s.Qg)(h),(0,o.Ic)("stats:single:error",{pageId:n,error:p});case 7:return e.p=7,c&&this.hideLoadingOverlay(c),e.f(7);case 8:return e.a(2)}},e,this,[[1,6,7,8]])})),function(e){return u.apply(this,arguments)})},{key:"performSingleRefresh",value:(i=ar(rr().m(function e(t,n){var r;return rr().w(function(e){for(;;)if(0===e.n)return r=window.notionToWp,e.a(2,new Promise(function(e,o){var i=new XMLHttpRequest;i.open("POST",r.ajax_url),i.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),i.timeout=n,i.onload=function(){try{var t=JSON.parse(i.responseText);e(t)}catch(e){o(new Error("响应解析失败"))}},i.onerror=function(){return o(new Error("网络错误"))},i.ontimeout=function(){return o(new Error("请求超时"))};var a=new URLSearchParams({action:"notion_to_wordpress_refresh_single",nonce:r.nonce,page_id:t});i.send(a.toString())}))},e)})),function(e,t){return i.apply(this,arguments)})},{key:"showLoadingOverlay",value:function(){var e=document.createElement("div");return e.className="notion-loading-overlay",e.innerHTML='\n      <div class="loading-content">\n        <div class="spinner"></div>\n        <p>正在刷新页面...</p>\n      </div>\n    ',e.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: rgba(0, 0, 0, 0.5);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 9999;\n    ",document.body.appendChild(e),setTimeout(function(){e.style.opacity="1"},10),e}},{key:"hideLoadingOverlay",value:function(e){e.style.opacity="0",setTimeout(function(){e.parentNode&&e.parentNode.removeChild(e)},300)}},{key:"toggleAutoRefresh",value:function(e){this.isAutoRefreshEnabled=e,e?(this.startAutoRefresh(),(0,s.cf)("已启用统计自动刷新")):(this.stopAutoRefresh(),(0,s.cf)("已禁用统计自动刷新")),(0,o.Ic)("stats:auto-refresh:toggled",e)}},{key:"startAutoRefresh",value:function(){var e=this;this.refreshTimer&&clearInterval(this.refreshTimer),this.refreshTimer=setInterval(function(){e.isAutoRefreshEnabled&&!document.hidden&&e.fetchStats()},this.autoRefreshInterval)}},{key:"stopAutoRefresh",value:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)}},{key:"setAutoRefreshInterval",value:function(e){this.autoRefreshInterval=e,this.isAutoRefreshEnabled&&this.startAutoRefresh(),(0,o.Ic)("stats:auto-refresh:interval:changed",e)}},{key:"getCurrentStats",value:function(){var e,t,n,r,o=null===(e=document.querySelector(".stat-imported-count"))||void 0===e?void 0:e.textContent,i=null===(t=document.querySelector(".stat-published-count"))||void 0===t?void 0:t.textContent,a=null===(n=document.querySelector(".stat-last-update"))||void 0===n?void 0:n.textContent,s=null===(r=document.querySelector(".stat-next-run"))||void 0===r?void 0:r.textContent;return o&&i?{imported_count:parseInt(o)||0,published_count:parseInt(i)||0,last_update:a||null,next_run:s||null}:null}},{key:"isAutoRefreshActive",value:function(){return this.isAutoRefreshEnabled}},{key:"destroy",value:function(){this.stopAutoRefresh(),e.instance=null}}],r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],n&&sr(t.prototype,n),r&&sr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c,l}();ur(lr,"instance",null);var fr=lr.getInstance();function hr(e){return hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hr(e)}function pr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function dr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pr(Object(n),!0).forEach(function(t){vr(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pr(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function yr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,mr(r.key),r)}}function vr(e,t,n){return(t=mr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mr(e){var t=function(e,t){if("object"!=hr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=hr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hr(t)?t:t+""}var br=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),vr(this,"initialized",!1),vr(this,"config",void 0),vr(this,"syncStatusManager",void 0),vr(this,"formManager",void 0),vr(this,"statsManager",void 0),e.instance)return e.instance;e.instance=this,this.config=dr({enableAutoRefresh:!0,autoRefreshInterval:3e4,enableFormValidation:!0,enableSyncStatusMonitoring:!0},t),this.syncStatusManager=Vn,this.formManager=tr,this.statsManager=fr}return t=e,n=[{key:"init",value:function(){if(!this.initialized)try{this.setupGlobalEventHandlers(),this.setupManagerCoordination(),this.setupErrorHandling(),this.setupCompatibilityLayer(),this.applyConfiguration(),this.initialized=!0,(0,o.Ic)("admin:interactions:initialized")}catch(e){throw(0,s.Qg)("管理界面初始化失败，部分功能可能不可用"),e}}},{key:"setupGlobalEventHandlers",value:function(){var e=this;window.addEventListener("beforeunload",function(){e.cleanup()}),document.addEventListener("visibilitychange",function(){document.hidden?(0,o.Ic)("admin:page:hidden"):(0,o.Ic)("admin:page:visible")}),document.addEventListener("keydown",function(t){e.handleGlobalKeyboard(t)}),document.addEventListener("click",function(t){e.handleGlobalClick(t)})}},{key:"setupManagerCoordination",value:function(){var e=this;(0,o.on)("sync:status:cleared",function(){e.statsManager.fetchStats()}),(0,o.on)("form:settings:saved",function(){e.syncStatusManager.checkSyncStatus()}),(0,o.on)("stats:updated",function(e,t){(0,o.Ic)("admin:stats:changed",t)})}},{key:"setupErrorHandling",value:function(){window.addEventListener("error",function(e){var t,n;(null!==(t=e.filename)&&void 0!==t&&t.includes("notion-to-wordpress")||null!==(n=e.error)&&void 0!==n&&null!==(n=n.stack)&&void 0!==n&&n.includes("notion-to-wordpress"))&&(0,s.Qg)("发生了一个错误，请刷新页面重试")}),window.addEventListener("unhandledrejection",function(e){e.preventDefault()})}},{key:"setupCompatibilityLayer",value:function(){var e=window.notionToWp||{};e.utils={debounce:Cn.debounce,throttle:Cn.throttle,setButtonLoading:Cn.setButtonLoading,updateProgress:Cn.updateProgress,hideProgress:Cn.hideProgress,validateInput:Cn.validateInput,formatDateTime:Cn.formatDateTime},e.managers={syncStatus:this.syncStatusManager,form:this.formManager,stats:this.statsManager},e.adminInteractions=this,window.notionToWp=e}},{key:"applyConfiguration",value:function(){this.config.enableAutoRefresh&&(this.statsManager.toggleAutoRefresh(!0),this.config.autoRefreshInterval&&this.statsManager.setAutoRefreshInterval(this.config.autoRefreshInterval)),this.config.enableSyncStatusMonitoring&&this.syncStatusManager.startStatusMonitoring()}},{key:"handleGlobalKeyboard",value:function(e){if((e.ctrlKey||e.metaKey)&&"s"===e.key){var t=document.getElementById("notion-to-wordpress-settings-form");t&&(e.preventDefault(),t.dispatchEvent(new Event("submit")))}(e.ctrlKey||e.metaKey)&&"r"===e.key&&document.querySelector(".notion-wp-admin")&&(e.preventDefault(),this.statsManager.fetchStats())}},{key:"handleGlobalClick",value:function(e){var t=e.target;t.dataset.notionAction&&(e.preventDefault(),this.handleDataAction(t,t.dataset.notionAction))}},{key:"handleDataAction",value:function(e,t){switch(t){case"refresh-stats":this.statsManager.fetchStats();break;case"check-sync-status":this.syncStatusManager.checkSyncStatus();break;case"clear-sync-status":this.syncStatusManager.clearSyncStatus()}}},{key:"getSyncStatusManager",value:function(){return this.syncStatusManager}},{key:"getFormManager",value:function(){return this.formManager}},{key:"getStatsManager",value:function(){return this.statsManager}},{key:"getConfig",value:function(){return dr({},this.config)}},{key:"updateConfig",value:function(e){this.config=dr(dr({},this.config),e),this.applyConfiguration(),(0,o.Ic)("admin:config:updated",this.config)}},{key:"isInitialized",value:function(){return this.initialized}},{key:"getSystemStatus",value:function(){return{initialized:this.initialized,managersActive:{syncStatus:this.syncStatusManager.hasActivSync(),form:!0,stats:this.statsManager.isAutoRefreshActive()},config:this.config}}},{key:"cleanup",value:function(){if(this.initialized)try{this.syncStatusManager.destroy(),this.formManager.destroy(),this.statsManager.destroy(),this.initialized=!1,e.instance=null,(0,o.Ic)("admin:interactions:destroyed")}catch(e){}}},{key:"destroy",value:function(){this.cleanup()}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&yr(t.prototype,n),r&&yr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();vr(br,"instance",null);var gr=br.getInstance();"loading"===document.readyState?document.addEventListener("DOMContentLoaded",function(){document.querySelector(".notion-wp-admin")&&gr.init()}):document.querySelector(".notion-wp-admin")&&gr.init();function wr(e){return wr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wr(e)}function kr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Sr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Tr(r.key),r)}}function Or(e,t,n){return(t=Tr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tr(e){var t=function(e,t){if("object"!=wr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=wr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==wr(t)?t:t+""}var Er=function(){return e=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Or(this,"taskId",void 0),Or(this,"options",void 0),Or(this,"eventSource",null),Or(this,"reconnectTimer",null),Or(this,"reconnectAttempts",0),Or(this,"isConnected",!1),Or(this,"isStopped",!1),this.taskId=t,this.options=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?kr(Object(n),!0).forEach(function(t){Or(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kr(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({onProgress:function(){},onComplete:function(){},onError:function(){},onConnect:function(){},onDisconnect:function(){},reconnectAttempts:3,reconnectDelay:2e3,timeout:3e4},n)},(t=[{key:"start",value:function(){if(!this.eventSource&&!this.isStopped){if("undefined"==typeof EventSource){var e=new Error("浏览器不支持Server-Sent Events");return this.options.onError(e),void(0,o.Ic)("sse:error",{taskId:this.taskId,error:e})}this.connect()}}},{key:"connect",value:function(){var e=this;try{var t=this.buildSSEUrl();this.eventSource=new EventSource(t),this.setupEventListeners(),setTimeout(function(){!e.isConnected&&e.eventSource&&e.handleConnectionError(new Error("连接超时"))},this.options.timeout)}catch(e){this.handleConnectionError(e)}}},{key:"buildSSEUrl",value:function(){var e,t,n=(null===(e=window.notionToWp)||void 0===e?void 0:e.sse_endpoint)||"/wp-admin/admin-ajax.php",r=new URLSearchParams({action:"notion_to_wordpress_sse_progress",task_id:this.taskId,nonce:(null===(t=window.notionToWp)||void 0===t?void 0:t.nonce)||""});return"".concat(n,"?").concat(r.toString())}},{key:"setupEventListeners",value:function(){var e=this;this.eventSource&&(this.eventSource.onopen=function(){e.isConnected=!0,e.reconnectAttempts=0,e.options.onConnect(),(0,o.Ic)("sse:connected",{taskId:e.taskId})},this.eventSource.onmessage=function(t){try{var n=JSON.parse(t.data);e.handleMessage(n)}catch(t){e.options.onError(t)}},this.eventSource.onerror=function(t){e.handleConnectionError(new Error("SSE连接错误"))},this.eventSource.addEventListener("progress",function(t){try{var n=JSON.parse(t.data);e.handleProgressUpdate(n)}catch(e){}}),this.eventSource.addEventListener("complete",function(t){try{var n=JSON.parse(t.data);e.handleCompletion(n)}catch(e){}}),this.eventSource.addEventListener("error",function(t){try{var n=JSON.parse(t.data);e.handleServerError(n)}catch(e){}}))}},{key:"handleMessage",value:function(e){switch(e.type){case"progress":this.handleProgressUpdate(e);break;case"complete":this.handleCompletion(e);break;case"error":this.handleServerError(e)}}},{key:"handleProgressUpdate",value:function(e){var t={progress:{percentage:e.percentage||0,current:e.current||0,total:e.total||0,message:e.message,step:e.step},status:e.status||"running",timestamp:Date.now(),taskId:this.taskId};this.options.onProgress(t),(0,o.Ic)("sse:progress",t)}},{key:"handleCompletion",value:function(e){var t={status:e.status||"completed",message:e.message||"任务完成",summary:e.summary,timestamp:Date.now(),taskId:this.taskId};this.options.onComplete(t),(0,o.Ic)("sse:complete",t),this.stop()}},{key:"handleServerError",value:function(e){var t=new Error(e.message||"服务器错误");this.options.onError(t),(0,o.Ic)("sse:server:error",{taskId:this.taskId,error:t,data:e})}},{key:"handleConnectionError",value:function(e){var t=this;this.isConnected=!1,this.options.onDisconnect(),(0,o.Ic)("sse:disconnected",{taskId:this.taskId,error:e}),this.reconnectAttempts<this.options.reconnectAttempts&&!this.isStopped?(this.reconnectAttempts++,this.reconnectTimer=setTimeout(function(){t.disconnect(),t.connect()},this.options.reconnectDelay*this.reconnectAttempts)):this.options.onError(e)}},{key:"disconnect",value:function(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.isConnected=!1}},{key:"stop",value:function(){this.isStopped=!0,this.disconnect(),(0,o.Ic)("sse:stopped",{taskId:this.taskId})}},{key:"isConnectedToServer",value:function(){return this.isConnected}},{key:"getTaskId",value:function(){return this.taskId}},{key:"getReconnectAttempts",value:function(){return this.reconnectAttempts}},{key:"resetReconnectAttempts",value:function(){this.reconnectAttempts=0}}])&&Sr(e.prototype,t),n&&Sr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();function jr(e){return jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jr(e)}function _r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Pr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_r(Object(n),!0).forEach(function(t){Cr(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_r(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ir(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,xr(r.key),r)}}function Cr(e,t,n){return(t=xr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xr(e){var t=function(e,t){if("object"!=jr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jr(t)?t:t+""}var Ar=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Cr(this,"container",null),Cr(this,"progressFill",null),Cr(this,"statusText",null),Cr(this,"percentageText",null),Cr(this,"etaText",null),Cr(this,"currentItemText",null),Cr(this,"iconElement",null),Cr(this,"isVisible",!1),Cr(this,"currentTaskId",null),Cr(this,"options",void 0),Cr(this,"startTime",0),Cr(this,"hideTimer",null),e.instance)return e.instance;e.instance=this,this.options=Pr({title:"同步进度",syncType:"同步",showPercentage:!0,showETA:!0,showCurrentItem:!0,theme:"default",position:"top",closable:!1,autoHide:!0,autoHideDelay:2e3},t)}return t=e,n=[{key:"show",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.options=Pr(Pr({},this.options),t),this.currentTaskId=e,this.startTime=Date.now(),this.hideTimer&&(clearTimeout(this.hideTimer),this.hideTimer=null),this.createOrUpdateUI(),this.showContainer(),this.isVisible=!0,(0,o.Ic)("progress:ui:shown",{taskId:e,options:this.options})}},{key:"hide",value:function(){var e=this;this.isVisible&&this.container&&(this.container.style.transition="opacity 0.3s ease-out, transform 0.3s ease-out",this.container.style.opacity="0",this.container.style.transform="translateY(-10px)",setTimeout(function(){e.container&&(e.container.remove(),e.container=null),e.resetReferences()},300),this.isVisible=!1,this.currentTaskId=null,(0,o.Ic)("progress:ui:hidden"))}},{key:"updateProgress",value:function(e){this.isVisible&&this.container&&(this.progressFill&&(this.progressFill.style.width="".concat(Math.max(0,Math.min(100,e.percentage)),"%")),this.percentageText&&this.options.showPercentage&&(this.percentageText.textContent="".concat(Math.round(e.percentage),"%")),this.statusText&&e.message&&(this.statusText.textContent=e.message),this.currentItemText&&this.options.showCurrentItem&&e.step&&(this.currentItemText.textContent=e.step),this.etaText&&this.options.showETA&&e.eta&&(this.etaText.textContent=this.formatETA(e.eta)),this.updateProgressAnimation(e.percentage),(0,o.Ic)("progress:ui:updated",{data:e,taskId:this.currentTaskId}))}},{key:"setStatus",value:function(e,t){var n=this;this.isVisible&&this.container&&(this.iconElement&&(this.iconElement.textContent={running:"🔄",completed:"✅",failed:"❌",cancelled:"⏹️"}[e]),this.statusText&&t&&(this.statusText.textContent=t),this.container.className=this.container.className.replace(/status-\w+/g,""),this.container.classList.add("status-".concat(e)),"completed"===e&&(this.progressFill&&(this.progressFill.style.width="100%"),this.percentageText&&(this.percentageText.textContent="100%")),!this.options.autoHide||"completed"!==e&&"failed"!==e&&"cancelled"!==e||(this.hideTimer=setTimeout(function(){n.hide()},this.options.autoHideDelay)),(0,o.Ic)("progress:ui:status:changed",{status:e,message:t,taskId:this.currentTaskId}))}},{key:"createOrUpdateUI",value:function(){var e=document.querySelector(".notion-sync-progress-container");e&&e.remove(),this.container=this.createElement();var t=this.findInsertTarget();t?t.appendChild(this.container):document.body.appendChild(this.container),this.getElementReferences()}},{key:"createElement",value:function(){var e=this,t=document.createElement("div");if(t.className="notion-sync-progress-container theme-".concat(this.options.theme," position-").concat(this.options.position),t.style.display="none",t.innerHTML='\n      <div class="sync-progress-content">\n        <div class="sync-progress-header">\n          <div class="sync-progress-title">\n            <span class="sync-progress-icon">🔄</span>\n            <span class="sync-progress-text">'.concat(this.options.title,"</span>\n          </div>\n          ").concat(this.options.closable?'<button class="sync-progress-close" type="button">×</button>':"",'\n        </div>\n        \n        <div class="sync-progress-body">\n          <div class="progress-bar-container">\n            <div class="progress-bar">\n              <div class="progress-fill"></div>\n              <div class="progress-shine"></div>\n            </div>\n            ').concat(this.options.showPercentage?'<div class="progress-percentage">0%</div>':"",'\n          </div>\n          \n          <div class="progress-info">\n            <div class="progress-status">准备中...</div>\n            ').concat(this.options.showCurrentItem?'<div class="progress-current-item"></div>':"","\n            ").concat(this.options.showETA?'<div class="progress-eta"></div>':"","\n          </div>\n        </div>\n      </div>\n    "),this.options.closable){var n=t.querySelector(".sync-progress-close");n&&n.addEventListener("click",function(){e.hide()})}return t}},{key:"findInsertTarget",value:function(){for(var e=0,t=[".notion-wp-sync-actions",".notion-wp-admin .wrap",".wrap","body"];e<t.length;e++){var n=t[e],r=document.querySelector(n);if(r)return r}return document.body}},{key:"getElementReferences",value:function(){this.container&&(this.progressFill=this.container.querySelector(".progress-fill"),this.statusText=this.container.querySelector(".progress-status"),this.percentageText=this.container.querySelector(".progress-percentage"),this.etaText=this.container.querySelector(".progress-eta"),this.currentItemText=this.container.querySelector(".progress-current-item"),this.iconElement=this.container.querySelector(".sync-progress-icon"))}},{key:"showContainer",value:function(){var e=this;this.container&&(this.container.style.opacity="0",this.container.style.transform="translateY(-10px)",this.container.style.display="block",setTimeout(function(){e.container&&(e.container.style.transition="opacity 0.3s ease-out, transform 0.3s ease-out",e.container.style.opacity="1",e.container.style.transform="translateY(0)")},10))}},{key:"updateProgressAnimation",value:function(e){this.progressFill&&(e>0&&e<100?this.progressFill.classList.add("progress-active"):this.progressFill.classList.remove("progress-active"))}},{key:"formatETA",value:function(e){if(e<=0)return"";var t=Math.floor(e/60),n=Math.floor(e%60);return t>0?"预计剩余: ".concat(t,"分").concat(n,"秒"):"预计剩余: ".concat(n,"秒")}},{key:"resetReferences",value:function(){this.progressFill=null,this.statusText=null,this.percentageText=null,this.etaText=null,this.currentItemText=null,this.iconElement=null}},{key:"getCurrentTaskId",value:function(){return this.currentTaskId}},{key:"isProgressVisible",value:function(){return this.isVisible}},{key:"getDuration",value:function(){return this.startTime>0?Date.now()-this.startTime:0}},{key:"updateOptions",value:function(e){this.options=Pr(Pr({},this.options),e),(0,o.Ic)("progress:ui:options:updated",this.options)}},{key:"destroy",value:function(){this.hide(),this.hideTimer&&(clearTimeout(this.hideTimer),this.hideTimer=null),e.instance=null,(0,o.Ic)("progress:ui:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&Ir(t.prototype,n),r&&Ir(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();Cr(Ar,"instance",null);Ar.getInstance();function Lr(e){return Lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lr(e)}function Dr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function Rr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Dr(Object(n),!0).forEach(function(t){Nr(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Dr(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Mr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Fr(r.key),r)}}function Nr(e,t,n){return(t=Fr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fr(e){var t=function(e,t){if("object"!=Lr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Lr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Lr(t)?t:t+""}var Br=function(){function e(){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Nr(this,"sseManager",null),Nr(this,"uiManager",void 0),Nr(this,"state",void 0),Nr(this,"visibilityTimer",null),e.instance)return e.instance;e.instance=this,this.uiManager=Ar.getInstance(),this.state={taskId:null,isActive:!1,isVisible:!1,startTime:0,lastUpdate:0,currentProgress:null},this.init()}return t=e,r=[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}],(n=[{key:"init",value:function(){this.setupEventListeners(),this.setupVisibilityHandling(),(0,o.Ic)("sync:progress:manager:initialized")}},{key:"setupEventListeners",value:function(){var e=this;(0,o.on)("progress:ui:shown",function(){e.state.isVisible=!0}),(0,o.on)("progress:ui:hidden",function(){e.state.isVisible=!1}),(0,o.on)("sse:connected",function(){}),(0,o.on)("sse:disconnected",function(){}),(0,o.on)("sse:error",function(e,t){})}},{key:"setupVisibilityHandling",value:function(){var e=this;document.addEventListener("visibilitychange",function(){e.handleVisibilityChange()}),window.addEventListener("focus",function(){e.state.isActive&&e.checkProgressStatus()})}},{key:"showProgress",value:function(e){var t=e.taskId,n=e.syncType,r=void 0===n?"同步":n,i=e.title,a=e.enableSSE,s=void 0===a||a,u=e.enableUI,c=void 0===u||u,l=e.uiOptions,f=void 0===l?{}:l,h=e.onProgress,p=e.onComplete,d=e.onError;if(this.state.isActive&&this.hideProgress(),this.state={taskId:t,isActive:!0,isVisible:!1,startTime:Date.now(),lastUpdate:Date.now(),currentProgress:null},c){var y=Rr({title:i||"".concat(r,"进行中"),syncType:r},f);this.uiManager.show(t,y),this.state.isVisible=!0}s&&this.startSSE(t,{onProgress:h,onComplete:p,onError:d}),(0,o.Ic)("sync:progress:started",{taskId:t,options:e})}},{key:"hideProgress",value:function(){if(this.state.isActive){this.sseManager&&(this.sseManager.stop(),this.sseManager=null),this.uiManager.hide(),this.visibilityTimer&&(clearTimeout(this.visibilityTimer),this.visibilityTimer=null);var e=this.state.taskId;this.state={taskId:null,isActive:!1,isVisible:!1,startTime:0,lastUpdate:0,currentProgress:null},(0,o.Ic)("sync:progress:stopped",{taskId:e})}}},{key:"startSSE",value:function(e,t){var n=this;this.sseManager=new Er(e,{onProgress:function(e){var r;n.handleSSEProgress(e),null===(r=t.onProgress)||void 0===r||r.call(t,e)},onComplete:function(e){var r;n.handleSSEComplete(e),null===(r=t.onComplete)||void 0===r||r.call(t,e)},onError:function(e){var r;n.handleSSEError(e),null===(r=t.onError)||void 0===r||r.call(t,e)},onConnect:function(){},onDisconnect:function(){}}),this.sseManager.start()}},{key:"handleSSEProgress",value:function(e){var t={percentage:e.progress.percentage,current:e.progress.current,total:e.progress.total,message:e.progress.message,step:e.progress.step};this.state.currentProgress=t,this.state.lastUpdate=Date.now(),this.state.isVisible&&this.uiManager.updateProgress(t),(0,o.Ic)("sync:progress:updated",{data:e,uiData:t})}},{key:"handleSSEComplete",value:function(e){var t=this;this.state.isVisible&&this.uiManager.setStatus(e.status,e.message),(0,o.Ic)("sync:progress:completed",{data:e}),setTimeout(function(){t.hideProgress()},2e3)}},{key:"handleSSEError",value:function(e){var t=this;this.state.isVisible&&this.uiManager.setStatus("failed","连接错误: ".concat(e.message)),(0,o.Ic)("sync:progress:error",{error:e}),setTimeout(function(){t.hideProgress()},3e3)}},{key:"handleVisibilityChange",value:function(){this.state.isActive&&!document.hidden&&this.checkProgressStatus()}},{key:"checkProgressStatus",value:function(){var e=this;this.state.isActive&&this.state.taskId&&Date.now()-this.state.lastUpdate>3e4&&this.sseManager&&(this.sseManager.stop(),setTimeout(function(){e.state.taskId&&e.startSSE(e.state.taskId,{})},1e3))}},{key:"updateProgress",value:function(e){this.state.isActive&&(this.state.currentProgress=e,this.state.lastUpdate=Date.now(),this.state.isVisible&&this.uiManager.updateProgress(e),(0,o.Ic)("sync:progress:manual:updated",{data:e}))}},{key:"setStatus",value:function(e,t){var n=this;this.state.isActive&&(this.state.isVisible&&this.uiManager.setStatus(e,t),(0,o.Ic)("sync:progress:status:changed",{status:e,message:t}),"running"!==e&&setTimeout(function(){n.hideProgress()},"completed"===e?1e3:2e3))}},{key:"getState",value:function(){return Rr({},this.state)}},{key:"isActive",value:function(){return this.state.isActive}},{key:"isVisible",value:function(){return this.state.isVisible}},{key:"getCurrentTaskId",value:function(){return this.state.taskId}},{key:"getDuration",value:function(){return this.state.startTime>0?Date.now()-this.state.startTime:0}},{key:"destroy",value:function(){this.hideProgress(),this.uiManager.destroy(),this.visibilityTimer&&(clearTimeout(this.visibilityTimer),this.visibilityTimer=null),e.instance=null,(0,o.Ic)("sync:progress:manager:destroyed")}}])&&Mr(t.prototype,n),r&&Mr(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();Nr(Br,"instance",null);var Gr=Br.getInstance();n(4743),n(1745),n(1489),n(1630),n(2170),n(5044),n(1920),n(1694),n(9955),n(3206),n(4496),n(6651),n(2887),n(9369),n(6812),n(8995),n(1575),n(6072),n(8747),n(8845),n(9423),n(7301),n(373),n(6614),n(1405),n(3684);function Vr(e){return Vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vr(e)}function Ur(e){return function(e){if(Array.isArray(e))return qr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return qr(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qr(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function zr(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Hr(r.key),r)}}function Qr(e,t,n){return(t=Hr(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Hr(e){var t=function(e,t){if("object"!=Vr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Vr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Vr(t)?t:t+""}var Wr=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},n=[{key:"validateToken",value:function(e){var t={isValid:!1,errors:[],warnings:[],suggestions:[]};if(!e||""===e.trim())return t.errors.push("Webhook令牌不能为空"),t.suggestions.push("请生成一个新的Webhook令牌"),t;var n=e.trim();return n.length<this.TOKEN_MIN_LENGTH?(t.errors.push("Webhook令牌长度不能少于".concat(this.TOKEN_MIN_LENGTH,"个字符")),t.suggestions.push("建议使用32位或更长的随机字符串"),t):n.length>this.TOKEN_MAX_LENGTH?(t.errors.push("Webhook令牌长度不能超过".concat(this.TOKEN_MAX_LENGTH,"个字符")),t):this.TOKEN_PATTERN.test(n)?(this.isWeakToken(n)&&(t.warnings.push("当前令牌可能不够安全"),t.suggestions.push("建议使用更复杂的随机字符串")),this.isCommonWeakToken(n)?(t.errors.push("检测到常见的弱令牌，存在安全风险"),t.suggestions.push("请生成一个新的随机令牌"),t):(t.isValid=!0,t)):(t.errors.push("Webhook令牌只能包含字母、数字、下划线和连字符"),t.suggestions.push("请使用字母数字组合，避免特殊字符"),t)}},{key:"validateWebhookUrl",value:function(e){var t={isValid:!1,errors:[],warnings:[],suggestions:[]};if(!e||""===e.trim())return t.errors.push("Webhook URL不能为空"),t;var n=e.trim();if(!this.URL_PATTERN.test(n))return t.errors.push("Webhook URL格式不正确"),t.suggestions.push("URL应以http://或https://开头"),t;try{var r=new URL(n);return"https:"!==r.protocol&&"http:"!==r.protocol?(t.errors.push("Webhook URL必须使用HTTP或HTTPS协议"),t):("http:"===r.protocol&&(t.warnings.push("建议使用HTTPS协议以确保安全性"),t.suggestions.push("如果可能，请使用HTTPS版本的URL")),r.hostname?(this.isLocalAddress(r.hostname)&&(t.warnings.push("检测到本地地址，可能无法从外部访问"),t.suggestions.push("确保Notion服务能够访问此地址")),r.pathname.includes("/webhook/")||(t.warnings.push("URL路径可能不正确"),t.suggestions.push("确保URL包含正确的webhook路径")),t.isValid=!0,t):(t.errors.push("Webhook URL缺少有效的主机名"),t))}catch(e){return t.errors.push("URL解析失败："+e.message),t}}},{key:"validateWebhookConfig",value:function(e){var t,n,r,o,i,a,s={isValid:!0,errors:[],warnings:[],suggestions:[]};if(!e.enabled)return s;var u=this.validateToken(e.token);(t=s.errors).push.apply(t,Ur(u.errors)),(n=s.warnings).push.apply(n,Ur(u.warnings)),(r=s.suggestions).push.apply(r,Ur(u.suggestions)),u.isValid||(s.isValid=!1);var c=this.validateWebhookUrl(e.url);return(o=s.errors).push.apply(o,Ur(c.errors)),(i=s.warnings).push.apply(i,Ur(c.warnings)),(a=s.suggestions).push.apply(a,Ur(c.suggestions)),c.isValid||(s.isValid=!1),e.verificationToken?e.verificationToken.length<8&&s.warnings.push("验证令牌长度较短，可能不够安全"):s.suggestions.push("建议设置验证令牌以增强安全性"),s}},{key:"generateSecureToken",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-",n="";if("undefined"!=typeof crypto&&crypto.getRandomValues){var r=new Uint8Array(e);crypto.getRandomValues(r);for(var o=0;o<e;o++)n+=t[r[o]%64]}else for(var i=0;i<e;i++)n+=t[Math.floor(64*Math.random())];return n}},{key:"buildWebhookUrl",value:function(e,t){var n=e.replace(/\/$/,"");return"".concat(n,"/wp-json/notion-to-wordpress/v1/webhook/").concat(t)}},{key:"extractTokenFromUrl",value:function(e){try{var t=new URL(e).pathname.split("/"),n=t.indexOf("webhook");return-1!==n&&n<t.length-1?t[n+1]:null}catch(e){return null}}},{key:"isWeakToken",value:function(e){return!!/(.)\1{3,}/.test(e)||!!/^(123|abc|aaa|111)/i.test(e)||[/[a-z]/.test(e),/[A-Z]/.test(e),/[0-9]/.test(e),/[_-]/.test(e)].filter(Boolean).length<2}},{key:"isCommonWeakToken",value:function(e){var t=e.toLowerCase();return["password","secret","token","webhook","notion","wordpress","123456","abcdef","test","demo"].some(function(e){return t.includes(e)||e.includes(t)})}},{key:"isLocalAddress",value:function(e){return[/^localhost$/i,/^127\./,/^192\.168\./,/^10\./,/^172\.(1[6-9]|2[0-9]|3[0-1])\./,/^::1$/,/^fe80:/i].some(function(t){return t.test(e)})}},{key:"validateEventType",value:function(e){return["page.created","page.updated","page.deleted","block.created","block.updated","block.deleted","database.created","database.updated","database.deleted"].includes(e)||/^(page|block|database)\.(created|updated|deleted)$/.test(e)}},{key:"getConfigurationSuggestions",value:function(){return["使用HTTPS协议确保数据传输安全","定期更换Webhook令牌以提高安全性","启用增量同步以提高性能","根据需要配置删除检测","监控Webhook日志以及时发现问题","设置适当的超时和重试机制","使用强密码生成器创建令牌"]}},{key:"getSecurityBestPractices",value:function(){return["不要在URL中暴露敏感信息","使用足够长度的随机令牌（建议32位以上）","定期检查和更新Webhook配置","监控异常的Webhook请求","限制Webhook的访问频率","使用HTTPS防止中间人攻击","验证请求来源的合法性"]}}],(t=null)&&zr(e.prototype,t),n&&zr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();Qr(Wr,"TOKEN_MIN_LENGTH",16),Qr(Wr,"TOKEN_MAX_LENGTH",64),Qr(Wr,"TOKEN_PATTERN",/^[a-zA-Z0-9_-]+$/),Qr(Wr,"URL_PATTERN",/^https?:\/\/[^\s/$.?#].[^\s]*$/i);n(8350),n(237),n(1392);function Kr(e){return Kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(e)}function $r(e){return function(e){if(Array.isArray(e))return Jr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Jr(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Jr(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Xr(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return Yr(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(Yr(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,Yr(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,Yr(f,"constructor",c),Yr(c,"constructor",u),u.displayName="GeneratorFunction",Yr(c,o,"GeneratorFunction"),Yr(f),Yr(f,o,"Generator"),Yr(f,r,function(){return this}),Yr(f,"toString",function(){return"[object Generator]"}),(Xr=function(){return{w:i,m:h}})()}function Yr(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}Yr=function(e,t,n,r){function i(t,n){Yr(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},Yr(e,t,n,r)}function Zr(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function eo(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){Zr(i,r,o,a,s,"next",e)}function s(e){Zr(i,r,o,a,s,"throw",e)}a(void 0)})}}function to(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ro(r.key),r)}}function no(e,t,n){return(t=ro(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ro(e){var t=function(e,t){if("object"!=Kr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Kr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Kr(t)?t:t+""}var oo=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=null,n=[{key:"testWebhook",value:(l=eo(Xr().m(function e(t){var n,r,o,i,a,s,u,c;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:n=t.url,r=t.token,o=t.timeout,i=void 0===o?this.DEFAULT_TIMEOUT:o,a=t.testType,s=void 0===a?"full":a,e.p=1,u=s,e.n="connection"===u?2:"verification"===u?4:6;break;case 2:return e.n=3,this.testConnection(n,i);case 3:case 5:case 7:return e.a(2,e.v);case 4:return e.n=5,this.testVerification(n,r,i);case 6:return e.n=7,this.testFullWebhook(n,r,i);case 8:e.n=10;break;case 9:return e.p=9,c=e.v,e.a(2,{success:!1,status:"error",message:"测试执行失败",details:{error:c.message},suggestions:["检查网络连接","确认URL和令牌正确","稍后重试"]});case 10:return e.a(2)}},e,this,[[1,9]])})),function(e){return l.apply(this,arguments)})},{key:"testConnection",value:(c=eo(Xr().m(function e(t,n){var r,o,i,a,s,u;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:return r=Date.now(),e.p=1,o=this.buildTestUrl(t,this.TEST_ENDPOINTS.connection),e.n=2,this.sendTestRequest(o,{action:"notion_to_wordpress_webhook_test",test_type:"connection"},n);case 2:if(i=e.v,a=Date.now()-r,!i.ok){e.n=3;break}return e.a(2,{success:!0,status:"success",message:"连接测试成功",details:{responseTime:a,statusCode:i.status}});case 3:return e.a(2,{success:!1,status:"error",message:"连接测试失败 (".concat(i.status,")"),details:{responseTime:a,statusCode:i.status},suggestions:["检查服务器是否正常运行","确认URL是否正确","检查防火墙设置"]});case 4:e.n=6;break;case 5:return e.p=5,u=e.v,s=Date.now()-r,e.a(2,{success:!1,status:"error",message:"连接失败",details:{responseTime:s,error:u.message},suggestions:["检查网络连接","确认服务器地址正确","检查是否存在网络限制"]});case 6:return e.a(2)}},e,this,[[1,5]])})),function(e,t){return c.apply(this,arguments)})},{key:"testVerification",value:(u=eo(Xr().m(function e(t,n,r){var o,i,a,s,u,c,l;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:return o=Date.now(),e.p=1,i=this.buildTestUrl(t,this.TEST_ENDPOINTS.webhook+n),e.n=2,this.sendTestRequest(i,{verification_token:"test_verification_"+Date.now()},r,"POST");case 2:if(a=e.v,s=Date.now()-o,!a.ok){e.n=6;break}return e.n=3,a.json();case 3:if(!(u=e.v).verification_token){e.n=4;break}return e.a(2,{success:!0,status:"success",message:"验证令牌测试成功",details:{responseTime:s,statusCode:a.status,body:u}});case 4:return e.a(2,{success:!1,status:"warning",message:"验证响应格式异常",details:{responseTime:s,statusCode:a.status,body:u},suggestions:["检查Webhook处理逻辑","确认返回格式正确"]});case 5:e.n=7;break;case 6:return e.a(2,{success:!1,status:"error",message:"验证失败 (".concat(a.status,")"),details:{responseTime:s,statusCode:a.status},suggestions:["检查令牌是否正确","确认Webhook已启用","检查服务器日志"]});case 7:e.n=9;break;case 8:return e.p=8,l=e.v,c=Date.now()-o,e.a(2,{success:!1,status:"error",message:"验证测试失败",details:{responseTime:c,error:l.message},suggestions:["检查网络连接","确认URL和令牌正确","检查服务器状态"]});case 9:return e.a(2)}},e,this,[[1,8]])})),function(e,t,n){return u.apply(this,arguments)})},{key:"testFullWebhook",value:(s=eo(Xr().m(function e(t,n,r){var o,i,a,s,u,c,l,f;return Xr().w(function(e){for(;;)switch(e.n){case 0:return o=[],e.n=1,this.testConnection(t,r);case 1:if(i=e.v,o.push(i),i.success){e.n=2;break}return e.a(2,{success:!1,status:"error",message:"基础连接测试失败，无法继续",details:i.details,suggestions:i.suggestions});case 2:return e.n=3,this.testVerification(t,n,r);case 3:return a=e.v,o.push(a),e.n=4,this.testWebhookEvent(t,n,r);case 4:return s=e.v,o.push(s),u=o.every(function(e){return e.success}),c=o.some(function(e){return"warning"===e.status}),l=o.reduce(function(e,t){var n;return e+((null===(n=t.details)||void 0===n?void 0:n.responseTime)||0)},0),f=o.flatMap(function(e){return e.suggestions||[]}),e.a(2,{success:u,status:u?c?"warning":"success":"error",message:u?"所有测试通过":"部分测试失败",details:{responseTime:l,statusCode:200},suggestions:$r(new Set(f))})}},e,this)})),function(e,t,n){return s.apply(this,arguments)})},{key:"testWebhookEvent",value:(a=eo(Xr().m(function e(t,n,r){var o,i,a,s,u,c,l,f;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:return o=Date.now(),e.p=1,i=this.buildTestUrl(t,this.TEST_ENDPOINTS.webhook+n),a={type:"page.updated",event:{type:"page.updated",object:"page",id:"test-page-id-"+Date.now()},timestamp:(new Date).toISOString()},e.n=2,this.sendTestRequest(i,a,r,"POST");case 2:if(s=e.v,u=Date.now()-o,!s.ok){e.n=4;break}return e.n=3,s.json();case 3:return c=e.v,e.a(2,{success:!0,status:"success",message:"事件处理测试成功",details:{responseTime:u,statusCode:s.status,body:c}});case 4:return e.a(2,{success:!1,status:"error",message:"事件处理失败 (".concat(s.status,")"),details:{responseTime:u,statusCode:s.status},suggestions:["检查事件处理逻辑","确认同步功能正常","查看服务器错误日志"]});case 5:e.n=7;break;case 6:return e.p=6,f=e.v,l=Date.now()-o,e.a(2,{success:!1,status:"error",message:"事件测试失败",details:{responseTime:l,error:f.message},suggestions:["检查Webhook处理器","确认事件格式正确","检查服务器配置"]});case 7:return e.a(2)}},e,this,[[1,6]])})),function(e,t,n){return a.apply(this,arguments)})},{key:"sendTestRequest",value:(i=eo(Xr().m(function e(t,n,r){var o,i,a,s,u,c=arguments;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:return o=c.length>3&&void 0!==c[3]?c[3]:"POST",i=new AbortController,a=setTimeout(function(){return i.abort()},r),e.p=1,e.n=2,fetch(t,{method:o,headers:{"Content-Type":"application/json","User-Agent":"NotionToWordPress-WebhookTester/1.0"},body:"POST"===o?JSON.stringify(n):void 0,signal:i.signal});case 2:return s=e.v,clearTimeout(a),e.a(2,s);case 3:throw e.p=3,u=e.v,clearTimeout(a),u;case 4:return e.a(2)}},e,null,[[1,3]])})),function(e,t,n){return i.apply(this,arguments)})},{key:"buildTestUrl",value:function(e,t){return e.replace(/\/$/,"")+(t.startsWith("/")?t:"/"+t)}},{key:"diagnoseWebhookIssues",value:(o=eo(Xr().m(function e(t,n){var r,o,i,a,s;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:if(r=[],o=[],e.p=1,t&&t.trim()){e.n=2;break}return r.push("Webhook URL为空"),o.push("请配置有效的Webhook URL"),e.a(2,{issues:r,recommendations:o});case 2:if(n&&n.trim()){e.n=3;break}return r.push("Webhook令牌为空"),o.push("请生成并配置Webhook令牌"),e.a(2,{issues:r,recommendations:o});case 3:try{"http:"===(i=new URL(t)).protocol&&(r.push("使用HTTP协议，存在安全风险"),o.push("建议使用HTTPS协议")),("localhost"===i.hostname||i.hostname.startsWith("127."))&&(r.push("使用本地地址，外部无法访问"),o.push("使用公网可访问的域名"))}catch(e){r.push("URL格式不正确"),o.push("请检查URL格式是否正确")}return e.n=4,this.testWebhook({url:t,token:n,testType:"connection"});case 4:(a=e.v).success||(r.push("连接测试失败: "+a.message),o.push.apply(o,$r(a.suggestions||[]))),e.n=6;break;case 5:e.p=5,s=e.v,r.push("诊断过程出错: "+s.message),o.push("请检查网络连接和配置");case 6:return e.a(2,{issues:r,recommendations:o})}},e,this,[[1,5]])})),function(e,t){return o.apply(this,arguments)})},{key:"generateTestReport",value:(r=eo(Xr().m(function e(t,n){var r,o,i,a,s,u,c,l,f,h;return Xr().w(function(e){for(;;)switch(e.p=e.n){case 0:r=[],o=0,i=[{name:"连接测试",type:"connection"},{name:"验证测试",type:"verification"},{name:"完整测试",type:"full"}];case 1:if(!(o<i.length)){e.n=6;break}return a=i[o],e.p=2,e.n=3,this.testWebhook({url:t,token:n,testType:a.type});case 3:s=e.v,r.push(s),e.n=5;break;case 4:e.p=4,h=e.v,r.push({success:!1,status:"error",message:"".concat(a.name,"执行失败"),details:{error:h.message}});case 5:o++,e.n=1;break;case 6:return u=r.filter(function(e){return e.success}).length,c=Math.round(u/r.length*100),l=100===c?"Webhook配置完全正常":c>=70?"Webhook配置基本正常，有少量问题":"Webhook配置存在问题，需要修复",f=$r(new Set(r.flatMap(function(e){return e.suggestions||[]}))),e.a(2,{summary:l,details:r,score:c,recommendations:f})}},e,this,[[2,4]])})),function(e,t){return r.apply(this,arguments)})}],t&&to(e.prototype,t),n&&to(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,o,i,a,s,u,c,l}();no(oo,"DEFAULT_TIMEOUT",1e4),no(oo,"TEST_ENDPOINTS",{connection:"/wp-admin/admin-ajax.php",webhook:"/wp-json/notion-to-wordpress/v1/webhook/"});function io(e){return io="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},io(e)}function ao(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return so(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(so(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,so(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,so(f,"constructor",c),so(c,"constructor",u),u.displayName="GeneratorFunction",so(c,o,"GeneratorFunction"),so(f),so(f,o,"Generator"),so(f,r,function(){return this}),so(f,"toString",function(){return"[object Generator]"}),(ao=function(){return{w:i,m:h}})()}function so(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}so=function(e,t,n,r){function i(t,n){so(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},so(e,t,n,r)}function uo(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function co(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){uo(i,r,o,a,s,"next",e)}function s(e){uo(i,r,o,a,s,"throw",e)}a(void 0)})}}function lo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function fo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lo(Object(n),!0).forEach(function(t){po(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function ho(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,yo(r.key),r)}}function po(e,t,n){return(t=yo(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yo(e){var t=function(e,t){if("object"!=io(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=io(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==io(t)?t:t+""}var vo=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),po(this,"options",void 0),po(this,"status",void 0),po(this,"refreshTimer",null),po(this,"elements",{}),e.instance)return e.instance;e.instance=this,this.options=fo({autoValidate:!0,autoTest:!1,refreshInterval:3e4},t),this.status={enabled:!1,configured:!1,tested:!1,issues:[]},this.init()}return t=e,n=[{key:"init",value:function(){this.bindElements(),this.setupEventListeners(),this.loadCurrentConfig(),this.options.refreshInterval>0&&this.startStatusRefresh(),(0,o.Ic)("webhook:manager:initialized")}},{key:"bindElements",value:function(){this.elements={enabledCheckbox:document.getElementById("webhook_enabled"),tokenInput:document.getElementById("webhook_token"),urlInput:document.getElementById("webhook_url"),verificationTokenInput:document.getElementById("verification_token"),generateTokenButton:document.getElementById("generate-webhook-token"),testWebhookButton:document.getElementById("test-webhook"),refreshTokenButton:document.getElementById("refresh-verification-token"),copyUrlButton:document.querySelector(".copy-webhook-url"),statusIndicator:document.getElementById("webhook-status"),settingsContainer:document.getElementById("webhook-settings")}}},{key:"setupEventListeners",value:function(){var e=this;this.elements.enabledCheckbox&&this.elements.enabledCheckbox.addEventListener("change",function(t){var n=t.target.checked;e.handleWebhookToggle(n)}),this.elements.generateTokenButton&&this.elements.generateTokenButton.addEventListener("click",function(){e.generateNewToken()}),this.elements.testWebhookButton&&this.elements.testWebhookButton.addEventListener("click",function(){e.testWebhook()}),this.elements.refreshTokenButton&&this.elements.refreshTokenButton.addEventListener("click",function(){e.refreshVerificationToken()}),this.elements.copyUrlButton&&this.elements.copyUrlButton.addEventListener("click",function(){e.copyWebhookUrl()}),this.elements.tokenInput&&this.elements.tokenInput.addEventListener("input",function(){e.options.autoValidate&&e.validateCurrentConfig()}),(0,o.on)("form:settings:submit",function(){e.handleSettingsSave()})}},{key:"loadCurrentConfig",value:(l=co(ao().m(function e(){var t,n;return ao().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_get_webhook_config",{});case 1:(t=e.v)&&t.data&&(n=t.data,this.updateStatus({enabled:n.enabled||!1,configured:!(!n.token||!n.url)}),this.updateUI(n)),e.n=3;break;case 2:e.p=2,e.v,(0,s.Qg)("加载Webhook配置失败");case 3:return e.a(2)}},e,this,[[0,2]])})),function(){return l.apply(this,arguments)})},{key:"handleWebhookToggle",value:function(e){this.updateStatus({enabled:e}),this.elements.settingsContainer&&(e?(this.elements.settingsContainer.classList.remove("notion-wp-hidden"),this.elements.settingsContainer.style.display="block"):(this.elements.settingsContainer.classList.add("notion-wp-hidden"),this.elements.settingsContainer.style.display="none")),e&&!this.status.configured&&this.generateNewToken(),(0,o.Ic)("webhook:toggled",{enabled:e})}},{key:"generateNewToken",value:function(){var e=Wr.generateSecureToken(32);this.elements.tokenInput&&(this.elements.tokenInput.value=e),this.updateWebhookUrl(e),this.options.autoValidate&&this.validateCurrentConfig(),(0,s.Te)("已生成新的Webhook令牌"),(0,o.Ic)("webhook:token:generated",{token:e})}},{key:"updateWebhookUrl",value:function(e){var t=window.location.origin,n=Wr.buildWebhookUrl(t,e);this.elements.urlInput&&(this.elements.urlInput.value=n)}},{key:"testWebhook",value:(c=co(ao().m(function e(){var t,n,r,i;return ao().w(function(e){for(;;)switch(e.p=e.n){case 0:if((t=this.getCurrentConfig()).enabled){e.n=1;break}return(0,s.Qg)("请先启用Webhook功能"),e.a(2);case 1:if(t.token&&t.url){e.n=2;break}return(0,s.Qg)("请先配置Webhook令牌和URL"),e.a(2);case 2:return this.elements.testWebhookButton&&(this.elements.testWebhookButton.disabled=!0,this.elements.testWebhookButton.textContent="测试中..."),e.p=3,(0,s.cf)("正在测试Webhook连接..."),e.n=4,oo.testWebhook({url:t.url,token:t.token,testType:"full"});case 4:n=e.v,this.updateStatus({tested:!0,lastTest:new Date,lastTestResult:n}),n.success?(0,s.Te)("Webhook测试成功！响应时间: ".concat(null===(r=n.details)||void 0===r?void 0:r.responseTime,"ms")):((0,s.Qg)("Webhook测试失败: ".concat(n.message)),n.suggestions&&n.suggestions.length>0&&(0,s.cf)("建议: "+n.suggestions.join(", "))),(0,o.Ic)("webhook:tested",{result:n}),e.n=6;break;case 5:e.p=5,i=e.v,(0,s.Qg)("Webhook测试执行失败"),this.updateStatus({tested:!0,lastTest:new Date,issues:["测试执行失败: "+i.message]});case 6:return e.p=6,this.elements.testWebhookButton&&(this.elements.testWebhookButton.disabled=!1,this.elements.testWebhookButton.textContent="测试Webhook"),e.f(6);case 7:return e.a(2)}},e,this,[[3,5,6,7]])})),function(){return c.apply(this,arguments)})},{key:"refreshVerificationToken",value:(u=co(ao().m(function e(){var t,n;return ao().w(function(e){for(;;)switch(e.p=e.n){case 0:return this.elements.refreshTokenButton&&(this.elements.refreshTokenButton.disabled=!0),e.p=1,e.n=2,(0,a.bE)("notion_to_wordpress_refresh_verification_token",{});case 2:(n=e.v)&&null!==(t=n.data)&&void 0!==t&&t.verification_token?(this.elements.verificationTokenInput&&(this.elements.verificationTokenInput.value=n.data.verification_token),(0,s.Te)("验证令牌已刷新"),(0,o.Ic)("webhook:verification:refreshed",{token:n.data.verification_token})):(0,s.Qg)("刷新验证令牌失败"),e.n=4;break;case 3:e.p=3,e.v,(0,s.Qg)("刷新验证令牌失败");case 4:return e.p=4,this.elements.refreshTokenButton&&(this.elements.refreshTokenButton.disabled=!1),e.f(4);case 5:return e.a(2)}},e,this,[[1,3,4,5]])})),function(){return u.apply(this,arguments)})},{key:"copyWebhookUrl",value:(i=co(ao().m(function e(){var t,n,r;return ao().w(function(e){for(;;)switch(e.p=e.n){case 0:if(n=null===(t=this.elements.urlInput)||void 0===t?void 0:t.value){e.n=1;break}return(0,s.Qg)("没有可复制的URL"),e.a(2);case 1:return e.p=1,e.n=2,navigator.clipboard.writeText(n);case 2:(0,s.Te)("Webhook URL已复制到剪贴板"),(0,o.Ic)("webhook:url:copied",{url:n}),e.n=4;break;case 3:e.p=3,e.v,(r=document.createElement("textarea")).value=n,document.body.appendChild(r),r.select(),document.execCommand("copy"),document.body.removeChild(r),(0,s.Te)("Webhook URL已复制到剪贴板");case 4:return e.a(2)}},e,this,[[1,3]])})),function(){return i.apply(this,arguments)})},{key:"validateCurrentConfig",value:function(){var e=this.getCurrentConfig();if(e.enabled){var t=Wr.validateWebhookConfig(e);this.updateStatus({configured:t.isValid,issues:t.errors}),this.updateValidationUI(t),(0,o.Ic)("webhook:validated",{result:t})}}},{key:"getCurrentConfig",value:function(){var e,t,n,r;return{enabled:(null===(e=this.elements.enabledCheckbox)||void 0===e?void 0:e.checked)||!1,token:(null===(t=this.elements.tokenInput)||void 0===t?void 0:t.value)||"",url:(null===(n=this.elements.urlInput)||void 0===n?void 0:n.value)||"",verificationToken:(null===(r=this.elements.verificationTokenInput)||void 0===r?void 0:r.value)||""}}},{key:"updateStatus",value:function(e){this.status=fo(fo({},this.status),e),this.updateStatusUI(),(0,o.Ic)("webhook:status:changed",{status:this.status})}},{key:"updateUI",value:function(e){this.elements.enabledCheckbox&&(this.elements.enabledCheckbox.checked=e.enabled||!1),this.elements.tokenInput&&(this.elements.tokenInput.value=e.token||""),this.elements.urlInput&&(this.elements.urlInput.value=e.url||""),this.elements.verificationTokenInput&&(this.elements.verificationTokenInput.value=e.verificationToken||""),this.elements.settingsContainer&&(e.enabled?this.elements.settingsContainer.classList.remove("notion-wp-hidden"):this.elements.settingsContainer.classList.add("notion-wp-hidden"))}},{key:"updateStatusUI",value:function(){if(this.elements.statusIndicator){var e=this.status,t=e.enabled,n=e.configured,r=e.tested,o=e.issues,i="status-disabled",a="未启用";t&&(o.length>0?(i="status-error",a="配置错误"):n&&r?(i="status-success",a="正常运行"):n?(i="status-warning",a="未测试"):(i="status-warning",a="未配置")),this.elements.statusIndicator.className="webhook-status ".concat(i),this.elements.statusIndicator.textContent=a}}},{key:"updateValidationUI",value:function(e){e.errors.length,e.warnings.length}},{key:"handleSettingsSave",value:function(){var e=this.getCurrentConfig();e.enabled&&this.options.autoValidate&&this.validateCurrentConfig(),(0,o.Ic)("webhook:settings:saved",{config:e})}},{key:"startStatusRefresh",value:function(){var e=this;this.refreshTimer=setInterval(function(){e.status.enabled&&e.loadCurrentConfig()},this.options.refreshInterval)}},{key:"stopStatusRefresh",value:function(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)}},{key:"getStatus",value:function(){return fo({},this.status)}},{key:"getConfigurationSuggestions",value:function(){return Wr.getConfigurationSuggestions()}},{key:"getSecurityBestPractices",value:function(){return Wr.getSecurityBestPractices()}},{key:"destroy",value:function(){this.stopStatusRefresh(),e.instance=null,(0,o.Ic)("webhook:manager:destroyed")}}],r=[{key:"getInstance",value:function(t){return e.instance||(e.instance=new e(t)),e.instance}}],n&&ho(t.prototype,n),r&&ho(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r,i,u,c,l}();po(vo,"instance",null);var mo=vo.getInstance();function bo(e){return bo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bo(e)}function go(){var e,t,n="function"==typeof Symbol?Symbol:{},r=n.iterator||"@@iterator",o=n.toStringTag||"@@toStringTag";function i(n,r,o,i){var u=r&&r.prototype instanceof s?r:s,c=Object.create(u.prototype);return wo(c,"_invoke",function(n,r,o){var i,s,u,c=0,l=o||[],f=!1,h={p:0,n:0,v:e,a:p,f:p.bind(e,4),d:function(t,n){return i=t,s=0,u=e,h.n=n,a}};function p(n,r){for(s=n,u=r,t=0;!f&&c&&!o&&t<l.length;t++){var o,i=l[t],p=h.p,d=i[2];n>3?(o=d===r)&&(u=i[(s=i[4])?5:(s=3,3)],i[4]=i[5]=e):i[0]<=p&&((o=n<2&&p<i[1])?(s=0,h.v=r,h.n=i[1]):p<d&&(o=n<3||i[0]>r||r>d)&&(i[4]=n,i[5]=r,h.n=d,s=0))}if(o||n>1)return a;throw f=!0,r}return function(o,l,d){if(c>1)throw TypeError("Generator is already running");for(f&&1===l&&p(l,d),s=l,u=d;(t=s<2?e:u)||!f;){i||(s?s<3?(s>1&&(h.n=-1),p(s,u)):h.n=u:h.v=u);try{if(c=2,i){if(s||(o="next"),t=i[o]){if(!(t=t.call(i,u)))throw TypeError("iterator result is not an object");if(!t.done)return t;u=t.value,s<2&&(s=0)}else 1===s&&(t=i.return)&&t.call(i),s<2&&(u=TypeError("The iterator does not provide a '"+o+"' method"),s=1);i=e}else if((t=(f=h.n<0)?u:n.call(r,h))!==a)break}catch(t){i=e,s=1,u=t}finally{c=1}}return{value:t,done:f}}}(n,o,i),!0),c}var a={};function s(){}function u(){}function c(){}t=Object.getPrototypeOf;var l=[][r]?t(t([][r]())):(wo(t={},r,function(){return this}),t),f=c.prototype=s.prototype=Object.create(l);function h(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,c):(e.__proto__=c,wo(e,o,"GeneratorFunction")),e.prototype=Object.create(f),e}return u.prototype=c,wo(f,"constructor",c),wo(c,"constructor",u),u.displayName="GeneratorFunction",wo(c,o,"GeneratorFunction"),wo(f),wo(f,o,"Generator"),wo(f,r,function(){return this}),wo(f,"toString",function(){return"[object Generator]"}),(go=function(){return{w:i,m:h}})()}function wo(e,t,n,r){var o=Object.defineProperty;try{o({},"",{})}catch(e){o=0}wo=function(e,t,n,r){function i(t,n){wo(e,t,function(e){return this._invoke(t,n,e)})}t?o?o(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r}):e[t]=n:(i("next",0),i("throw",1),i("return",2))},wo(e,t,n,r)}function ko(e,t,n,r,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void n(e)}s.done?t(u):Promise.resolve(u).then(r,o)}function So(e){return function(){var t=this,n=arguments;return new Promise(function(r,o){var i=e.apply(t,n);function a(e){ko(i,r,o,a,s,"next",e)}function s(e){ko(i,r,o,a,s,"throw",e)}a(void 0)})}}function Oo(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],u=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return To(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?To(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function To(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function Eo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function jo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Eo(Object(n),!0).forEach(function(t){Po(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Eo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Io(r.key),r)}}function Po(e,t,n){return(t=Io(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Io(e){var t=function(e,t){if("object"!=bo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=bo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==bo(t)?t:t+""}var Co=new(function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Po(this,"initialized",!1)},t=[{key:"init",value:function(){var e;this.initialized||(null!==(e=window.notionToWp)&&void 0!==e&&e.debug_mode&&o.Bt.setDebug(!0),this.initializeComponents(),this.bindGlobalEvents(),this.initialized=!0,(0,o.Ic)("admin:initialized"))}},{key:"initializeComponents",value:function(){this.initializePerformanceOptimization(),this.initializeStateManagement(),this.initializeSyncFeatures(),this.initializeQueueMonitoring(),this.initializeComponentManager(),this.initializeAdminInteractions(),this.initializeSyncProgressManager(),this.initializeWebhookManager(),this.initializeDatabaseManager(),this.initializeLogManager(),this.initializeSettingsManager(),this.initializeErrorManager(),(0,o.Ic)("admin:components:init")}},{key:"initializeComponentManager",value:function(){var e=this;jn.init(),(0,o.on)("component:mount",function(e,t){}),(0,o.on)("component:unmount",function(e,t){}),(0,o.on)("tab:change",function(t,n){e.handleTabChange(n.activeTab,n.previousTab)}),(0,o.on)("form:submit",function(t,n){e.handleFormSubmit(n)}),(0,o.on)("form:success",function(e,t){})}},{key:"initializePerformanceOptimization",value:function(){he.startTimer("admin_init"),re.preloadCritical().catch(console.error),ke.smartPreload().catch(console.error),(0,o.on)("performance:metric",function(e,t){"long_task_duration"===t.name&&t.value}),(0,o.on)("performance:report",function(e,t){}),(0,o.on)("lazy:loaded",function(e,t){}),(0,o.on)("lazy:error",function(e,t){}),(0,o.on)("resource:preloaded",function(e,t){t.success})}},{key:"initializeAdminInteractions",value:function(){gr.init(),(0,o.on)("admin:interactions:initialized",function(){}),(0,o.on)("admin:interactions:destroyed",function(){}),(0,o.on)("admin:stats:changed",function(e,t){})}},{key:"initializeSyncProgressManager",value:function(){(0,o.on)("sync:progress:started",function(e,t){}),(0,o.on)("sync:progress:completed",function(e,t){}),(0,o.on)("sync:progress:error",function(e,t){})}},{key:"initializeWebhookManager",value:function(){(0,o.on)("webhook:status:changed",function(e,t){}),(0,o.on)("webhook:tested",function(e,t){}),(0,o.on)("webhook:token:generated",function(e,t){}),(0,o.on)("webhook:validation:result",function(e,t){t.result.isValid})}},{key:"initializeDatabaseManager",value:function(){(0,o.on)("database:records:loaded",function(e,t){}),(0,o.on)("database:records:error",function(e,t){}),(0,o.on)("database:state:changed",function(e,t){}),(0,o.on)("database:view:type:changed",function(e,t){})}},{key:"initializeLogManager",value:function(){(0,o.on)("log:manager:initialized",function(){}),(0,o.on)("log:files:loaded",function(e,t){}),(0,o.on)("log:entries:loaded",function(e,t){}),(0,o.on)("log:content:loaded",function(e,t){}),(0,o.on)("log:cleared",function(){}),(0,o.on)("log:exported",function(e,t){}),(0,o.on)("log:auto:refresh:started",function(){}),(0,o.on)("log:auto:refresh:stopped",function(){})}},{key:"initializeSettingsManager",value:function(){(0,o.on)("settings:manager:initialized",function(){}),(0,o.on)("settings:loaded",function(e,t){}),(0,o.on)("settings:saved",function(e,t){}),(0,o.on)("settings:reset",function(e,t){}),(0,o.on)("settings:changed",function(e,t){}),(0,o.on)("settings:imported",function(e,t){}),(0,o.on)("settings:exported",function(e,t){}),(0,o.on)("settings:connection:success",function(){}),(0,o.on)("settings:connection:failed",function(e,t){})}},{key:"initializeErrorManager",value:function(){(0,o.on)("error:manager:initialized",function(){}),(0,o.on)("error:handled",function(e,t){}),(0,o.on)("error:resolved",function(e,t){}),(0,o.on)("error:retry",function(e,t){}),(0,o.on)("error:admin:notify",function(e,t){}),(0,o.on)("error:history:cleared",function(){})}},{key:"initializeStateManagement",value:function(){var e=this;i.Gi.subscribe(function(t,n,r){e.updateUIFromState(t,n)}),i.Gi.setState({ui:jo(jo({},i.Gi.getState().ui),{},{activeTab:this.getCurrentTab()})})}},{key:"initializeSyncFeatures",value:function(){(0,o.on)("sync:start",function(e,t){(0,s.cf)("".concat(t.syncType,"已开始"))}),(0,o.on)("sync:complete",function(e,t){(0,s.Te)("同步完成")}),(0,o.on)("sync:error",function(e,t){(0,s.Qg)("同步失败: ".concat(t.error.message))})}},{key:"initializeQueueMonitoring",value:function(){var e=this;_.startMonitoring(),(0,o.on)("queue:status:update",function(t,n){e.updateQueueDisplay(n)})}},{key:"updateUIFromState",value:function(e,t){e.sync!==t.sync&&this.updateSyncDisplay(e.sync),e.queue!==t.queue&&this.updateQueueDisplay(e.queue),e.ui.activeTab!==t.ui.activeTab&&this.updateActiveTab(e.ui.activeTab)}},{key:"updateSyncDisplay",value:function(e){var t=document.querySelector("#sync-status");if(t&&(t.textContent=this.getSyncStatusText(e.status),t.className="sync-status sync-status-".concat(e.status)),"running"===e.status&&e.total>0){var n=Math.round(e.progress/e.total*100),r=document.querySelector("#sync-progress");r&&(r.textContent="".concat(n,"% (").concat(e.progress,"/").concat(e.total,")"))}}},{key:"updateQueueDisplay",value:function(e){var t={total:document.querySelector("#queue-total"),pending:document.querySelector("#queue-pending"),processing:document.querySelector("#queue-processing"),completed:document.querySelector("#queue-completed"),failed:document.querySelector("#queue-failed")};Object.entries(t).forEach(function(t){var n=Oo(t,2),r=n[0],o=n[1];o&&void 0!==e[r+"_jobs"]&&(o.textContent=e[r+"_jobs"].toString())})}},{key:"updateActiveTab",value:function(e){document.querySelectorAll(".nav-tab").forEach(function(e){e.classList.remove("nav-tab-active")});var t=document.querySelector('[data-tab="'.concat(e,'"]'));t&&t.classList.add("nav-tab-active")}},{key:"getSyncStatusText",value:function(e){return{idle:"空闲",running:"运行中",paused:"已暂停",completed:"已完成",error:"错误",cancelled:"已取消"}[e]||e}},{key:"getCurrentTab",value:function(){var e=document.querySelector(".nav-tab-active");return(null==e?void 0:e.getAttribute("data-tab"))||"sync"}},{key:"bindGlobalEvents",value:function(){window.addEventListener("beforeunload",function(){(0,o.Ic)("admin:beforeunload")}),window.addEventListener("resize",function(){(0,o.Ic)("admin:resize",{width:window.innerWidth,height:window.innerHeight})}),document.addEventListener("visibilitychange",function(){(0,o.Ic)("admin:visibility:change",{visible:!document.hidden})})}},{key:"startSync",value:(y=So(go().m(function e(t){var n,r,o,i=arguments;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return n=i.length>1&&void 0!==i[1]?i[1]:{},e.p=1,e.n=2,b.startSync({syncType:t,incremental:n.incremental||!1,checkDeletions:n.checkDeletions||!1,batchSize:n.batchSize||10});case 2:(r=e.v).success||(0,s.Qg)(r.message),e.n=4;break;case 3:e.p=3,o=e.v,(0,s.Qg)("启动同步失败: ".concat(o.message));case 4:return e.a(2)}},e,null,[[1,3]])})),function(e){return y.apply(this,arguments)})},{key:"stopSync",value:(d=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,b.stopSync();case 1:(t=e.v).success||(0,s.Qg)(t.message),e.n=3;break;case 2:e.p=2,n=e.v,(0,s.Qg)("停止同步失败: ".concat(n.message));case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return d.apply(this,arguments)})},{key:"pauseSync",value:(p=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,b.pauseSync();case 1:(t=e.v).success||(0,s.Qg)(t.message),e.n=3;break;case 2:e.p=2,n=e.v,(0,s.Qg)("暂停同步失败: ".concat(n.message));case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return p.apply(this,arguments)})},{key:"resumeSync",value:(h=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,b.resumeSync();case 1:(t=e.v).success||(0,s.Qg)(t.message),e.n=3;break;case 2:e.p=2,n=e.v,(0,s.Qg)("恢复同步失败: ".concat(n.message));case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return h.apply(this,arguments)})},{key:"cleanupQueue",value:(f=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,_.cleanupQueue({removeCompleted:!0,removeFailed:!1,olderThan:24});case 1:(t=e.v).success?(0,s.Te)(t.message):(0,s.Qg)(t.message),e.n=3;break;case 2:e.p=2,n=e.v,(0,s.Qg)("清理队列失败: ".concat(n.message));case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return f.apply(this,arguments)})},{key:"handleTabChange",value:function(e,t){switch(he.startTimer("tab_switch_".concat(e)),e){case"sync":this.refreshSyncStatus();break;case"queue":this.refreshQueueStatus();break;case"settings":this.loadSettingsModule();break;case"logs":this.loadLogsModule()}var n=new URL(window.location.href);n.searchParams.set("tab",e),window.history.replaceState({},"",n.toString()),he.endTimer("tab_switch_".concat(e),{tab:e})}},{key:"handleFormSubmit",value:(l=So(go().m(function e(t){return go().w(function(e){for(;;)switch(e.n){case 0:case 1:return e.a(2)}},e)})),function(e){return l.apply(this,arguments)})},{key:"refreshSyncStatus",value:(c=So(go().m(function e(){var t;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,b.getSyncStatus();case 1:(t=e.v)&&i.Gi.setState({sync:t}),e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return c.apply(this,arguments)})},{key:"refreshQueueStatus",value:(u=So(go().m(function e(){var t;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,_.getQueueStatus();case 1:(t=e.v)&&i.Gi.updateQueueStatus(t),e.n=3;break;case 2:e.p=2,e.v;case 3:return e.a(2)}},e,null,[[0,2]])})),function(){return u.apply(this,arguments)})},{key:"loadSettingsModule",value:(a=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:if(e.p=0,t=document.querySelector("#settings-container")){e.n=1;break}return e.a(2);case 1:if(!ke.isChunkLoaded("admin-settings")){e.n=2;break}return e.a(2);case 2:return t.innerHTML='<div class="loading-indicator">加载设置中...</div>',e.n=3,ke.loadChunk("admin-settings");case 3:(n=e.v)&&n.default&&n.default(t),e.n=5;break;case 4:e.p=4,e.v,(0,s.Qg)("加载设置模块失败");case 5:return e.a(2)}},e,null,[[0,4]])})),function(){return a.apply(this,arguments)})},{key:"loadLogsModule",value:(r=So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:if(e.p=0,t=document.querySelector("#logs-container")){e.n=1;break}return e.a(2);case 1:if(!ke.isChunkLoaded("admin-logs")){e.n=2;break}return e.a(2);case 2:return t.innerHTML='<div class="loading-indicator">加载日志中...</div>',e.n=3,ke.loadChunk("admin-logs");case 3:(n=e.v)&&n.default&&n.default(t),e.n=5;break;case 4:e.p=4,e.v,(0,s.Qg)("加载日志模块失败");case 5:return e.a(2)}},e,null,[[0,4]])})),function(){return r.apply(this,arguments)})},{key:"destroy",value:function(){this.initialized&&(he.endTimer("admin_init",{phase:"destroy"}),_.stopMonitoring(),L.hide(),jn.destroyAll(),he.cleanup(),z.cleanup(),re.cleanup(),ke.cleanup(),gr.destroy(),Gr.destroy(),mo.destroy(),Ot.destroy(),Qt.destroy(),rn.destroy(),mn.destroy(),(0,o.Ic)("admin:destroy"),o.Bt.removeAllListeners(),this.initialized=!1)}}],t&&_o(e.prototype,t),n&&_o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n,r,a,u,c,l,f,h,p,d,y}());window.NotionWpAdmin=Co,(0,r.Gc)(function(){Co.init()}),(0,o.on)("admin:test:ajax",So(go().m(function e(){var t,n;return go().w(function(e){for(;;)switch(e.p=e.n){case 0:return e.p=0,e.n=1,(0,a.bE)("notion_to_wordpress_test",{test_data:"Hello from TypeScript!"});case 1:t=e.v,(0,o.Ic)("admin:test:ajax:success",t),e.n=3;break;case 2:e.p=2,n=e.v,(0,o.Ic)("admin:test:ajax:error",n);case 3:return e.a(2)}},e,null,[[0,2]])})))},7518:(e,t,n)=>{n.d(t,{Gi:()=>w});n(2675),n(9463),n(2259),n(5700),n(8706),n(2008),n(3418),n(3792),n(4782),n(9572),n(2010),n(2892),n(3851),n(1278),n(875),n(9432),n(287),n(6099),n(825),n(7495),n(8781),n(1415),n(7764),n(3500),n(2953);var r=n(3040),o=n(6653);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e){return function(e){if(Array.isArray(e))return h(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||f(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t,n){return t=c(t),function(e,t){if(t&&("object"==i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,u()?Reflect.construct(t,n||[],c(e).constructor):t.apply(e,n))}function u(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(u=function(){return!!e})()}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}function f(e,t){if(e){if("string"==typeof e)return h(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach(function(t){b(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function v(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,g(r.key),r)}}function m(e,t,n){return t&&v(e.prototype,t),n&&v(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function b(e,t,n){return(t=g(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}var w=new(function(e){function t(){var e;y(this,t);return(e=s(this,t,[{sync:{status:"idle",progress:0,total:0,current_item:"",start_time:0,sync_type:"",sync_id:"",errors:[],warnings:[]},queue:{total_jobs:0,pending_jobs:0,processing_jobs:0,completed_jobs:0,failed_jobs:0,queue_size:0,is_processing:!1,last_processed:""},ui:{activeTab:"sync",loading:!1,notifications:[]},config:{auto_refresh:!0,refresh_interval:5e3,page_visible:!0}},{storageKey:"notion_wp_app_state",autoSave:!0}])).setupActionHandlers(),e.setupVisibilityHandling(),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,e),m(t,[{key:"setupActionHandlers",value:function(){var e=this;r.Bt.on("action:START_SYNC",function(t,n){e.setState({sync:d(d({},e.getState().sync),{},{status:"running",start_time:Date.now(),sync_type:n.payload.syncType,sync_id:n.payload.syncId,progress:0,errors:[],warnings:[]})},n)}),r.Bt.on("action:UPDATE_SYNC_PROGRESS",function(t,n){e.setState({sync:d(d({},e.getState().sync),{},{progress:n.payload.progress,total:n.payload.total,current_item:n.payload.currentItem||""})},n)}),r.Bt.on("action:COMPLETE_SYNC",function(t,n){e.setState({sync:d(d({},e.getState().sync),{},{status:"completed",progress:e.getState().sync.total})},n)}),r.Bt.on("action:ERROR_SYNC",function(t,n){var r=e.getState();e.setState({sync:d(d({},r.sync),{},{status:"error",errors:[].concat(a(r.sync.errors),[n.payload.error])})},n)}),r.Bt.on("action:UPDATE_QUEUE_STATUS",function(t,n){e.setState({queue:d(d({},e.getState().queue),n.payload)},n)}),r.Bt.on("action:SET_ACTIVE_TAB",function(t,n){e.setState({ui:d(d({},e.getState().ui),{},{activeTab:n.payload.tab})},n)}),r.Bt.on("action:ADD_NOTIFICATION",function(t,n){var r=e.getState();e.setState({ui:d(d({},r.ui),{},{notifications:[].concat(a(r.ui.notifications),[n.payload.notification])})},n)})}},{key:"setupVisibilityHandling",value:function(){var e=this;document.addEventListener("visibilitychange",function(){e.setState({config:d(d({},e.getState().config),{},{page_visible:!document.hidden})})}),window.addEventListener("focus",function(){e.setState({config:d(d({},e.getState().config),{},{page_visible:!0})})})}},{key:"startSync",value:function(e,t){this.dispatch({type:"START_SYNC",payload:{syncType:e,syncId:t}})}},{key:"updateSyncProgress",value:function(e,t,n){this.dispatch({type:"UPDATE_SYNC_PROGRESS",payload:{progress:e,total:t,currentItem:n}})}},{key:"completeSync",value:function(){this.dispatch({type:"COMPLETE_SYNC",payload:{}})}},{key:"syncError",value:function(e){this.dispatch({type:"ERROR_SYNC",payload:{error:e}})}},{key:"updateQueueStatus",value:function(e){this.dispatch({type:"UPDATE_QUEUE_STATUS",payload:e})}},{key:"addNotification",value:function(e){var t=d(d({},e),{},{id:"notification_".concat(Date.now(),"_").concat(Math.random().toString(36).substring(2,11)),timestamp:Date.now()});this.dispatch({type:"ADD_NOTIFICATION",payload:{notification:t}})}}])}(function(){return m(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};y(this,e),b(this,"state",void 0),b(this,"listeners",new Set),b(this,"middleware",[]),b(this,"storageKey",void 0),b(this,"autoSave",!1),this.state=t,this.storageKey=n.storageKey,this.autoSave=n.autoSave||!1,this.storageKey&&this.restoreState()},[{key:"getState",value:function(){return d({},this.state)}},{key:"setState",value:function(e,t){var n=this,o=d({},this.state);this.state=d(d({},this.state),e),this.listeners.forEach(function(e){try{e(n.state,o,t||{type:"SET_STATE"})}catch(e){}}),this.autoSave&&this.storageKey&&this.saveState(),r.Bt.emit("state:change",{state:this.state,prevState:o,action:t||{type:"SET_STATE"}})}},{key:"dispatch",value:function(e){var t,n=e,o=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=f(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw i}}}}(this.middleware);try{for(o.s();!(t=o.n()).done;){if(!(n=(0,t.value)(n,this.state)))break}}catch(e){o.e(e)}finally{o.f()}n&&(r.Bt.emit("state:action",n),r.Bt.emit("action:".concat(n.type),n))}},{key:"subscribe",value:function(e){var t=this;return this.listeners.add(e),function(){t.listeners.delete(e)}}},{key:"use",value:function(e){this.middleware.push(e)}},{key:"saveState",value:function(){if(this.storageKey)try{o.Lr.set(this.storageKey,this.state,{ttl:36e5})}catch(e){}}},{key:"restoreState",value:function(){if(this.storageKey)try{var e=o.Lr.get(this.storageKey);e&&(this.state=d(d({},this.state),e))}catch(e){}}},{key:"clearStoredState",value:function(){this.storageKey&&o.Lr.remove(this.storageKey)}},{key:"reset",value:function(e){e&&(this.state=e),this.clearStoredState(),r.Bt.emit("state:reset",{state:this.state})}}])}()))},8055:(e,t,n)=>{n.d(t,{Az:()=>r});n(2675),n(9463),n(2259),n(5700),n(8706),n(8980),n(3418),n(3792),n(2062),n(4782),n(4554),n(9572),n(2010),n(2892),n(9868),n(9085),n(875),n(287),n(6099),n(3362),n(7495),n(8781),n(1415),n(7764),n(8156),n(5440),n(2953);function r(e){var t=Math.floor(e/1e3),n=Math.floor(t/60),r=Math.floor(n/60),o=Math.floor(r/24);return o>0?"".concat(o,"天").concat(r%24,"小时"):r>0?"".concat(r,"小时").concat(n%60,"分钟"):n>0?"".concat(n,"分钟").concat(t%60,"秒"):"".concat(t,"秒")}},9223:(e,t,n)=>{n.d(t,{$:()=>f});n(2675),n(9463),n(2259),n(5700),n(2008),n(8980),n(3792),n(4554),n(9572),n(2010),n(2892),n(3851),n(1278),n(9432),n(6099),n(7764),n(3500),n(2953);var r=n(3040),o=n(7518),i=n(404);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,l(r.key),r)}}function c(e,t,n){return(t=l(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}var f=function(){return e=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),c(this,"element",null),c(this,"options",void 0),c(this,"state",void 0),c(this,"eventListeners",[]),c(this,"stateUnsubscribe",null),this.options=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach(function(t){c(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({autoInit:!0,destroyOnUnmount:!0},t),this.state={mounted:!1,initialized:!1,destroyed:!1},this.options.autoInit&&this.init()},(t=[{key:"init",value:function(){this.state.initialized||this.state.destroyed||(this.options.element?this.element=this.options.element:this.options.selector&&(this.element=(0,i.iT)(this.options.selector)),this.element&&(this.state.initialized=!0,this.setupStateListeners(),this.onInit(),this.mount()))}},{key:"mount",value:function(){this.state.mounted||this.state.destroyed||!this.element||(this.state.mounted=!0,this.bindEvents(),this.onMount(),this.emit("component:mount",{component:this}))}},{key:"unmount",value:function(){this.state.mounted&&!this.state.destroyed&&(this.state.mounted=!1,this.unbindEvents(),this.onUnmount(),this.emit("component:unmount",{component:this}),this.options.destroyOnUnmount&&this.destroy())}},{key:"destroy",value:function(){this.state.destroyed||(this.state.mounted&&this.unmount(),this.state.destroyed=!0,this.stateUnsubscribe&&(this.stateUnsubscribe(),this.stateUnsubscribe=null),this.onDestroy(),this.element=null,this.eventListeners=[],this.emit("component:destroy",{component:this}))}},{key:"render",value:function(){this.state.mounted&&!this.state.destroyed&&this.onRender()}},{key:"addEventListener",value:function(e,t,n,r){e.addEventListener(t,n,r),this.eventListeners.push({element:e,event:t,handler:n})}},{key:"removeEventListener",value:function(e,t,n){e.removeEventListener(t,n);var r=this.eventListeners.findIndex(function(r){return r.element===e&&r.event===t&&r.handler===n});r>-1&&this.eventListeners.splice(r,1)}},{key:"unbindEvents",value:function(){this.eventListeners.forEach(function(e){var t=e.element,n=e.event,r=e.handler;t.removeEventListener(n,r)}),this.eventListeners=[]}},{key:"emit",value:function(e,t){r.Bt.emit(e,t)}},{key:"on",value:function(e,t){r.Bt.on(e,t)}},{key:"off",value:function(e,t){r.Bt.off(e,t)}},{key:"getState",value:function(){return o.Gi.getState()}},{key:"setState",value:function(e){o.Gi.setState(e)}},{key:"$",value:function(e){return this.element?this.element.querySelector(e):null}},{key:"$$",value:function(e){return this.element?this.element.querySelectorAll(e):document.querySelectorAll("")}},{key:"setupStateListeners",value:function(){var e=this;this.stateUnsubscribe=o.Gi.subscribe(function(t,n,r){e.onStateChange(t,n,r)})}},{key:"isMounted",value:function(){return this.state.mounted}},{key:"isInitialized",value:function(){return this.state.initialized}},{key:"isDestroyed",value:function(){return this.state.destroyed}},{key:"getElement",value:function(){return this.element}}])&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}()}},e=>{e.O(0,[96,76],()=>{return t=7295,e(e.s=t);var t});e.O()}]);
//# sourceMappingURL=admin.d1d468e7.js.map